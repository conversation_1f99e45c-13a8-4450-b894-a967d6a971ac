#!/usr/bin/env python3
"""
Professional Order Book Widget - Clean Table Display
"""

import tkinter as tk
from tkinter import ttk
from typing import List, Tuple
from config import EXECUTIVE_COLORS, APP_CONFIG


class UnifiedOrderBookWidget(tk.Frame):
    """Professional order book widget with clean table display"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, bg=EXECUTIVE_COLORS['bg_card'], **kwargs)

        self.bid_data = []
        self.ask_data = []
        self.max_levels = APP_CONFIG['max_order_book_levels']

        self.create_order_book_table()
    
    def create_order_book_table(self):
        """Create professional order book table display"""

        # Main container
        main_frame = tk.Frame(self, bg=EXECUTIVE_COLORS['bg_card'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Title with status indicator
        title_frame = tk.Frame(main_frame, bg=EXECUTIVE_COLORS['bg_card'])
        title_frame.pack(fill='x', pady=(0, 10))

        title_label = tk.Label(
            title_frame,
            text="📊 ORDER BOOK DEPTH",
            font=('Segoe UI', 14, 'bold'),
            bg=EXECUTIVE_COLORS['bg_card'],
            fg=EXECUTIVE_COLORS['text_primary']
        )
        title_label.pack(side='left')

        # Status indicator
        self.status_label = tk.Label(
            title_frame,
            text="● LIVE",
            font=('Segoe UI', 10, 'bold'),
            bg=EXECUTIVE_COLORS['bg_card'],
            fg=EXECUTIVE_COLORS['success']
        )
        self.status_label.pack(side='right')

        # Create table container with scrollbar
        table_container = tk.Frame(main_frame, bg=EXECUTIVE_COLORS['bg_card'])
        table_container.pack(fill='both', expand=True)

        # Create treeview for order book data
        self.create_treeview(table_container)

        # Initialize with sample data
        self.update_display()

    def create_treeview(self, parent):
        """Create treeview widget for order book display"""

        # Create style for treeview
        style = ttk.Style()
        style.theme_use('clam')

        # Configure treeview colors
        style.configure("OrderBook.Treeview",
                       background=EXECUTIVE_COLORS['bg_secondary'],
                       foreground=EXECUTIVE_COLORS['text_primary'],
                       fieldbackground=EXECUTIVE_COLORS['bg_secondary'],
                       borderwidth=0,
                       font=('Consolas', 10))

        style.configure("OrderBook.Treeview.Heading",
                       background=EXECUTIVE_COLORS['bg_primary'],
                       foreground=EXECUTIVE_COLORS['text_primary'],
                       font=('Segoe UI', 11, 'bold'),
                       borderwidth=1,
                       relief='solid')

        # Create treeview with columns
        columns = ('bid_count', 'bid_amount', 'bid_total', 'bid_price',
                  'ask_price', 'ask_total', 'ask_amount', 'ask_count')

        self.tree = ttk.Treeview(parent, columns=columns, show='headings',
                                style="OrderBook.Treeview", height=20)

        # Configure column headings and widths
        headings = ['COUNT', 'AMOUNT', 'TOTAL', 'PRICE',
                   'PRICE', 'TOTAL', 'AMOUNT', 'COUNT']
        widths = [80, 100, 120, 100, 100, 120, 100, 80]

        for col, heading, width in zip(columns, headings, widths):
            self.tree.heading(col, text=heading)
            self.tree.column(col, width=width, anchor='center')

        # Create scrollbar
        scrollbar = ttk.Scrollbar(parent, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # Pack treeview and scrollbar
        self.tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

    def update_order_book(self, bids: List[Tuple], asks: List[Tuple]):
        """Update order book display with new data"""
        try:
            # Clear existing data
            for item in self.tree.get_children():
                self.tree.delete(item)

            # Calculate maximum rows to display
            max_rows = max(len(bids), len(asks), 10)

            # Calculate cumulative totals
            bid_cumulative = 0
            ask_cumulative = 0

            for i in range(max_rows):
                # Prepare bid data
                if i < len(bids):
                    bid_qty, bid_orders, bid_price = bids[i]
                    bid_cumulative += bid_qty
                    bid_count = str(bid_orders)
                    bid_amount = f"{bid_qty:,}"
                    bid_total = f"{bid_cumulative:,}"
                    bid_price_str = f"₹{bid_price:.2f}"
                else:
                    bid_count = ""
                    bid_amount = ""
                    bid_total = ""
                    bid_price_str = ""

                # Prepare ask data
                if i < len(asks):
                    ask_price, ask_orders, ask_qty = asks[i]
                    ask_cumulative += ask_qty
                    ask_count = str(ask_orders)
                    ask_amount = f"{ask_qty:,}"
                    ask_total = f"{ask_cumulative:,}"
                    ask_price_str = f"₹{ask_price:.2f}"
                else:
                    ask_count = ""
                    ask_amount = ""
                    ask_total = ""
                    ask_price_str = ""

                # Insert row into treeview
                item = self.tree.insert('', 'end', values=(
                    bid_count, bid_amount, bid_total, bid_price_str,
                    ask_price_str, ask_total, ask_amount, ask_count
                ))

                # Color coding for bid/ask sides
                if i < len(bids) or i < len(asks):
                    self.tree.set(item, 'bid_count', bid_count)
                    self.tree.set(item, 'bid_amount', bid_amount)
                    self.tree.set(item, 'bid_total', bid_total)
                    self.tree.set(item, 'bid_price', bid_price_str)
                    self.tree.set(item, 'ask_price', ask_price_str)
                    self.tree.set(item, 'ask_total', ask_total)
                    self.tree.set(item, 'ask_amount', ask_amount)
                    self.tree.set(item, 'ask_count', ask_count)

            # Update status
            self.status_label.config(text="● LIVE", fg=EXECUTIVE_COLORS['success'])

        except Exception as e:
            print(f"❌ Error updating order book: {e}")
            self.status_label.config(text="● ERROR", fg=EXECUTIVE_COLORS['danger'])

    def update_display(self):
        """Initialize display with sample data"""
        # Sample bid data (quantity, orders, price)
        sample_bids = [
            (1520, 3, 872.0),
            (850, 2, 871.8),
            (2371, 4, 871.6),
            (2871, 5, 871.4),
            (3327, 6, 871.2),
            (6327, 8, 871.0),
            (14540, 12, 870.8),
            (17540, 15, 870.6),
            (19860, 18, 870.4),
            (21250, 20, 870.2)
        ]

        # Sample ask data (price, orders, quantity)
        sample_asks = [
            (872.2, 2, 240),
            (872.4, 3, 390),
            (872.6, 1, 1150),
            (872.8, 4, 281),
            (873.0, 2, 371),
            (873.2, 3, 516),
            (873.4, 1, 700),
            (873.6, 2, 1067),
            (873.8, 1, 50),
            (874.0, 3, 100)
        ]

        self.update_order_book(sample_bids, sample_asks)
