"""
Order Book State Management
"""

from collections import deque
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Callable
import threading
import time

from .data_structures import (
    OrderBookLevel, OrderBookSnapshot, OrderBookEvent, 
    OrderBookEventType, MarketData
)
from config.settings import SYSTEM_CONFIG


class OrderBookStateManager:
    """Efficient order book state management with change tracking"""
    
    def __init__(self, max_levels: int = 20):
        self.max_levels = max_levels
        self.bids: List[OrderBookLevel] = []  # Sorted highest to lowest
        self.asks: List[OrderBookLevel] = []  # Sorted lowest to highest
        
        # Fast lookup dictionaries for price levels
        self.bid_levels: Dict[float, OrderBookLevel] = {}
        self.ask_levels: Dict[float, OrderBookLevel] = {}
        
        # Change tracking
        self.last_update_time = datetime.now()
        self.update_count = 0
        self.changes_since_last_snapshot = []
        
        # Thread safety
        self._lock = threading.RLock()
    
    def update_bids(self, new_bids: List[Tuple[int, int, float]]) -> List[str]:
        """Update bid levels and return list of changes"""
        with self._lock:
            changes = []
            new_bid_levels = {}
            
            # Process new bid data
            for qty, orders, price in new_bids:
                if qty > 0:  # Valid level
                    level = OrderBookLevel(
                        price=price,
                        quantity=qty,
                        orders=orders,
                        timestamp=datetime.now()
                    )
                    new_bid_levels[price] = level
            
            # Detect changes
            old_prices = set(self.bid_levels.keys())
            new_prices = set(new_bid_levels.keys())
            
            # Removed levels
            for price in old_prices - new_prices:
                changes.append(f"BID_REMOVED:{price}")
            
            # Added levels
            for price in new_prices - old_prices:
                changes.append(f"BID_ADDED:{price}")
            
            # Updated levels
            for price in old_prices & new_prices:
                old_level = self.bid_levels[price]
                new_level = new_bid_levels[price]
                if (old_level.quantity != new_level.quantity or 
                    old_level.orders != new_level.orders):
                    changes.append(f"BID_UPDATED:{price}")
            
            # Update state
            self.bid_levels = new_bid_levels
            self.bids = sorted(new_bid_levels.values(), 
                             key=lambda x: x.price, reverse=True)[:self.max_levels]
            
            self.changes_since_last_snapshot.extend(changes)
            self.last_update_time = datetime.now()
            self.update_count += 1
            
            return changes
    
    def update_asks(self, new_asks: List[Tuple[float, int, int]]) -> List[str]:
        """Update ask levels and return list of changes"""
        with self._lock:
            changes = []
            new_ask_levels = {}
            
            # Process new ask data
            for price, orders, qty in new_asks:
                if qty > 0:  # Valid level
                    level = OrderBookLevel(
                        price=price,
                        quantity=qty,
                        orders=orders,
                        timestamp=datetime.now()
                    )
                    new_ask_levels[price] = level
            
            # Detect changes (similar to bids)
            old_prices = set(self.ask_levels.keys())
            new_prices = set(new_ask_levels.keys())
            
            for price in old_prices - new_prices:
                changes.append(f"ASK_REMOVED:{price}")
            
            for price in new_prices - old_prices:
                changes.append(f"ASK_ADDED:{price}")
            
            for price in old_prices & new_prices:
                old_level = self.ask_levels[price]
                new_level = new_ask_levels[price]
                if (old_level.quantity != new_level.quantity or 
                    old_level.orders != new_level.orders):
                    changes.append(f"ASK_UPDATED:{price}")
            
            # Update state
            self.ask_levels = new_ask_levels
            self.asks = sorted(new_ask_levels.values(), 
                             key=lambda x: x.price)[:self.max_levels]
            
            self.changes_since_last_snapshot.extend(changes)
            self.last_update_time = datetime.now()
            self.update_count += 1
            
            return changes
    
    def get_snapshot(self, symbol: str, market_data: Optional[MarketData] = None) -> OrderBookSnapshot:
        """Get current order book snapshot"""
        with self._lock:
            snapshot = OrderBookSnapshot(
                symbol=symbol,
                timestamp=self.last_update_time,
                bids=self.bids.copy(),
                asks=self.asks.copy(),
                market_data=market_data
            )
            
            # Clear changes after snapshot
            self.changes_since_last_snapshot.clear()
            
            return snapshot
    
    def get_changes_since_last_snapshot(self) -> List[str]:
        """Get changes since last snapshot"""
        with self._lock:
            return self.changes_since_last_snapshot.copy()
    
    @property
    def best_bid_price(self) -> float:
        """Get best bid price"""
        return self.bids[0].price if self.bids else 0.0
    
    @property
    def best_ask_price(self) -> float:
        """Get best ask price"""
        return self.asks[0].price if self.asks else 0.0
    
    @property
    def spread(self) -> float:
        """Get current spread"""
        if self.bids and self.asks:
            return self.asks[0].price - self.bids[0].price
        return 0.0
    
    @property
    def total_bid_quantity(self) -> int:
        """Get total bid quantity"""
        return sum(level.quantity for level in self.bids)
    
    @property
    def total_ask_quantity(self) -> int:
        """Get total ask quantity"""
        return sum(level.quantity for level in self.asks)
    
    @property
    def imbalance(self) -> float:
        """Calculate order book imbalance percentage"""
        total = self.total_bid_quantity + self.total_ask_quantity
        if total == 0:
            return 0.0
        return ((self.total_bid_quantity - self.total_ask_quantity) / total) * 100


class OrderBookEventSystem:
    """Event-driven architecture for order book updates and notifications"""
    
    def __init__(self):
        # Event subscribers
        self.subscribers: Dict[OrderBookEventType, List[Callable]] = {}
        self.global_subscribers: List[Callable] = []  # Subscribe to all events
        
        # Event queue and processing
        self.event_queue = deque(maxlen=1000)
        self.event_history = deque(maxlen=100)
        
        # Performance tracking
        self.events_processed = 0
        self.last_event_time = None
        
        # Thread safety
        self._lock = threading.RLock()
    
    def subscribe(self, event_type: OrderBookEventType, callback: Callable):
        """Subscribe to specific event type"""
        with self._lock:
            if event_type not in self.subscribers:
                self.subscribers[event_type] = []
            self.subscribers[event_type].append(callback)
    
    def subscribe_all(self, callback: Callable):
        """Subscribe to all events"""
        with self._lock:
            self.global_subscribers.append(callback)
    
    def emit_event(self, event_type: OrderBookEventType, symbol: str, 
                   data: Dict, priority: int = 1):
        """Emit an event"""
        with self._lock:
            event = OrderBookEvent(
                event_type=event_type,
                symbol=symbol,
                timestamp=datetime.now(),
                data=data,
                priority=priority
            )
            
            self.event_queue.append(event)
            self.last_event_time = event.timestamp
            
            # Process event immediately for high priority
            if priority >= 3:
                self._process_event(event)
    
    def _process_event(self, event: OrderBookEvent):
        """Process a single event"""
        try:
            # Call specific subscribers
            if event.event_type in self.subscribers:
                for callback in self.subscribers[event.event_type]:
                    try:
                        callback(event)
                    except Exception as e:
                        print(f"Error in event callback: {e}")
            
            # Call global subscribers
            for callback in self.global_subscribers:
                try:
                    callback(event)
                except Exception as e:
                    print(f"Error in global event callback: {e}")
            
            # Update tracking
            self.events_processed += 1
            self.event_history.append(event)
            
        except Exception as e:
            print(f"Error processing event: {e}")
    
    def process_events(self):
        """Process all queued events"""
        with self._lock:
            while self.event_queue:
                event = self.event_queue.popleft()
                self._process_event(event)
    
    def get_recent_events(self, count: int = 10) -> List[OrderBookEvent]:
        """Get recent events"""
        with self._lock:
            return list(self.event_history)[-count:]
    
    def get_event_stats(self) -> Dict:
        """Get event processing statistics"""
        with self._lock:
            return {
                'events_processed': self.events_processed,
                'queue_size': len(self.event_queue),
                'last_event_time': self.last_event_time,
                'subscribers_count': sum(len(subs) for subs in self.subscribers.values()),
                'global_subscribers_count': len(self.global_subscribers)
            }
