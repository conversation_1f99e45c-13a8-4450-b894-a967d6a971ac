"""
Market Data Simulator for CGCL
Generates realistic order book data when market is closed
"""

import random
import threading
import time
from datetime import datetime
from typing import Callable, Optional, List
import math

from core.data_structures import OrderBookLevel, OrderBookSnapshot, MarketData


class CGCLMarketSimulator:
    """Simulates realistic CGCL market data with order book depth"""
    
    def __init__(self):
        self.symbol = "CGCL"
        self.base_price = 850.0  # Typical CGCL price range
        self.current_price = self.base_price
        self.tick_size = 0.05
        
        # Market microstructure parameters
        self.min_quantity = 100
        self.max_quantity = 50000
        self.min_orders = 1
        self.max_orders = 25
        
        # Price movement parameters
        self.volatility = 0.015  # 1.5% volatility
        self.trend_strength = 0.0
        self.mean_reversion = 0.1
        
        # Simulation state
        self.running = False
        self.simulation_thread = None
        self._lock = threading.RLock()
        
        # Callbacks
        self.on_order_book_update: Optional[Callable] = None
        self.on_market_data_update: Optional[Callable] = None
        
        # Historical data for realistic patterns
        self.price_history = []
        self.volume_history = []
        
        print(f"📊 CGCL Market Simulator initialized - Base Price: ₹{self.base_price}")
    
    def start_simulation(self):
        """Start the market data simulation"""
        if self.running:
            print("⚠️ Simulation already running")
            return
            
        self.running = True
        self.simulation_thread = threading.Thread(target=self._simulation_loop, daemon=True)
        self.simulation_thread.start()
        print("🚀 CGCL Market Simulation started - Updates every second")
    
    def stop_simulation(self):
        """Stop the market data simulation"""
        self.running = False
        if self.simulation_thread:
            self.simulation_thread.join(timeout=2)
        print("⏹️ CGCL Market Simulation stopped")
    
    def _simulation_loop(self):
        """Main simulation loop - runs every second"""
        while self.running:
            try:
                # Generate new price movement
                self._update_price()
                
                # Generate order book snapshot
                order_book = self._generate_order_book()
                
                # Generate market data
                market_data = self._generate_market_data()
                
                # Call callbacks
                if self.on_order_book_update:
                    self.on_order_book_update("CGCL", order_book)
                
                if self.on_market_data_update:
                    self.on_market_data_update("CGCL", market_data)
                
                # Update history
                self._update_history()
                
                # Wait for next update
                time.sleep(1.0)
                
            except Exception as e:
                print(f"❌ Simulation error: {e}")
                time.sleep(1.0)
    
    def _update_price(self):
        """Update current price with realistic movement"""
        with self._lock:
            # Random walk with mean reversion
            random_change = random.gauss(0, self.volatility)
            trend_component = self.trend_strength * 0.001
            
            # Mean reversion towards base price
            reversion = (self.base_price - self.current_price) * self.mean_reversion * 0.001
            
            # Calculate price change
            price_change = (random_change + trend_component + reversion) * self.current_price
            
            # Apply change with tick size constraint
            new_price = self.current_price + price_change
            self.current_price = round(new_price / self.tick_size) * self.tick_size
            
            # Keep price within reasonable bounds
            min_price = self.base_price * 0.95
            max_price = self.base_price * 1.05
            self.current_price = max(min_price, min(max_price, self.current_price))
    
    def _generate_order_book(self) -> OrderBookSnapshot:
        """Generate realistic order book with 10 levels each side"""
        timestamp = datetime.now()
        
        # Generate bids (descending price order)
        bids = []
        for i in range(10):
            price = self.current_price - (i + 1) * self.tick_size
            quantity = self._generate_realistic_quantity(i)
            orders = self._generate_realistic_orders(quantity)
            
            bid_level = OrderBookLevel(
                price=price,
                quantity=quantity,
                orders=orders,
                timestamp=timestamp
            )
            bids.append(bid_level)
        
        # Generate asks (ascending price order)
        asks = []
        for i in range(10):
            price = self.current_price + (i + 1) * self.tick_size
            quantity = self._generate_realistic_quantity(i)
            orders = self._generate_realistic_orders(quantity)
            
            ask_level = OrderBookLevel(
                price=price,
                quantity=quantity,
                orders=orders,
                timestamp=timestamp
            )
            asks.append(ask_level)
        
        # Create market data
        market_data = MarketData(
            symbol="CGCL",
            timestamp=timestamp,
            ltp=self.current_price,
            open_price=self.base_price + random.uniform(-1.0, 1.0),
            high_price=self.current_price + random.uniform(0, 2.0),
            low_price=self.current_price - random.uniform(0, 2.0),
            close_price=self.current_price,
            volume=random.randint(1000, 10000),
            prev_close=self.base_price
        )
        
        return OrderBookSnapshot(
            symbol="CGCL",
            timestamp=timestamp,
            bids=bids,
            asks=asks,
            market_data=market_data
        )
    
    def _generate_realistic_quantity(self, level: int) -> int:
        """Generate realistic quantity based on level depth"""
        # Closer to best price = higher quantities typically
        base_qty = random.randint(self.min_quantity, self.max_quantity)
        
        # Reduce quantity as we go deeper
        level_factor = 1.0 - (level * 0.1)
        level_factor = max(0.3, level_factor)  # Minimum 30% of base
        
        # Add some randomness
        randomness = random.uniform(0.7, 1.3)
        
        final_qty = int(base_qty * level_factor * randomness)
        return max(self.min_quantity, final_qty)
    
    def _generate_realistic_orders(self, quantity: int) -> int:
        """Generate realistic order count based on quantity"""
        # Typical order size ranges
        avg_order_size = random.randint(500, 2000)
        orders = max(1, quantity // avg_order_size)
        
        # Add some variation
        variation = random.randint(-2, 3)
        orders = max(1, orders + variation)
        
        return min(orders, self.max_orders)
    
    def _generate_market_data(self) -> MarketData:
        """Generate current market data"""
        return MarketData(
            symbol="CGCL",
            timestamp=datetime.now(),
            ltp=self.current_price,
            open_price=self.base_price + random.uniform(-0.5, 0.5),
            high_price=self.current_price + random.uniform(0, 1.5),
            low_price=self.current_price - random.uniform(0, 1.5),
            close_price=self.current_price,
            volume=random.randint(5000, 25000),
            prev_close=self.base_price
        )
    
    def _update_history(self):
        """Update price and volume history"""
        with self._lock:
            self.price_history.append(self.current_price)
            self.volume_history.append(random.randint(1000, 10000))
            
            # Keep only last 5 minutes of data (300 seconds)
            if len(self.price_history) > 300:
                self.price_history = self.price_history[-300:]
                self.volume_history = self.volume_history[-300:]
    
    def set_trend(self, strength: float):
        """Set trend strength (-1.0 to 1.0)"""
        self.trend_strength = max(-1.0, min(1.0, strength))
        print(f"📈 Trend set to: {self.trend_strength:.2f}")
    
    def create_volume_spike(self, multiplier: float = 3.0):
        """Create a temporary volume spike"""
        original_max = self.max_quantity
        self.max_quantity = int(self.max_quantity * multiplier)
        
        # Reset after 10 seconds
        def reset_volume():
            time.sleep(10)
            self.max_quantity = original_max
        
        threading.Thread(target=reset_volume, daemon=True).start()
        print(f"💥 Volume spike created (x{multiplier:.1f})")
    
    def get_current_price(self) -> float:
        """Get current simulated price"""
        return self.current_price
    
    def get_statistics(self) -> dict:
        """Get simulation statistics"""
        with self._lock:
            return {
                'current_price': self.current_price,
                'base_price': self.base_price,
                'price_change': self.current_price - self.base_price,
                'price_change_percent': ((self.current_price - self.base_price) / self.base_price) * 100,
                'trend_strength': self.trend_strength,
                'volatility': self.volatility,
                'running': self.running,
                'history_length': len(self.price_history)
            }
