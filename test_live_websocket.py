"""
Live WebSocket Test with Real Smart API Credentials
==================================================

Test WebSocket connection and order flow engine with your authenticated credentials.
"""

import asyncio
import json
import websocket
import threading
import time
from datetime import datetime
import struct
import logging

from order_flow_engine import Order<PERSON>lowEngine, AdvancedOrderFlowAnalyzer, Tick, OrderBook, OrderBookLevel
from order_flow_monitor import OrderFlowMonitor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LiveSmartAPIWebSocket:
    """Live WebSocket connection with Smart API using real credentials"""
    
    def __init__(self, credentials_file="smart_api_credentials.json"):
        # Load credentials
        try:
            with open(credentials_file, 'r') as f:
                self.credentials = json.load(f)
        except FileNotFoundError:
            logger.error("Credentials file not found. Run test_smart_api_auth.py first.")
            raise
        
        # WebSocket connection
        self.ws = None
        self.is_connected = False
        self.connection_start_time = None
        
        # Order flow components
        self.symbols = ["RELIANCE", "TCS", "INFY"]  # Test symbols
        self.order_flow_monitor = OrderFlowMonitor(self.symbols)
        self.analyzers = {symbol: AdvancedOrderFlowAnalyzer(symbol) for symbol in self.symbols}
        
        # Data tracking
        self.tick_count = 0
        self.signal_count = 0
        self.last_heartbeat = time.time()
        
        # Symbol token mapping (will be populated)
        self.symbol_tokens = {
            "RELIANCE": "2885",  # Example token
            "TCS": "11536",      # Example token  
            "INFY": "1594"       # Example token
        }
    
    def connect(self):
        """Connect to Smart API WebSocket"""
        logger.info("🔌 Connecting to Smart API WebSocket...")
        
        try:
            ws_url = "wss://smartapisocket.angelone.in/smart-stream"
            
            # Create WebSocket with proper headers
            headers = {
                "Authorization": f"Bearer {self.credentials['auth_token']}",
                "x-api-key": self.credentials['api_key'],
                "x-client-code": self.credentials['client_code'],
                "x-feed-token": self.credentials['feed_token']
            }
            
            self.connection_start_time = time.time()
            
            self.ws = websocket.WebSocketApp(
                ws_url,
                header=headers,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )
            
            # Start connection in thread
            connection_thread = threading.Thread(target=self.ws.run_forever)
            connection_thread.daemon = True
            connection_thread.start()
            
            return True
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    def _on_open(self, ws):
        """WebSocket opened"""
        connection_time = time.time() - self.connection_start_time
        logger.info(f"✅ WebSocket connected in {connection_time:.2f}s")
        self.is_connected = True
        self.last_heartbeat = time.time()
        
        # Send authentication message
        auth_message = {
            "a": "auth",
            "user": self.credentials['client_code'],
            "token": self.credentials['feed_token']
        }
        
        try:
            ws.send(json.dumps(auth_message))
            logger.info("🔐 Authentication message sent")
            
            # Wait a moment then subscribe
            time.sleep(1)
            self._subscribe_symbols()
            
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
    
    def _on_message(self, ws, message):
        """Process WebSocket message"""
        try:
            self.last_heartbeat = time.time()
            
            # Handle different message types
            if isinstance(message, str):
                # JSON message
                try:
                    data = json.loads(message)
                    self._process_json_message(data)
                except json.JSONDecodeError:
                    logger.debug(f"Non-JSON message: {message}")
            else:
                # Binary message (tick data)
                self._process_binary_message(message)
                
        except Exception as e:
            logger.error(f"Message processing error: {e}")
    
    def _on_error(self, ws, error):
        """WebSocket error"""
        logger.error(f"❌ WebSocket error: {error}")
    
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket closed"""
        logger.warning(f"🔌 WebSocket closed: {close_status_code} - {close_msg}")
        self.is_connected = False
    
    def _subscribe_symbols(self):
        """Subscribe to symbol data"""
        logger.info("📡 Subscribing to symbols...")
        
        try:
            # Subscribe to LTP mode for each symbol
            for symbol in self.symbols:
                token = self.symbol_tokens.get(symbol, "1")  # Default token
                
                # LTP subscription
                ltp_sub = {
                    "a": "subscribe",
                    "v": [[1, token]]  # Mode 1 = LTP
                }
                self.ws.send(json.dumps(ltp_sub))
                
                # Depth subscription  
                depth_sub = {
                    "a": "subscribe",
                    "v": [[4, token]]  # Mode 4 = Depth
                }
                self.ws.send(json.dumps(depth_sub))
                
                logger.info(f"📊 Subscribed to {symbol} (token: {token})")
                
        except Exception as e:
            logger.error(f"Subscription failed: {e}")
    
    def _process_json_message(self, data):
        """Process JSON message"""
        message_type = data.get('t', '')
        
        if message_type == 'ck':
            logger.info("✅ Connection acknowledged")
        elif message_type == 'tk':
            self._process_tick_json(data)
        elif message_type == 'dp':
            self._process_depth_json(data)
        else:
            logger.debug(f"Unknown JSON message: {data}")
    
    def _process_binary_message(self, data):
        """Process binary tick data"""
        try:
            if len(data) < 8:
                return
            
            # Simple binary parsing (Smart API specific format would be more complex)
            # This is a basic example
            self.tick_count += 1
            
            # Extract basic info (simplified)
            price = struct.unpack('>f', data[4:8])[0] if len(data) >= 8 else 0
            volume = struct.unpack('>I', data[8:12])[0] if len(data) >= 12 else 0
            
            if price > 0:
                # Create tick for first symbol (simplified)
                symbol = self.symbols[0]
                tick = Tick(
                    timestamp=datetime.now(),
                    price=price,
                    volume=volume,
                    buyer_initiated=True  # Simplified
                )
                
                # Process through order flow engine
                signal = self.analyzers[symbol].flow_engine.add_tick(tick)
                
                if signal:
                    self.signal_count += 1
                    logger.info(f"🚨 SIGNAL #{self.signal_count}: {symbol} - {signal.signal_type} "
                              f"(Strength: {signal.strength:.2f})")
                
                logger.info(f"📈 Binary Tick #{self.tick_count}: {symbol} ₹{price:.2f} (Vol: {volume:,})")
                
        except Exception as e:
            logger.error(f"Binary processing error: {e}")
    
    def _process_tick_json(self, data):
        """Process JSON tick data"""
        try:
            symbol_token = data.get('tk', '')
            price = float(data.get('ltp', 0))
            volume = int(data.get('v', 0))
            
            # Find symbol by token (simplified mapping)
            symbol = "UNKNOWN"
            for sym, token in self.symbol_tokens.items():
                if token == symbol_token:
                    symbol = sym
                    break
            
            if price > 0 and volume > 0:
                self.tick_count += 1
                
                tick = Tick(
                    timestamp=datetime.now(),
                    price=price,
                    volume=volume,
                    buyer_initiated=True  # Simplified
                )
                
                # Process through order flow engine
                if symbol in self.analyzers:
                    signal = self.analyzers[symbol].flow_engine.add_tick(tick)
                    
                    if signal:
                        self.signal_count += 1
                        logger.info(f"🚨 SIGNAL #{self.signal_count}: {symbol} - {signal.signal_type} "
                                  f"(Strength: {signal.strength:.2f}, Confidence: {signal.confidence:.2f})")
                        
                        # Print signal details
                        print(f"\n{'='*50}")
                        print(f"📊 ORDER FLOW SIGNAL - {symbol}")
                        print(f"{'='*50}")
                        print(f"Time: {tick.timestamp.strftime('%H:%M:%S')}")
                        print(f"Signal: {signal.signal_type} (Strength: {signal.strength:.2f})")
                        print(f"Confidence: {signal.confidence:.2f}")
                        print(f"Price: ₹{price:.2f}")
                        print(f"Volume: {volume:,}")
                        print(f"Reasons: {', '.join(signal.reasons)}")
                        print(f"{'='*50}\n")
                
                if self.tick_count % 10 == 0:  # Log every 10th tick
                    logger.info(f"📊 Tick #{self.tick_count}: {symbol} ₹{price:.2f} (Vol: {volume:,})")
                
        except Exception as e:
            logger.error(f"JSON tick processing error: {e}")
    
    def _process_depth_json(self, data):
        """Process JSON depth data"""
        try:
            symbol_token = data.get('tk', '')
            
            # Extract bid/ask data
            bids = []
            asks = []
            
            for i in range(5):
                bid_price = data.get(f'bp{i+1}', 0)
                bid_qty = data.get(f'bq{i+1}', 0)
                if bid_price > 0:
                    bids.append(OrderBookLevel(
                        price=float(bid_price),
                        quantity=int(bid_qty),
                        orders=1
                    ))
                
                ask_price = data.get(f'sp{i+1}', 0)
                ask_qty = data.get(f'sq{i+1}', 0)
                if ask_price > 0:
                    asks.append(OrderBookLevel(
                        price=float(ask_price),
                        quantity=int(ask_qty),
                        orders=1
                    ))
            
            if bids and asks:
                # Find symbol
                symbol = "UNKNOWN"
                for sym, token in self.symbol_tokens.items():
                    if token == symbol_token:
                        symbol = sym
                        break
                
                order_book = OrderBook(
                    timestamp=datetime.now(),
                    bids=bids,
                    asks=asks
                )
                
                # Process through order flow engine
                if symbol in self.analyzers:
                    self.analyzers[symbol].flow_engine.add_order_book(order_book)
                
                logger.debug(f"📚 Order Book: {symbol} (Bid: ₹{bids[0].price}, Ask: ₹{asks[0].price})")
                
        except Exception as e:
            logger.error(f"Depth processing error: {e}")
    
    def get_status(self):
        """Get connection status"""
        return {
            'connected': self.is_connected,
            'ticks_processed': self.tick_count,
            'signals_generated': self.signal_count,
            'uptime_seconds': time.time() - self.connection_start_time if self.connection_start_time else 0,
            'last_heartbeat': self.last_heartbeat
        }


async def main():
    """Main test function"""
    print("🚀 LIVE SMART API WEBSOCKET & ORDER FLOW TEST")
    print("=" * 60)
    
    try:
        # Create WebSocket connection
        ws_client = LiveSmartAPIWebSocket()
        
        # Connect
        if not ws_client.connect():
            print("❌ Connection failed")
            return
        
        # Wait for connection
        print("⏳ Waiting for connection...")
        timeout = 30
        start_time = time.time()
        
        while not ws_client.is_connected and (time.time() - start_time) < timeout:
            time.sleep(0.5)
        
        if not ws_client.is_connected:
            print("❌ Connection timeout")
            return
        
        print("✅ Connected! Monitoring order flow...")
        print("📊 Watching for signals (Press Ctrl+C to stop)")
        
        # Monitor for 2 minutes
        test_duration = 120  # 2 minutes
        start_time = time.time()
        
        while (time.time() - start_time) < test_duration:
            await asyncio.sleep(10)  # Check every 10 seconds
            
            status = ws_client.get_status()
            elapsed = int(time.time() - start_time)
            
            print(f"\n⏱️  Status ({elapsed}s): "
                  f"Ticks: {status['ticks_processed']}, "
                  f"Signals: {status['signals_generated']}, "
                  f"Connected: {status['connected']}")
        
        # Final results
        final_status = ws_client.get_status()
        
        print("\n" + "=" * 60)
        print("📊 LIVE TEST RESULTS")
        print("=" * 60)
        print(f"✅ Connection: {'SUCCESS' if final_status['connected'] else 'FAILED'}")
        print(f"📈 Ticks Processed: {final_status['ticks_processed']}")
        print(f"🚨 Signals Generated: {final_status['signals_generated']}")
        print(f"⏱️  Test Duration: {test_duration} seconds")
        
        if final_status['signals_generated'] > 0:
            print("🎯 Order flow engine is working with live data!")
        else:
            print("⚠️  No signals generated - may need more market activity")
        
        # Close connection
        if ws_client.ws:
            ws_client.ws.close()
        
    except KeyboardInterrupt:
        print("\n⏹️  Test stopped by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
