#!/usr/bin/env python3
"""
Quick Launcher for CGCL Professional Trading Dashboard
Executive-Level Interface
"""

import sys
import os
from pathlib import Path

def main():
    """Launch the professional dashboard"""
    print("🚀 LAUNCHING CGCL PROFESSIONAL DASHBOARD")
    print("=" * 50)
    
    try:
        # Import and run the professional dashboard
        from professional_cgcl_dashboard import main as dashboard_main
        dashboard_main()
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n🔧 Trying alternative import...")
        
        # Try direct execution
        try:
            exec(open('professional_cgcl_dashboard.py').read())
        except Exception as e:
            print(f"❌ Execution Error: {e}")
            print("\n📋 Manual Launch Instructions:")
            print("1. Open terminal/command prompt")
            print("2. Navigate to the project directory")
            print("3. Run: python professional_cgcl_dashboard.py")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
