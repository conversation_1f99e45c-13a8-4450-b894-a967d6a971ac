"""
Demo: Angel One Style Market Depth
=================================

Simplified demo showing the Angel One style market depth interface
with enhanced coloring and professional layout.
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import random
from datetime import datetime

class AngelStyleMarketDepthDemo:
    """Demo of Angel One style market depth interface"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Angel One Style Market Depth - Demo")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1a1a1a')
        
        # Colors matching Angel One theme
        self.colors = {
            'bg_dark': '#1a1a1a',
            'bg_medium': '#2d2d2d', 
            'bg_light': '#3d3d3d',
            'text_white': '#ffffff',
            'text_gray': '#cccccc',
            'bid_green': '#00ff88',
            'bid_green_light': '#66ffaa',
            'ask_red': '#ff4444',
            'ask_red_light': '#ff6666',
            'spread_yellow': '#ffdd44',
            'volume_blue': '#4488ff'
        }
        
        # Sample data
        self.symbol = "RELIANCE"
        self.base_price = 2500.0
        self.current_price = self.base_price
        
        # Create GUI
        self.create_widgets()
        
        # Start data simulation
        self.start_simulation()
    
    def create_widgets(self):
        """Create the main widgets"""
        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_dark'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_frame = tk.Frame(main_frame, bg=self.colors['bg_medium'], height=60)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        title_frame.pack_propagate(False)
        
        tk.Label(
            title_frame,
            text="📊 RELIANCE - Market Depth (Angel One Style)",
            font=("Arial", 16, "bold"),
            fg=self.colors['text_white'],
            bg=self.colors['bg_medium']
        ).pack(side=tk.LEFT, padx=20, pady=15)
        
        # Status
        self.status_label = tk.Label(
            title_frame,
            text="🟢 Live Data",
            font=("Arial", 12, "bold"),
            fg=self.colors['bid_green'],
            bg=self.colors['bg_medium']
        )
        self.status_label.pack(side=tk.RIGHT, padx=20, pady=15)
        
        # Price info
        price_frame = tk.Frame(main_frame, bg=self.colors['bg_medium'], height=80)
        price_frame.pack(fill=tk.X, pady=(0, 10))
        price_frame.pack_propagate(False)
        
        # Current price
        self.price_label = tk.Label(
            price_frame,
            text=f"₹{self.current_price:.2f}",
            font=("Arial", 24, "bold"),
            fg=self.colors['text_white'],
            bg=self.colors['bg_medium']
        )
        self.price_label.pack(side=tk.LEFT, padx=20, pady=20)
        
        # Change
        self.change_label = tk.Label(
            price_frame,
            text="0.00 (0.00%)",
            font=("Arial", 14),
            fg=self.colors['text_gray'],
            bg=self.colors['bg_medium']
        )
        self.change_label.pack(side=tk.LEFT, padx=10, pady=20)
        
        # Market depth table
        self.create_depth_table(main_frame)
        
        # Analysis section
        self.create_analysis_section(main_frame)
    
    def create_depth_table(self, parent):
        """Create the market depth table"""
        # Table container
        table_frame = tk.Frame(parent, bg=self.colors['bg_light'])
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Header
        header_frame = tk.Frame(table_frame, bg=self.colors['bg_medium'], height=40)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # Header labels
        headers = [
            ("BID QTY", self.colors['bid_green']),
            ("BID PRICE", self.colors['bid_green']),
            ("", self.colors['text_white']),
            ("ASK PRICE", self.colors['ask_red']),
            ("ASK QTY", self.colors['ask_red'])
        ]
        
        for i, (header, color) in enumerate(headers):
            tk.Label(
                header_frame,
                text=header,
                font=("Arial", 11, "bold"),
                fg=color,
                bg=self.colors['bg_medium']
            ).grid(row=0, column=i, padx=10, pady=10, sticky=tk.EW)
        
        # Configure grid weights
        for i in range(5):
            header_frame.grid_columnconfigure(i, weight=1)
        
        # Scrollable content
        self.content_frame = tk.Frame(table_frame, bg=self.colors['bg_light'])
        self.content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Store reference for updates
        self.depth_rows = []
    
    def create_analysis_section(self, parent):
        """Create analysis section"""
        analysis_frame = tk.Frame(parent, bg=self.colors['bg_medium'], height=100)
        analysis_frame.pack(fill=tk.X)
        analysis_frame.pack_propagate(False)
        
        tk.Label(
            analysis_frame,
            text="📈 Real-Time Analysis",
            font=("Arial", 12, "bold"),
            fg=self.colors['text_white'],
            bg=self.colors['bg_medium']
        ).pack(pady=(10, 5))
        
        self.analysis_label = tk.Label(
            analysis_frame,
            text="Analyzing market depth...",
            font=("Arial", 10),
            fg=self.colors['text_gray'],
            bg=self.colors['bg_medium'],
            wraplength=1000
        )
        self.analysis_label.pack(pady=(0, 10))
    
    def update_market_depth(self):
        """Update market depth display"""
        # Clear existing rows
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # Generate realistic market depth data
        bids = []
        asks = []
        
        for i in range(10):
            # Bids (buy orders)
            bid_price = self.current_price - (i + 1) * 0.05
            bid_qty = random.randint(1000, 50000)
            bids.append((bid_price, bid_qty))
            
            # Asks (sell orders)
            ask_price = self.current_price + (i + 1) * 0.05
            ask_qty = random.randint(1000, 50000)
            asks.append((ask_price, ask_qty))
        
        # Calculate total volumes for volume bars
        total_bid_vol = sum(qty for _, qty in bids)
        total_ask_vol = sum(qty for _, qty in asks)
        max_vol = max(total_bid_vol, total_ask_vol)
        
        # Display asks (top to bottom, highest price first)
        for i, (price, qty) in enumerate(reversed(asks[:5])):
            self.create_depth_row(i, None, None, price, qty, 'ask', qty/max_vol)
        
        # Spread row
        if bids and asks:
            spread = asks[0][0] - bids[0][0]
            spread_pct = (spread / bids[0][0]) * 100
            self.create_spread_row(5, spread, spread_pct)
        
        # Display bids (top to bottom, highest price first)
        for i, (price, qty) in enumerate(bids[:5]):
            self.create_depth_row(6 + i, qty, price, None, None, 'bid', qty/max_vol)
    
    def create_depth_row(self, row, bid_qty, bid_price, ask_price, ask_qty, side, volume_pct):
        """Create a single depth row with volume visualization"""
        row_frame = tk.Frame(self.content_frame, bg=self.colors['bg_light'], height=30)
        row_frame.pack(fill=tk.X, pady=1)
        row_frame.pack_propagate(False)
        
        # Volume bar background
        if volume_pct > 0:
            bar_color = self.colors['bid_green_light'] if side == 'bid' else self.colors['ask_red_light']
            bar_width = max(int(volume_pct * 300), 10)  # Minimum 10px width
            
            if side == 'bid':
                # Bid volume bar (left side)
                bar_canvas = tk.Canvas(row_frame, bg=self.colors['bg_light'], height=30, highlightthickness=0)
                bar_canvas.pack(fill=tk.BOTH, expand=True)
                bar_canvas.create_rectangle(0, 5, bar_width, 25, fill=bar_color, outline="")
            else:
                # Ask volume bar (right side)
                bar_canvas = tk.Canvas(row_frame, bg=self.colors['bg_light'], height=30, highlightthickness=0)
                bar_canvas.pack(fill=tk.BOTH, expand=True)
                canvas_width = 1200  # Approximate canvas width
                bar_canvas.create_rectangle(canvas_width - bar_width, 5, canvas_width, 25, fill=bar_color, outline="")
        
        # Data labels frame
        data_frame = tk.Frame(row_frame, bg=self.colors['bg_light'])
        data_frame.place(x=0, y=0, relwidth=1, relheight=1)
        
        # Configure grid
        for i in range(5):
            data_frame.grid_columnconfigure(i, weight=1)
        
        # Data labels
        labels = [
            (f"{bid_qty:,}" if bid_qty else "", 0, self.colors['bid_green']),
            (f"₹{bid_price:.2f}" if bid_price else "", 1, self.colors['bid_green']),
            ("", 2, self.colors['text_white']),
            (f"₹{ask_price:.2f}" if ask_price else "", 3, self.colors['ask_red']),
            (f"{ask_qty:,}" if ask_qty else "", 4, self.colors['ask_red'])
        ]
        
        for text, col, color in labels:
            if text:
                tk.Label(
                    data_frame,
                    text=text,
                    font=("Consolas", 10, "bold"),
                    fg=color,
                    bg=self.colors['bg_light']
                ).grid(row=0, column=col, padx=5, pady=5)
    
    def create_spread_row(self, row, spread, spread_pct):
        """Create spread indicator row"""
        spread_frame = tk.Frame(self.content_frame, bg=self.colors['spread_yellow'], height=35)
        spread_frame.pack(fill=tk.X, pady=3)
        spread_frame.pack_propagate(False)
        
        tk.Label(
            spread_frame,
            text=f"SPREAD: ₹{spread:.2f} ({spread_pct:.3f}%)",
            font=("Arial", 12, "bold"),
            fg=self.colors['bg_dark'],
            bg=self.colors['spread_yellow']
        ).pack(expand=True, pady=8)
    
    def start_simulation(self):
        """Start data simulation"""
        def simulate():
            while True:
                try:
                    # Update price with small random movement
                    price_change = random.uniform(-2, 2)
                    self.current_price += price_change
                    
                    # Keep price within reasonable bounds
                    self.current_price = max(self.current_price, self.base_price * 0.98)
                    self.current_price = min(self.current_price, self.base_price * 1.02)
                    
                    # Update price display
                    change = self.current_price - self.base_price
                    change_pct = (change / self.base_price) * 100
                    
                    self.root.after(0, self.update_price_display, change, change_pct)
                    
                    # Update market depth
                    self.root.after(0, self.update_market_depth)
                    
                    # Update analysis
                    analysis_text = self.generate_analysis()
                    self.root.after(0, lambda: self.analysis_label.config(text=analysis_text))
                    
                    time.sleep(1)  # Update every second
                    
                except Exception as e:
                    print(f"Simulation error: {e}")
                    time.sleep(1)
        
        # Start simulation in background thread
        sim_thread = threading.Thread(target=simulate, daemon=True)
        sim_thread.start()
    
    def update_price_display(self, change, change_pct):
        """Update price and change display"""
        self.price_label.config(text=f"₹{self.current_price:.2f}")
        
        change_color = self.colors['bid_green'] if change >= 0 else self.colors['ask_red']
        change_text = f"{change:+.2f} ({change_pct:+.2f}%)"
        self.change_label.config(text=change_text, fg=change_color)
    
    def generate_analysis(self):
        """Generate sample analysis text"""
        analyses = [
            "Strong buying pressure detected at current levels. Volume above average.",
            "Order book showing balanced liquidity. Spread within normal range.",
            "Large orders detected on bid side. Potential support building.",
            "Selling pressure increasing. Watch for breakdown below support.",
            "High frequency trading activity observed. Tight spreads maintained.",
            "Volume profile shows accumulation zone. Bullish sentiment emerging.",
            "Order flow imbalance favoring buyers. Momentum building upward.",
            "Resistance level tested multiple times. Breakout potential high."
        ]
        
        return f"🔍 {random.choice(analyses)} | Time: {datetime.now().strftime('%H:%M:%S')}"
    
    def run(self):
        """Start the demo"""
        self.root.mainloop()


def main():
    """Main demo function"""
    print("🚀 ANGEL ONE STYLE MARKET DEPTH DEMO")
    print("=" * 50)
    print("Features demonstrated:")
    print("✅ Professional Angel One style interface")
    print("✅ Enhanced coloring (Green bids, Red asks)")
    print("✅ Volume visualization bars")
    print("✅ Real-time price updates")
    print("✅ Spread calculation and display")
    print("✅ Live market analysis")
    print("✅ Dark theme for better readability")
    print("=" * 50)
    print("\n📊 Opening demo window...")
    print("⏹️  Close window to stop demo")
    
    try:
        demo = AngelStyleMarketDepthDemo()
        demo.run()
        
    except KeyboardInterrupt:
        print("\n⏹️ Demo stopped by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")


if __name__ == "__main__":
    main()
