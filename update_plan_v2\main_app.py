#!/usr/bin/env python3
"""
Main Application - Ultimate Trading Analysis v2
Modular version with separate component files
"""

import tkinter as tk
from tkinter import ttk
import customtkinter as ctk
import threading
import time
import numpy as np
import websocket
import json
import ssl
import requests
import random
import subprocess
import sys
import os
import sv_ttk  # Sun Valley theme
import ping3  # For ping monitoring
from datetime import datetime
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from config import EXECUTIVE_COLORS, APP_CONFIG
from trading_signals import TradingSignalsWidget
from market_analytics import MarketAnalyticsWidget
from trading_charts import TradingChartsWidget
from data_simulator import MarketDataSimulator
from market_conditions import MARKET_CONDITION_NAMES
from header_ui import HeaderUI
from order_flow_analysis import OrderFlowAnalyzer
from order_flow_ui import OrderFlowUI


class UltimateTrading:
    """Main trading application with modular components"""

    def __init__(self):
        # Set CustomTkinter appearance mode and theme
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

        self.root = ctk.CTk()  # Use CustomTkinter main window
        self.setup_window()

        # Data simulator with default market condition
        self.current_market_condition = "sideways"
        self.simulator = MarketDataSimulator(self.current_market_condition)

        # UI components
        self.order_book_frame = None
        self.trading_signals = None
        # REMOVED FOR ORDER FLOW ANALYSIS IMPLEMENTATION
        # self.market_analytics = None
        # self.trading_charts = None

        # Order Flow Analysis Engine
        self.order_flow_analyzer = OrderFlowAnalyzer(history_minutes=5)
        self.order_flow_ui = None

        # Chart update control
        self.chart_update_counter = 0

        # Control flags
        self.running = False
        self.data_source = "simulated"  # simulated, live, disconnected
        self.current_market_condition = 'sideways'

        # WebSocket connection for live data
        self.ws = None
        self.ws_connected = False
        self.ws_connecting = False  # Flag to prevent multiple connections
        self.live_order_book_data = {'bids': [], 'asks': []}

        # Smart API credentials
        self.smart_api = None
        self.cgcl_token = None
        self.sws = None  # Smart WebSocket V2

        # Smart API credentials
        self.api_credentials = {
            'api_key': 'xuyTns9P',
            'client_code': 'AAAN362675',
            'password': '4180',
            'totp_secret': 'TU6ZEIE7ROJBSES7MYJ5YVRJE4'
        }

        # Ping monitoring system
        self.ping_data = {
            'current_ping': 0,
            'status': 'unknown',
            'color': '#888888',
            'last_update': datetime.now()
        }
        self.ping_targets = ['*******', 'google.com', '*******']  # Multiple targets for reliability
        self.ping_label = None

        # CustomTkinter provides modern theming automatically
        print("✅ CustomTkinter dark theme applied successfully!")

        # Header UI will be initialized in create_ui
        self.header_ui = None

        # UI Freeze Detection
        self.last_ui_update = time.time()
        self.ui_freeze_detector_active = True

        # UI Update Throttling
        self.quote_update_pending = False
        self.depth_update_pending = False
        self.latest_quote_data = None
        self.latest_depth_data = None
        self.last_ui_update_time = 0

        self.create_ui()

        # Update UI with correct stock after creation (if restart data was loaded)
        if hasattr(self, '_restart_loaded') and self._restart_loaded:
            print(f"🔄 [RESTART] Updating UI with restarted stock: {self.current_symbol}")

            # Update header UI
            if self.header_ui:
                self.header_ui.update_current_stock(self.current_symbol)
                self.header_ui.set_data_source(self.data_source)

            # Update main symbol display
            self.update_symbol_display(self.current_symbol)

            # Update stock search widget
            if hasattr(self, 'stock_search_widget') and self.stock_search_widget:
                print(f"🔄 [RESTART] Updating stock search widget with: {self.current_symbol}")
                self.stock_search_widget.update_current_stock(self.current_symbol)

        self.start_data_updates()
        self.start_ui_freeze_detector()

        # Update symbol display with current symbol after UI is created
        self.root.after(100, lambda: self.update_symbol_display(self.current_symbol))

    def create_order_flow_row(self, parent_frame):
        """Create the Order Flow Analysis row"""
        try:
            self.order_flow_ui = OrderFlowUI(parent_frame)
            print("✅ Order Flow Analysis row created successfully")
        except Exception as e:
            print(f"❌ Error creating Order Flow Analysis row: {e}")
            import traceback
            traceback.print_exc()

    def update_order_flow_analysis(self, bids, asks, ltp=None):
        """Update order flow analysis with new order book data"""
        try:
            if self.order_flow_analyzer and self.order_flow_ui:
                # Get the selected number of levels from the Order Flow UI
                selected_levels = self.order_flow_ui.current_levels

                # Limit the data to selected levels
                limited_bids = bids[:selected_levels] if bids else []
                limited_asks = asks[:selected_levels] if asks else []

                # Level selection working correctly - analysis uses selected levels
                # print(f"🔬 [ORDER_FLOW] Using {selected_levels} levels: {len(limited_bids)} bids, {len(limited_asks)} asks")

                # Perform order flow analysis with selected levels
                analysis_result = self.order_flow_analyzer.analyze_order_flow(limited_bids, limited_asks, ltp)

                # Update UI with results
                self.order_flow_ui.update_order_flow_data(analysis_result)

                # Log significant events
                summary = analysis_result.get('summary', {})
                if summary.get('key_signals'):
                    signals = ', '.join(summary['key_signals'])
                    print(f"🔬 [ORDER_FLOW] Signals: {signals}")

        except Exception as e:
            print(f"⚠️ [ORDER_FLOW] Analysis error: {e}")

    def update_symbol_display(self, symbol):
        """Update the main symbol display in market status row"""
        try:
            if hasattr(self, 'symbol_label') and self.symbol_label:
                clean_symbol = symbol.replace('-EQ', '')
                self.symbol_label.configure(text=clean_symbol)
                print(f"✅ [SYMBOL] Updated main symbol display: {clean_symbol}")
        except Exception as e:
            print(f"❌ [SYMBOL] Error updating symbol display: {e}")

    def setup_window(self):
        """Setup main window with proper sizing and centering"""
        # Set initial title (will be updated later when current_symbol is set)
        self.root.title("Ultimate Trading Analysis v2 - Loading...")

        # Get screen dimensions
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # Parse window size from config
        window_size = APP_CONFIG['window_size']
        width, height = map(int, window_size.split('x'))

        # Ensure window fits on screen (max 90% of screen size)
        max_width = int(screen_width * 0.9)
        max_height = int(screen_height * 0.9)

        width = min(width, max_width)
        height = min(height, max_height)

        # Calculate center position
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        # Set geometry with center positioning
        self.root.geometry(f'{width}x{height}+{x}+{y}')

        # Set minimum size
        self.root.minsize(1000, 700)

        # Make window resizable
        self.root.resizable(True, True)

    def update_window_title(self):
        """Update window title with current stock"""
        clean_symbol = self.current_symbol.replace('-EQ', '')
        self.root.title(f"Ultimate Trading Analysis v2 - {clean_symbol} Live Market")

    def stop_all_functions_completely(self):
        """Stop ALL running functions completely before switching stocks"""
        try:
            print("🛑 Stopping all functions completely...")

            # Set stop flag
            self.stop_all_functions = True

            # Stop WebSocket if connected
            if hasattr(self, 'ws_connected') and self.ws_connected:
                print("🔌 Stopping WebSocket...")
                self.disconnect_websocket()

            # Cancel all active timers
            for timer_id in self.active_timers:
                try:
                    self.root.after_cancel(timer_id)
                except:
                    pass
            self.active_timers.clear()

            # Wait for background threads to finish
            for thread in self.background_threads:
                if thread.is_alive():
                    thread.join(timeout=1.0)  # Wait max 1 second
            self.background_threads.clear()

            # Reset data streams
            self.live_order_book_data = {'bids': [], 'asks': []}

            # Reset chart counter
            self.chart_update_counter = 0

            print("✅ All functions stopped successfully")

        except Exception as e:
            print(f"⚠️ Error stopping functions: {e}")

    def restart_all_functions(self):
        """Restart all functions after stopping"""
        try:
            print("🚀 Restarting all functions...")

            # Reset stop flag
            self.stop_all_functions = False

            # Update window title
            self.update_window_title()

            # Restart data source if in live mode
            if self.data_source == 'live':
                # Small delay before reconnecting
                timer_id = self.root.after(1000, self.connect_websocket)
                self.active_timers.append(timer_id)

            print("✅ All functions restarted successfully")

        except Exception as e:
            print(f"⚠️ Error restarting functions: {e}")

    def create_ui(self):
        """Create main user interface"""
        # Main content area (header will be created inside scrollable content)
        self.create_main_content()

    def create_header_ui(self):
        """Create header UI using the HeaderUI component"""
        # Define callbacks for header interactions
        callbacks = {
            'market_condition_changed': self.handle_market_condition_change,
            'data_source_changed': self.handle_data_source_change,
            'refresh_clicked': self.handle_refresh_click,
            'stock_selected': self.select_stock
        }

        # Create header UI instance
        self.header_ui = HeaderUI(self.scrollable_frame, callbacks)

        # Set initial state to match current application state (without triggering callbacks)
        self.header_ui.data_source = self.data_source
        self.header_ui.current_market_condition = self.current_market_condition

        # Update button states to reflect initial state
        self.header_ui.update_data_source_buttons()
        self.header_ui.update_market_condition_buttons()

        # Create separate stock search section
        self.create_stock_search_section()
        self.header_ui.update_market_condition_visibility()

    def create_stock_search_section(self):
        """Create separate stock search section below header"""
        try:
            from stock_search import StockSearchWidget

            # Create stock search callbacks
            stock_callbacks = {
                'stock_selected': self.select_stock
            }

            # Create stock search widget in main scrollable frame
            self.stock_search_widget = StockSearchWidget(self.scrollable_frame, stock_callbacks)
            self.stock_search_widget.get_widget().pack(side=tk.TOP, pady=(10, 15), fill=tk.X, padx=20)

            # Update with current stock if it's not the default
            if hasattr(self, 'current_symbol') and self.current_symbol != "CGCL-EQ":
                print(f"🔄 [RESTART] Updating newly created stock search widget with: {self.current_symbol}")
                self.stock_search_widget.update_current_stock(self.current_symbol)

            print("✅ Stock search section created successfully")

        except Exception as e:
            print(f"❌ Error creating stock search section: {e}")
            import traceback
            traceback.print_exc()

    def handle_market_condition_change(self, condition_key):
        """Handle market condition change from header"""
        self.current_market_condition = condition_key

        # Update the simulator with new market condition
        if hasattr(self, 'data_simulator') and self.data_simulator:
            self.data_simulator.set_market_condition(condition_key)

    def handle_data_source_change(self, source):
        """Handle data source change from header"""
        # Use the existing set_data_source method
        self.set_data_source(source)

    def handle_refresh_click(self):
        """Handle refresh button click from header"""
        # Use the existing refresh method
        self.refresh_http_data()






    def create_main_content(self):
        """Create HFT-style single column, multi-row layout with CustomTkinter scrollable frame"""
        # Use CustomTkinter's built-in scrollable frame - much simpler!
        self.scrollable_frame = ctk.CTkScrollableFrame(
            self.root,
            corner_radius=10,
            scrollbar_button_color=("gray70", "gray30"),
            scrollbar_button_hover_color=("gray60", "gray40")
        )
        self.scrollable_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Note: CustomTkinter handles all scrolling automatically
        # No need for manual canvas, scrollbar, or window creation

        # CustomTkinter handles all scrolling automatically

        # ROW 0: Header (now inside scrollable content)
        self.create_header_ui()

        # ROW 1: Market Status Header
        self.create_market_status_row(self.scrollable_frame)

        # ROW 2: System Status Display (Simplified - No trading controls)
        self.create_system_status_row(self.scrollable_frame)

        # ROW 3: Order Book Display
        self.create_order_book_row(self.scrollable_frame)

        # ROW 4: Depth Chart (Between Order Book and Quantity Bars)
        self.create_depth_chart_row(self.scrollable_frame)

        # ROW 5: Total Quantity Bars
        self.create_quantity_bars_row(self.scrollable_frame)

        # ROW 6: Order Flow Analysis - NEW PROFESSIONAL IMPLEMENTATION
        self.create_order_flow_row(self.scrollable_frame)

        # ROW 7: Price Chart - REMOVED FOR ORDER FLOW ANALYSIS IMPLEMENTATION
        # self.create_price_chart_row(self.scrollable_frame)

        # ROW 8: Market Analytics - REMOVED FOR ORDER FLOW ANALYSIS IMPLEMENTATION
        # self.create_analytics_row(self.scrollable_frame)

        # Initialize notification system
        self.create_notification_system()

        # Show welcome notification
        self.root.after(2000, lambda: self.show_notification("🚀 Welcome to Ultimate Trading Analysis v2!", "success"))






    # Note: Canvas configuration and mousewheel binding no longer needed
    # CustomTkinter CTkScrollableFrame handles all scrolling automatically

    def create_market_status_row(self, parent):
        """ROW 1: Enhanced market status header with comprehensive trading metrics"""
        status_frame = ctk.CTkFrame(parent, height=80, corner_radius=8)
        status_frame.pack(fill=tk.X, padx=5, pady=2)
        status_frame.pack_propagate(False)

        # Left section - Symbol and price information
        left_frame = ctk.CTkFrame(status_frame, fg_color="transparent")
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=10, pady=8)

        # Symbol with exchange info
        symbol_frame = ctk.CTkFrame(left_frame, fg_color="transparent")
        symbol_frame.pack(side=tk.TOP, anchor='w')

        self.symbol_label = ctk.CTkLabel(symbol_frame, text="CGCL",
                                        font=ctk.CTkFont(family="Segoe UI", size=18, weight="bold"),
                                        text_color="#00ff88")
        self.symbol_label.pack(side=tk.LEFT)

        self.exchange_label = ctk.CTkLabel(symbol_frame, text="NSE",
                                          font=ctk.CTkFont(family="Segoe UI", size=10),
                                          text_color="#888888")
        self.exchange_label.pack(side=tk.LEFT, padx=(5, 0))

        # Price and change information
        price_frame = ctk.CTkFrame(left_frame, fg_color="transparent")
        price_frame.pack(side=tk.TOP, anchor='w', pady=(2, 0))

        self.main_price_label = ctk.CTkLabel(price_frame, text="₹850.28",
                                           font=ctk.CTkFont(family="Consolas", size=16, weight="bold"),
                                           text_color="#ffffff")
        self.main_price_label.pack(side=tk.LEFT)

        self.change_label = ctk.CTkLabel(price_frame, text="+2.15 (+0.25%)",
                                        font=ctk.CTkFont(family="Consolas", size=12),
                                        text_color="#00ff41")
        self.change_label.pack(side=tk.LEFT, padx=(8, 0))

        # Center-left - Market data
        market_frame = ctk.CTkFrame(status_frame, fg_color="transparent")
        market_frame.pack(side=tk.LEFT, fill=tk.Y, padx=15, pady=8)

        # High/Low
        hl_frame = ctk.CTkFrame(market_frame, fg_color="transparent")
        hl_frame.pack(side=tk.TOP, anchor='w')

        ctk.CTkLabel(hl_frame, text="H:",
                    font=ctk.CTkFont(family="Segoe UI", size=10),
                    text_color="#888888").pack(side=tk.LEFT)
        self.high_label = ctk.CTkLabel(hl_frame, text="₹855.50",
                                      font=ctk.CTkFont(family="Consolas", size=11),
                                      text_color="#00ff41")
        self.high_label.pack(side=tk.LEFT, padx=(2, 8))

        ctk.CTkLabel(hl_frame, text="L:",
                    font=ctk.CTkFont(family="Segoe UI", size=10),
                    text_color="#888888").pack(side=tk.LEFT)
        self.low_label = ctk.CTkLabel(hl_frame, text="₹845.20",
                                     font=ctk.CTkFont(family="Consolas", size=11),
                                     text_color="#ff4444")
        self.low_label.pack(side=tk.LEFT, padx=(2, 0))

        # Volume and spread
        vol_frame = ctk.CTkFrame(market_frame, fg_color="transparent")
        vol_frame.pack(side=tk.TOP, anchor='w', pady=(4, 0))

        ctk.CTkLabel(vol_frame, text="VOL:",
                    font=ctk.CTkFont(family="Segoe UI", size=10),
                    text_color="#888888").pack(side=tk.LEFT)
        self.volume_label = ctk.CTkLabel(vol_frame, text="1.2M",
                                        font=ctk.CTkFont(family="Consolas", size=11),
                                        text_color="#00aaff")
        self.volume_label.pack(side=tk.LEFT, padx=(2, 8))

        ctk.CTkLabel(vol_frame, text="SPR:",
                    font=ctk.CTkFont(family="Segoe UI", size=10),
                    text_color="#888888").pack(side=tk.LEFT)
        self.spread_label = ctk.CTkLabel(vol_frame, text="₹0.15",
                                        font=ctk.CTkFont(family="Consolas", size=11),
                                        text_color="#ffaa00")
        self.spread_label.pack(side=tk.LEFT, padx=(2, 0))

        # Center - Connection status (more compact)
        center_frame = ctk.CTkFrame(status_frame, fg_color="transparent")
        center_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=8)

        connection_label = ctk.CTkLabel(center_frame, text="● LIVE FEED (WebSocket Connected)",
                                       font=ctk.CTkFont(family="Segoe UI", size=11, weight="bold"),
                                       text_color="#00ff41")
        connection_label.pack(expand=True)

        # Right section - Trading metrics and timing
        right_frame = ctk.CTkFrame(status_frame, fg_color="transparent")
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=10, pady=8)

        # Market timing
        time_frame = ctk.CTkFrame(right_frame, fg_color="transparent")
        time_frame.pack(side=tk.TOP, anchor='e')

        ctk.CTkLabel(time_frame, text="LAST:",
                    font=ctk.CTkFont(family="Segoe UI", size=10),
                    text_color="#888888").pack(side=tk.LEFT)
        self.last_update_label = ctk.CTkLabel(time_frame, text="14:32:45",
                                             font=ctk.CTkFont(family="Consolas", size=11),
                                             text_color="#ffffff")
        self.last_update_label.pack(side=tk.LEFT, padx=(2, 0))

        # Additional metrics
        metrics_frame = ctk.CTkFrame(right_frame, fg_color="transparent")
        metrics_frame.pack(side=tk.TOP, anchor='e', pady=(4, 0))

        ctk.CTkLabel(metrics_frame, text="VWAP:",
                    font=ctk.CTkFont(family="Segoe UI", size=10),
                    text_color="#888888").pack(side=tk.LEFT)
        self.vwap_label = ctk.CTkLabel(metrics_frame, text="₹851.45",
                                      font=ctk.CTkFont(family="Consolas", size=11),
                                      text_color="#00aaff")
        self.vwap_label.pack(side=tk.LEFT, padx=(2, 8))

        ctk.CTkLabel(metrics_frame, text="SESS:",
                    font=ctk.CTkFont(family="Segoe UI", size=10),
                    text_color="#888888").pack(side=tk.LEFT)
        self.session_label = ctk.CTkLabel(metrics_frame, text="OPEN",
                                         font=ctk.CTkFont(family="Consolas", size=11),
                                         text_color="#00ff41")
        self.session_label.pack(side=tk.LEFT, padx=(2, 0))

        # Store old metrics_label reference for compatibility
        self.metrics_label = self.volume_label

    def create_order_book_row(self, parent):
        """ROW 2: Order book display - Using modular OrderBookUI"""
        from order_book_ui import OrderBookUI

        # Create container frame with CustomTkinter
        book_frame = ctk.CTkFrame(parent, corner_radius=8)
        book_frame.pack(fill=tk.X, padx=5, pady=2)

        # Initialize the modular order book UI
        self.order_book_ui = OrderBookUI(book_frame)

        print("✅ Order book row created using modular OrderBookUI")

    def create_depth_chart_row(self, parent):
        """ROW 3: Depth Chart - Visual quantity distribution"""
        from depth_chart_ui import DepthChartUI

        # Create container frame with CustomTkinter
        chart_frame = ctk.CTkFrame(parent, corner_radius=8)
        chart_frame.pack(fill=tk.X, padx=5, pady=2)

        # Initialize the modular depth chart UI
        self.depth_chart_ui = DepthChartUI(chart_frame)

        print("✅ Depth chart row created using modular DepthChartUI")

    def create_quantity_bars_row(self, parent):
        """ROW 3: Enhanced Angel One Style Single Proportional Quantity Bar"""
        bars_row_frame = ctk.CTkFrame(parent, height=160, corner_radius=8)
        bars_row_frame.pack(fill=tk.X, padx=5, pady=2)
        bars_row_frame.pack_propagate(False)

        # Container for quantity bars with modern styling
        bars_container = ctk.CTkFrame(bars_row_frame, fg_color="transparent")
        bars_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Professional level control frame at the top
        level_control_frame = ctk.CTkFrame(bars_container, height=35, corner_radius=6)
        level_control_frame.pack(fill=tk.X, padx=40, pady=(10, 0))
        level_control_frame.pack_propagate(False)

        # Initialize quantity levels system
        self.quantity_levels = 10  # Default to 10 levels
        self.available_quantity_levels = [5, 10, 15, 20]  # Available level options
        self.ui_update_lock = False  # Prevent simultaneous UI updates

        # Left side - Level controls with CustomTkinter
        left_controls = ctk.CTkFrame(level_control_frame, fg_color="transparent")
        left_controls.pack(side=tk.LEFT, pady=5)

        # Level selection label
        level_label = ctk.CTkLabel(left_controls, text="📊 Levels:",
                                  font=ctk.CTkFont(family="Consolas", size=12, weight="bold"),
                                  text_color="#aaaaaa")
        level_label.pack(side=tk.LEFT, padx=(0, 5))

        # Level dropdown selector (like Order Flow Analysis)
        self.quantity_level_selector = ctk.CTkOptionMenu(
            left_controls,
            values=["5", "10", "15", "20"],
            command=self.on_quantity_level_change,
            width=60,
            height=28,
            font=ctk.CTkFont(family="Segoe UI", size=10, weight="bold"),
            dropdown_font=ctk.CTkFont(family="Segoe UI", size=10),
            fg_color="#333333",
            button_color="#444444",
            button_hover_color="#555555",
            dropdown_fg_color="#333333",
            dropdown_hover_color="#444444",
            text_color="#ffffff",
            dropdown_text_color="#ffffff"
        )
        self.quantity_level_selector.set(str(self.quantity_levels))  # Set current level
        self.quantity_level_selector.pack(side=tk.LEFT, padx=(0, 10))

        # Right side - Mode info
        right_info = ctk.CTkFrame(level_control_frame, fg_color="transparent")
        right_info.pack(side=tk.RIGHT, pady=5)

        # Current mode display
        self.quantity_mode_info_label = ctk.CTkLabel(right_info, text=f"Using: {self.quantity_levels} levels",
                                                    font=ctk.CTkFont(family="Consolas", size=11),
                                                    text_color="#888888")
        self.quantity_mode_info_label.pack(side=tk.RIGHT)

        # Angel One style single bar container with modern spacing
        bar_container = ctk.CTkFrame(bars_container, fg_color="transparent")
        bar_container.pack(fill=tk.BOTH, expand=True, padx=40, pady=(5, 20))

        # Quantity labels row with modern styling
        labels_frame = ctk.CTkFrame(bar_container, height=35, corner_radius=6)
        labels_frame.pack(fill=tk.X, pady=(0, 5))
        labels_frame.pack_propagate(False)

        # Configure grid for proper alignment
        labels_frame.grid_columnconfigure(0, weight=1)
        labels_frame.grid_columnconfigure(1, weight=0)
        labels_frame.grid_columnconfigure(2, weight=1)

        # Bid quantity label (left) - Modern styling
        self.bid_qty_label = ctk.CTkLabel(labels_frame, text="1,560",
                                         font=ctk.CTkFont(family="Consolas", size=16, weight="bold"),
                                         text_color='#00ffaa', anchor='w')
        self.bid_qty_label.grid(row=0, column=0, sticky='w', padx=10, pady=5)

        # Total Quantity label (center) - Modern styling
        ctk.CTkLabel(labels_frame, text="Total Quantity",
                    font=ctk.CTkFont(family="Consolas", size=14, weight="bold"),
                    anchor='center').grid(row=0, column=1, padx=20, pady=5)

        # Ask quantity label (right) - Modern styling
        self.ask_qty_label = ctk.CTkLabel(labels_frame, text="8,448",
                                         font=ctk.CTkFont(family="Consolas", size=16, weight="bold"),
                                         text_color='#ff4444', anchor='e')
        self.ask_qty_label.grid(row=0, column=2, sticky='e', padx=10, pady=5)

        # Enhanced single proportional bar with CustomTkinter modern styling
        self.quantity_bar_frame = ctk.CTkFrame(bar_container, height=60, corner_radius=8, border_width=2, border_color="#555555")
        self.quantity_bar_frame.pack(fill=tk.X, pady=15, padx=15)
        self.quantity_bar_frame.pack_propagate(False)

        # Bid portion (left side - modern green styling)
        self.bid_portion = ctk.CTkFrame(self.quantity_bar_frame, fg_color='#00ff66', height=60, width=300, corner_radius=6)
        self.bid_portion.pack(side=tk.LEFT, fill=tk.Y, padx=2, pady=2)
        self.bid_portion.pack_propagate(False)

        # Ask portion (right side - modern red styling)
        self.ask_portion = ctk.CTkFrame(self.quantity_bar_frame, fg_color='#ff0066', height=60, width=300, corner_radius=6)
        self.ask_portion.pack(side=tk.RIGHT, fill=tk.Y, padx=2, pady=2)
        self.ask_portion.pack_propagate(False)

        print("✅ Quantity bars created with enhanced visibility")

    def create_system_status_row(self, parent):
        """ROW 2: Enhanced Trading Metrics Display"""
        status_frame = ctk.CTkFrame(parent, height=160, corner_radius=12)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        status_frame.pack_propagate(False)

        # Main status container
        main_status = ctk.CTkFrame(status_frame, corner_radius=8)
        main_status.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Market Depth Analysis
        depth_frame = ctk.CTkFrame(main_status, corner_radius=8)
        depth_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=8, pady=8)

        ctk.CTkLabel(depth_frame, text="📊 Market Depth",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(20, 8))

        self.depth_label = ctk.CTkLabel(depth_frame, text="Bid: 55.0% | Ask: 45.0%",
                                       font=ctk.CTkFont(size=12),
                                       text_color="#00aaff")
        self.depth_label.pack(pady=(5, 20))

        # Order Flow Analysis
        flow_frame = ctk.CTkFrame(main_status, corner_radius=8)
        flow_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=8, pady=8)

        ctk.CTkLabel(flow_frame, text="🔄 Order Flow",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(20, 8))

        self.flow_label = ctk.CTkLabel(flow_frame, text="Neutral",
                                      font=ctk.CTkFont(size=12),
                                      text_color="#ffaa00")
        self.flow_label.pack(pady=(5, 20))

        # Price Action
        action_frame = ctk.CTkFrame(main_status, corner_radius=8)
        action_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=8, pady=8)

        ctk.CTkLabel(action_frame, text="📈 Price Action",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(20, 8))

        self.action_label = ctk.CTkLabel(action_frame, text="Sideways",
                                        font=ctk.CTkFont(size=12),
                                        text_color="#888888")
        self.action_label.pack(pady=(5, 20))

        # Market Momentum
        momentum_frame = ctk.CTkFrame(main_status, corner_radius=8)
        momentum_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=8, pady=8)

        ctk.CTkLabel(momentum_frame, text="⚡ Momentum",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(20, 8))

        self.momentum_label = ctk.CTkLabel(momentum_frame, text="Weak",
                                          font=ctk.CTkFont(size=12),
                                          text_color="#888888")
        self.momentum_label.pack(pady=(5, 20))

        print("✅ Enhanced trading metrics display created successfully!")

        # Initialize volatility tracking for intelligent spread analysis
        self.price_history = []
        self.recent_volatility_factor = 1.0

        # Previous close price for percentage calculation (live mode only)
        self.previous_close_price = None

        # Stock selection variables (default values)
        self.current_symbol = "CGCL-EQ"
        self.current_token = None

        # Check for restart stock data and override defaults if found
        restart_loaded = self.load_restart_stock_if_exists()
        if restart_loaded:
            print(f"🔄 [RESTART] Stock data loaded during initialization: {self.current_symbol}")

        # Add control flags for stopping functions
        self.stop_all_functions = False
        self.background_threads = []
        self.active_timers = []

        # Update window title now that current_symbol is set
        self.update_window_title()

    def update_trading_metrics(self, bids, asks, current_price=None):
        """Update the enhanced trading metrics with real market data"""
        try:
            if not bids or not asks:
                return

            # Calculate market depth percentages
            total_bid_qty = sum([bid[0] for bid in bids[:10]])  # Top 10 levels
            total_ask_qty = sum([ask[2] for ask in asks[:10]])  # Top 10 levels
            total_qty = total_bid_qty + total_ask_qty

            if total_qty > 0:
                bid_pct = (total_bid_qty / total_qty) * 100
                ask_pct = (total_ask_qty / total_qty) * 100

                if hasattr(self, 'depth_label'):
                    self.depth_label.configure(text=f"Bid: {bid_pct:.1f}% | Ask: {ask_pct:.1f}%")

            # Analyze order flow (based on bid/ask imbalance)
            if hasattr(self, 'flow_label'):
                if bid_pct > 60:
                    flow_text = "Bullish"
                    flow_color = "#00ff41"
                elif ask_pct > 60:
                    flow_text = "Bearish"
                    flow_color = "#ff4444"
                else:
                    flow_text = "Neutral"
                    flow_color = "#ffaa00"

                self.flow_label.configure(text=flow_text, text_color=flow_color)

            # Analyze price action (intelligent spread analysis based on stock characteristics)
            if hasattr(self, 'action_label') and current_price:
                best_bid = bids[0][2] if bids else 0
                best_ask = asks[0][0] if asks else 0
                spread = best_ask - best_bid if best_bid and best_ask else 0

                # Calculate intelligent spread thresholds based on stock price and characteristics
                spread_percentage = (spread / current_price) * 100 if current_price > 0 else 0

                # Dynamic thresholds based on price level and volatility
                if current_price < 50:
                    # Low-priced stocks (₹1-50): Tighter absolute thresholds
                    tight_threshold = 0.05  # ₹0.05
                    wide_threshold = 0.25   # ₹0.25
                elif current_price < 200:
                    # Mid-priced stocks (₹50-200): Moderate thresholds
                    tight_threshold = 0.10  # ₹0.10
                    wide_threshold = 0.50   # ₹0.50
                elif current_price < 1000:
                    # High-priced stocks (₹200-1000): Higher absolute thresholds
                    tight_threshold = 0.25  # ₹0.25
                    wide_threshold = 1.00   # ₹1.00
                else:
                    # Very high-priced stocks (₹1000+): Use percentage-based thresholds
                    tight_threshold = current_price * 0.0005  # 0.05%
                    wide_threshold = current_price * 0.002    # 0.2%

                # Additional volatility adjustment (simplified)
                # In real implementation, this could use ATR or recent price volatility
                volatility_multiplier = 1.0
                if hasattr(self, 'recent_price_changes'):
                    # Adjust thresholds based on recent volatility
                    volatility_multiplier = max(0.5, min(2.0, self.recent_volatility_factor))

                adjusted_tight = tight_threshold * volatility_multiplier
                adjusted_wide = wide_threshold * volatility_multiplier

                # Determine spread classification
                if spread <= adjusted_tight:
                    action_text = "Tight"
                    action_color = "#00ff41"
                elif spread >= adjusted_wide:
                    action_text = "Wide"
                    action_color = "#ff4444"
                else:
                    action_text = "Normal"
                    action_color = "#ffaa00"

                # Add percentage context for better understanding
                if spread_percentage < 0.01:
                    action_text += " (<0.01%)"
                elif spread_percentage < 0.05:
                    action_text += f" ({spread_percentage:.2f}%)"
                elif spread_percentage < 0.1:
                    action_text += f" ({spread_percentage:.2f}%)"
                else:
                    action_text += f" ({spread_percentage:.1f}%)"

                self.action_label.configure(text=action_text, text_color=action_color)

            # Calculate momentum (based on order book pressure)
            if hasattr(self, 'momentum_label'):
                # Simple momentum based on top 3 levels imbalance
                top3_bid_qty = sum([bid[0] for bid in bids[:3]])
                top3_ask_qty = sum([ask[2] for ask in asks[:3]])
                top3_total = top3_bid_qty + top3_ask_qty

                if top3_total > 0:
                    top3_bid_pct = (top3_bid_qty / top3_total) * 100

                    if top3_bid_pct > 65:
                        momentum_text = "Strong ↑"
                        momentum_color = "#00ff41"
                    elif top3_bid_pct < 35:
                        momentum_text = "Strong ↓"
                        momentum_color = "#ff4444"
                    elif top3_bid_pct > 55:
                        momentum_text = "Weak ↑"
                        momentum_color = "#88ff88"
                    elif top3_bid_pct < 45:
                        momentum_text = "Weak ↓"
                        momentum_color = "#ff8888"
                    else:
                        momentum_text = "Neutral"
                        momentum_color = "#888888"

                    self.momentum_label.configure(text=momentum_text, text_color=momentum_color)

        except Exception as e:
            print(f"⚠️ Trading metrics update error: {e}")

    def create_price_chart_row(self, parent):
        """ROW 6: Price Chart Display"""
        chart_frame = ctk.CTkFrame(parent, height=300, corner_radius=12)
        chart_frame.pack(fill=tk.X, padx=5, pady=5)
        chart_frame.pack_propagate(False)

        # Title
        title_frame = ctk.CTkFrame(chart_frame, height=35, corner_radius=8)
        title_frame.pack(fill=tk.X, padx=10, pady=(10, 5))
        title_frame.pack_propagate(False)

        ctk.CTkLabel(title_frame, text="📈 Price Chart",
                    font=ctk.CTkFont(family="Segoe UI", size=16, weight="bold")).pack(pady=8)

        # Chart container
        chart_container = ctk.CTkFrame(chart_frame, corner_radius=8)
        chart_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # Import and create trading charts
        from trading_charts import TradingCharts
        self.trading_charts = TradingCharts(chart_container)

        print("✅ Price chart row created successfully!")

    def create_analytics_row(self, parent):
        """ROW 7: Market Analytics Display"""
        analytics_frame = ctk.CTkFrame(parent, height=200, corner_radius=12)
        analytics_frame.pack(fill=tk.X, padx=5, pady=5)
        analytics_frame.pack_propagate(False)

        # Title
        title_frame = ctk.CTkFrame(analytics_frame, height=35, corner_radius=8)
        title_frame.pack(fill=tk.X, padx=10, pady=(10, 5))
        title_frame.pack_propagate(False)

        ctk.CTkLabel(title_frame, text="📊 Market Analytics",
                    font=ctk.CTkFont(family="Segoe UI", size=16, weight="bold")).pack(pady=8)

        # Analytics container
        analytics_container = ctk.CTkFrame(analytics_frame, corner_radius=8)
        analytics_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # Performance metrics with progress bars
        metrics = [
            ("Market Trend", 0.65, "#0088cc"),
            ("Volume Activity", 0.80, "#00aa44"),
            ("Volatility Index", 0.45, "#ffaa00"),
            ("Support/Resistance", 0.75, "#cc6600")
        ]

        for i, (metric_name, value, color) in enumerate(metrics):
            metric_frame = ctk.CTkFrame(analytics_container, fg_color="transparent")
            metric_frame.pack(fill=tk.X, padx=20, pady=8)

            ctk.CTkLabel(metric_frame, text=metric_name, width=150,
                        font=ctk.CTkFont(size=12, weight="bold")).pack(side=tk.LEFT)

            progress = ctk.CTkProgressBar(metric_frame, width=200, height=15,
                                        progress_color=color, corner_radius=8)
            progress.set(value)
            progress.pack(side=tk.LEFT, padx=15)

            ctk.CTkLabel(metric_frame, text=f"{value*100:.0f}%", width=50,
                        font=ctk.CTkFont(size=11)).pack(side=tk.LEFT)

        print("✅ Analytics row created successfully!")



    def create_notification_system(self):
        """Create modern notification system with visual effects"""
        # Notification container (top-right overlay)
        self.notification_frame = ctk.CTkFrame(self.root, width=300, height=100,
                                              corner_radius=12, border_width=2,
                                              border_color="#00ff88")

        # Initially hidden
        self.notification_frame.place_forget()

    def show_notification(self, message, notification_type="info", duration=3000):
        """Show animated notification with modern styling"""
        # Configure colors based on type
        colors = {
            "info": {"bg": "#1f538d", "border": "#0099ff", "icon": "ℹ️"},
            "success": {"bg": "#2d5a2d", "border": "#00ff88", "icon": "✅"},
            "warning": {"bg": "#5a4d2d", "border": "#ffaa00", "icon": "⚠️"},
            "error": {"bg": "#5a2d2d", "border": "#ff4444", "icon": "❌"}
        }

        color_config = colors.get(notification_type, colors["info"])

        # Update notification appearance
        self.notification_frame.configure(fg_color=color_config["bg"],
                                         border_color=color_config["border"])

        # Clear previous content
        for widget in self.notification_frame.winfo_children():
            widget.destroy()

        # Add icon and message
        content_frame = ctk.CTkFrame(self.notification_frame, fg_color="transparent")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        icon_label = ctk.CTkLabel(content_frame, text=color_config["icon"],
                                 font=ctk.CTkFont(size=20))
        icon_label.pack(side=tk.LEFT, padx=(0, 10))

        message_label = ctk.CTkLabel(content_frame, text=message,
                                    font=ctk.CTkFont(size=12, weight="bold"),
                                    wraplength=200)
        message_label.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Position notification (top-right)
        self.notification_frame.place(x=self.root.winfo_width()-320, y=20)

        # Auto-hide after duration
        self.root.after(duration, self.hide_notification)

        print(f"📢 Notification: {message}")

    def hide_notification(self):
        """Hide notification with fade effect"""
        self.notification_frame.place_forget()

    def add_hover_effects(self):
        """Add hover effects to buttons and interactive elements"""
        # This would be called after UI creation to add hover effects
        # CustomTkinter already has built-in hover effects, but we can enhance them
        pass

    def create_loading_animation(self, parent):
        """Create modern loading animation"""
        loading_frame = ctk.CTkFrame(parent, corner_radius=8)

        # Animated progress bar
        self.loading_progress = ctk.CTkProgressBar(loading_frame, width=200, height=8,
                                                  corner_radius=4)
        self.loading_progress.pack(pady=20)

        # Start animation
        self.animate_loading()

        return loading_frame

    def animate_loading(self):
        """Animate loading progress bar"""
        if hasattr(self, 'loading_progress'):
            current_value = self.loading_progress.get()
            if current_value >= 1.0:
                self.loading_progress.set(0.0)
            else:
                self.loading_progress.set(current_value + 0.1)

            # Continue animation
            self.root.after(100, self.animate_loading)

    def create_pulse_effect(self, widget, color="#00ff88"):
        """Create pulse effect for important elements"""
        original_color = widget.cget("fg_color")

        def pulse():
            widget.configure(border_width=3, border_color=color)
            self.root.after(500, lambda: widget.configure(border_width=1, border_color="gray"))
            self.root.after(1000, pulse)

        pulse()

    def enhance_button_feedback(self, button):
        """Enhance button with visual feedback"""
        original_color = button.cget("fg_color")

        def on_click():
            # Flash effect on click
            button.configure(fg_color="#ffffff")
            self.root.after(100, lambda: button.configure(fg_color=original_color))

        # Bind click effect
        button.configure(command=lambda: [button.cget("command")() if button.cget("command") else None, on_click()])

    def create_modern_tooltip(self, widget, text):
        """Create modern tooltip for widgets"""
        def show_tooltip(event):
            tooltip = ctk.CTkToplevel(self.root)
            tooltip.wm_overrideredirect(True)
            tooltip.configure(fg_color="#2d2d2d", corner_radius=8)

            label = ctk.CTkLabel(tooltip, text=text,
                               font=ctk.CTkFont(size=10),
                               text_color="#ffffff")
            label.pack(padx=8, pady=4)

            # Position tooltip
            x = event.x_root + 10
            y = event.y_root + 10
            tooltip.geometry(f"+{x}+{y}")

            # Auto-hide after 3 seconds
            tooltip.after(3000, tooltip.destroy)

        widget.bind("<Enter>", show_tooltip)





















    def on_quantity_level_change(self, selected_level):
        """Handle level selection change from dropdown"""
        try:
            levels = int(selected_level)
            self.set_quantity_levels(levels)
        except ValueError:
            print(f"⚠️ [QUANTITY_BARS] Invalid level selection: {selected_level}")

    def set_quantity_levels(self, levels):
        """Set the number of quantity levels to display"""
        self.quantity_levels = levels

        # Update dropdown selector
        if hasattr(self, 'quantity_level_selector'):
            self.quantity_level_selector.set(str(levels))

        # Update mode info
        self.quantity_mode_info_label.configure(text=f"Using: {levels} levels")

        print(f"📊 [QUANTITY_BARS] Quantity levels changed to: {levels}")

        # NOTE: Removed automatic synchronization to make each UI independent
        # Each UI component now has its own independent level selection

        # Trigger quantity bar update if we have current data - Schedule on main thread
        if hasattr(self, 'current_bids') and hasattr(self, 'current_asks'):
            print(f"🔧 [DEBUG] Scheduling quantity bar update on main thread...")
            self.root.after_idle(lambda: self.update_quantity_bars(self.current_bids, self.current_asks))

    def set_quantity_mode(self, mode):
        """Set quantity calculation mode (legacy method for compatibility)"""
        if mode == "lite":
            self.set_quantity_levels(5)
        else:  # full mode
            self.set_quantity_levels(20)

        print(f"🔄 Quantity mode switched to: {mode.upper()} ({self.quantity_levels} levels)")

    def create_hybrid_order_book(self):
        """Create hybrid order book combining SNAP_QUOTE (levels 1-5) + DEPTH (levels 6-20)"""
        try:
            snap_data = getattr(self, 'snap_quote_data', {})
            depth_data = getattr(self, 'depth_data', {})

            if not snap_data or not depth_data:
                print("⚠️ Missing SNAP_QUOTE or DEPTH data for hybrid order book")
                return

            snap_bids = snap_data.get('bids', [])
            snap_asks = snap_data.get('asks', [])
            depth_bids = depth_data.get('bids', [])
            depth_asks = depth_data.get('asks', [])

            # Combine: SNAP_QUOTE (1-5) + DEPTH (6-20)
            hybrid_bids = snap_bids[:5] + depth_bids[:15]  # First 5 from SNAP, next 15 from DEPTH
            hybrid_asks = snap_asks[:5] + depth_asks[:15]

            print(f"🔄 Created hybrid order book: SNAP({len(snap_bids[:5])}) + DEPTH({len(depth_bids[:15])}) = {len(hybrid_bids)} bids")
            print(f"🔄 Created hybrid order book: SNAP({len(snap_asks[:5])}) + DEPTH({len(depth_asks[:15])}) = {len(hybrid_asks)} asks")

            # Update the live order book data
            self.live_order_book_data = {'bids': hybrid_bids, 'asks': hybrid_asks}

            # Update UI - Schedule on main thread to prevent freezing
            print(f"🔧 [DEBUG] Scheduling UI updates on main thread...")
            self.root.after_idle(lambda: self.update_order_book_display(hybrid_bids, hybrid_asks))
            self.root.after_idle(lambda: self.update_quantity_bars(hybrid_bids, hybrid_asks))

        except Exception as e:
            print(f"⚠️ Error creating hybrid order book: {e}")

    def update_quantity_bars(self, bids, asks):
        """Update Angel One style single proportional quantity bar with full/lite mode support"""
        try:
            print(f"🔧 [QBAR] [DEBUG] update_quantity_bars called with {len(bids)} bids, {len(asks)} asks")

            # Check if we should stop processing
            if self.stop_all_functions:
                print("🛑 [QBAR] [DEBUG] Stopping quantity bar update - functions stopped")
                return

            # Check if quantity bar components exist
            if not hasattr(self, 'bid_qty_label') or not hasattr(self, 'ask_qty_label'):
                print("⚠️ [QBAR] [DEBUG] Missing quantity bar labels, skipping update")
                return

            if not hasattr(self, 'bid_portion') or not hasattr(self, 'ask_portion'):
                print("⚠️ [QBAR] [DEBUG] Missing quantity bar portions, skipping update")
                return

            if not hasattr(self, 'quantity_bar_frame'):
                print("⚠️ [QBAR] [DEBUG] Missing quantity bar frame, skipping update")
                return

            print("✅ [QBAR] [DEBUG] All quantity bar components found")

            # Store current data for mode switching
            self.current_bids = bids
            self.current_asks = asks

            # Determine which levels to include based on quantity_levels
            levels = getattr(self, 'quantity_levels', 10)
            bids_to_use = bids[:levels] if len(bids) >= levels else bids
            asks_to_use = asks[:levels] if len(asks) >= levels else asks
            print(f"📊 QUANTITY LEVELS: Using {len(bids_to_use)} bid levels, {len(asks_to_use)} ask levels (max: {levels})")

            # Calculate total quantities - handle both old format (3 elements) and new format (4 elements with source)
            total_bid_qty = 0
            for bid in bids_to_use:
                if len(bid) == 4:
                    qty, _, _, _ = bid  # qty, orders, price, source
                else:
                    qty, _, _ = bid  # qty, orders, price
                total_bid_qty += qty

            total_ask_qty = 0
            for ask in asks_to_use:
                if len(ask) == 4:
                    _, _, qty, _ = ask  # price, orders, qty, source
                else:
                    _, _, qty = ask  # price, orders, qty
                total_ask_qty += qty

            # Calculate percentages
            total_qty = total_bid_qty + total_ask_qty
            if total_qty > 0:
                bid_percentage = (total_bid_qty / total_qty) * 100
                ask_percentage = (total_ask_qty / total_qty) * 100
            else:
                bid_percentage = ask_percentage = 0

            # Debug logging with data format verification
            print(f"🔧 [QBAR] Bid data format check - first bid: {bids[0] if bids else 'None'}")
            print(f"🔧 [QBAR] Ask data format check - first ask: {asks[0] if asks else 'None'}")
            print(f"📊 [QBAR] Quantity Bar Update ({levels}L): Bids={total_bid_qty:,} ({bid_percentage:.1f}%), Asks={total_ask_qty:,} ({ask_percentage:.1f}%)")

            # Update quantity labels with Angel One formatting and percentages
            bid_text = f"{total_bid_qty:,} ({bid_percentage:.1f}%)"
            ask_text = f"{total_ask_qty:,} ({ask_percentage:.1f}%)"

            print(f"🔧 [QBAR] Updating bid label to: {bid_text}")
            print(f"🔧 [QBAR] Updating ask label to: {ask_text}")
            self.bid_qty_label.configure(text=bid_text)
            self.ask_qty_label.configure(text=ask_text)
            print("✅ [QBAR] Quantity labels updated successfully")

            # Calculate proportional widths for single bar (using already calculated percentages)
            if total_qty > 0:
                # Get the total width of the bar frame
                bar_width = self.quantity_bar_frame.winfo_width()
                if bar_width <= 1:  # If not yet rendered, use default
                    bar_width = 600

                # Calculate proportional widths using already calculated percentages
                bid_width = max(int(bar_width * (bid_percentage / 100)), 20)  # Minimum 20px
                ask_width = max(int(bar_width * (ask_percentage / 100)), 20)  # Minimum 20px

                # Debug proportions
                print(f"📊 [QBAR] Bar proportions: Bid={bid_percentage:.1f}% ({bid_width}px), Ask={ask_percentage:.1f}% ({ask_width}px)")

                # Update the proportional bar portions
                self.bid_portion.configure(width=bid_width)
                self.ask_portion.configure(width=ask_width)

                # Force update the display
                self.bid_portion.update_idletasks()
                self.ask_portion.update_idletasks()
                print("✅ [QBAR] Bar portions updated and refreshed")
            else:
                # No data - equal split
                print("⚠️ [QBAR] No data - using equal split")
                self.bid_portion.configure(width=300)
                self.ask_portion.configure(width=300)
                self.bid_portion.update_idletasks()
                self.ask_portion.update_idletasks()
                print("✅ [QBAR] Equal split applied")

        except Exception as e:
            print(f"⚠️ Error updating quantity bars: {e}")
            import traceback
            traceback.print_exc()

    def create_price_chart_row(self, parent):
        """ROW 4: Price chart"""
        chart_frame = tk.Frame(parent, bg='#0a0a0a', height=300)
        chart_frame.pack(fill=tk.X, padx=1, pady=1)
        chart_frame.pack_propagate(False)

        # Chart container
        chart_container = tk.Frame(chart_frame, bg='#151515')
        chart_container.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

        # Chart header
        chart_header = tk.Frame(chart_container, bg='#1a1a1a', height=25)
        chart_header.pack(fill=tk.X, padx=1, pady=1)
        chart_header.pack_propagate(False)

        tk.Label(chart_header, text="PRICE CHART - 1MIN", font=('Consolas', 12, 'bold'),
                bg='#1a1a1a', fg='#00ffff').pack(side=tk.LEFT, padx=10, pady=3)

        # Trading charts
        self.trading_charts = TradingChartsWidget(chart_container)
        self.trading_charts.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

    def create_market_depth_row(self, parent):
        """ROW 4: Market depth and volume"""
        depth_frame = tk.Frame(parent, bg='#0a0a0a', height=150)
        depth_frame.pack(fill=tk.X, padx=1, pady=1)
        depth_frame.pack_propagate(False)

        # Depth container
        depth_container = tk.Frame(depth_frame, bg='#151515')
        depth_container.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

        # Header
        depth_header = tk.Frame(depth_container, bg='#1a1a1a', height=25)
        depth_header.pack(fill=tk.X, padx=1, pady=1)
        depth_header.pack_propagate(False)

        tk.Label(depth_header, text="MARKET DEPTH & VOLUME", font=('Consolas', 12, 'bold'),
                bg='#1a1a1a', fg='#00ffff').pack(side=tk.LEFT, padx=10, pady=3)

        # Volume metrics
        volume_frame = tk.Frame(depth_container, bg='#151515')
        volume_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.volume_label = tk.Label(volume_frame, text="VOLUME: 1,234,567 | AVG: 45,230 | VWAP: ₹871.23",
                                    font=('Consolas', 11), bg='#151515', fg='#ffaa00')
        self.volume_label.pack(pady=10)

        self.depth_label = tk.Label(volume_frame, text="BID DEPTH: ₹2.1M | ASK DEPTH: ₹1.8M | IMBALANCE: +14%",
                                   font=('Consolas', 11), bg='#151515', fg='#b0b0b0')
        self.depth_label.pack(pady=5)

    def create_signals_analytics_row(self, parent):
        """ROW 5: Trading signals and analytics"""
        signals_frame = tk.Frame(parent, bg='#0a0a0a', height=120)
        signals_frame.pack(fill=tk.X, padx=1, pady=1)
        signals_frame.pack_propagate(False)

        # Signals container
        signals_container = tk.Frame(signals_frame, bg='#151515')
        signals_container.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

        # Left - Trading signals
        left_signals = tk.Frame(signals_container, bg='#151515')
        left_signals.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=1, pady=1)

        self.trading_signals = TradingSignalsWidget(left_signals, height=100)
        self.trading_signals.pack(fill=tk.BOTH, expand=True)

        # Right - Market analytics
        right_analytics = tk.Frame(signals_container, bg='#151515')
        right_analytics.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=1, pady=1)

        self.market_analytics = MarketAnalyticsWidget(right_analytics)
        self.market_analytics.pack(fill=tk.BOTH, expand=True)



    def update_order_book_display(self, bids, asks):
        """Update order book display and depth chart using modular components"""
        try:
            print(f"🔧 [DEBUG] Starting order book display update...")

            # Update order book UI
            if hasattr(self, 'order_book_ui'):
                print(f"🔧 [DEBUG] Updating order book UI...")
                self.order_book_ui.update_display(bids, asks)
                print(f"✅ [DEBUG] Order book UI updated")
            else:
                print("⚠️ OrderBookUI not initialized")

            # Update depth chart UI with the same data
            if hasattr(self, 'depth_chart_ui'):
                print(f"🔧 [DEBUG] Updating depth chart UI...")
                self.depth_chart_ui.update_chart(bids, asks)
                print(f"✅ [DEBUG] Depth chart UI updated")
            else:
                print("⚠️ DepthChartUI not initialized")

            # Update enhanced trading metrics
            print(f"🔧 [DEBUG] Updating trading metrics...")
            current_price = None
            if bids and asks:
                current_price = (bids[0][2] + asks[0][0]) / 2  # Mid price
            self.update_trading_metrics(bids, asks, current_price)
            print(f"✅ [DEBUG] Trading metrics updated")

        except Exception as e:
            print(f"❌ Error updating order book display: {e}")

    def start_ui_freeze_detector(self):
        """Start UI freeze detection to monitor responsiveness"""
        def check_ui_responsiveness():
            try:
                if not self.ui_freeze_detector_active:
                    return

                current_time = time.time()
                time_since_last_update = current_time - self.last_ui_update

                if time_since_last_update > 5.0:  # 5 seconds without UI update
                    print(f"⚠️ [FREEZE DETECTOR] UI may be frozen - {time_since_last_update:.1f}s since last update")
                elif time_since_last_update > 2.0:  # 2 seconds warning
                    print(f"🔄 [FREEZE DETECTOR] UI slow - {time_since_last_update:.1f}s since last update")

                # Update timestamp to show detector is working
                self.last_ui_update = current_time

                # Schedule next check
                self.root.after(1000, check_ui_responsiveness)  # Check every second

            except Exception as e:
                print(f"❌ [FREEZE DETECTOR] Error: {e}")

        # Start the detector
        print("🔍 [FREEZE DETECTOR] Starting UI freeze detection...")
        self.root.after(1000, check_ui_responsiveness)

    def create_trading_analysis(self, parent):
        """Create trading analysis and signals"""
        # Title
        title_label = tk.Label(
            parent,
            text="📈 TRADING ANALYSIS & SIGNALS",
            font=('Segoe UI', 14, 'bold'),
            bg=EXECUTIVE_COLORS['bg_primary'],
            fg=EXECUTIVE_COLORS['text_primary']
        )
        title_label.pack(pady=(10, 15))

        # Analysis container
        analysis_container = tk.Frame(parent, bg=EXECUTIVE_COLORS['bg_primary'])
        analysis_container.pack(fill=tk.BOTH, expand=True, padx=10)

        # Trading signals (top half)
        signals_frame = tk.Frame(analysis_container, bg=EXECUTIVE_COLORS['bg_primary'])
        signals_frame.pack(fill=tk.X, pady=(0, 10))

        self.trading_signals = TradingSignalsWidget(signals_frame, height=180)
        self.trading_signals.pack(fill=tk.BOTH, expand=True)

        # Market analytics (bottom half)
        analytics_frame = tk.Frame(analysis_container, bg=EXECUTIVE_COLORS['bg_primary'])
        analytics_frame.pack(fill=tk.BOTH, expand=True)

        self.market_analytics = MarketAnalyticsWidget(analytics_frame)
        self.market_analytics.pack(fill=tk.BOTH, expand=True)

    def create_charts_container(self, parent):
        """Create charts container"""
        # Title
        title_label = tk.Label(
            parent,
            text="📊 TRADING CHARTS",
            font=('Segoe UI', 14, 'bold'),
            bg=EXECUTIVE_COLORS['bg_primary'],
            fg=EXECUTIVE_COLORS['text_primary']
        )
        title_label.pack(pady=(10, 5))

        # Charts container
        charts_frame = tk.Frame(parent, bg=EXECUTIVE_COLORS['bg_primary'])
        charts_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.trading_charts = TradingChartsWidget(charts_frame)
        self.trading_charts.pack(fill=tk.BOTH, expand=True)

    def update_order_book_levels(self, bids, asks):
        """Update order book levels with green/red text"""
        # Clear existing data
        for widget in self.order_book_frame.winfo_children():
            widget.destroy()

        # Display asks (red, top to bottom - highest to lowest)
        if asks:
            # Header for asks
            ask_header = tk.Label(
                self.order_book_frame,
                text="🔴 ASKS (Sell Orders)",
                font=('Segoe UI', 11, 'bold'),
                bg=EXECUTIVE_COLORS['bg_card'],
                fg=EXECUTIVE_COLORS['danger']
            )
            ask_header.pack(fill=tk.X, pady=(5, 10))

            # Show asks in reverse order (highest price first)
            for i, (price, orders, qty) in enumerate(reversed(asks[:10])):
                ask_frame = tk.Frame(self.order_book_frame, bg=EXECUTIVE_COLORS['bg_card'])
                ask_frame.pack(fill=tk.X, pady=1)

                # Price (red)
                price_label = tk.Label(
                    ask_frame,
                    text=f"₹{price:.2f}",
                    font=('Segoe UI', 10, 'bold'),
                    bg=EXECUTIVE_COLORS['bg_card'],
                    fg=EXECUTIVE_COLORS['danger'],
                    width=12,
                    anchor='w'
                )
                price_label.pack(side=tk.LEFT, padx=(10, 5))

                # Quantity
                qty_label = tk.Label(
                    ask_frame,
                    text=f"{qty:,}",
                    font=('Segoe UI', 10),
                    bg=EXECUTIVE_COLORS['bg_card'],
                    fg=EXECUTIVE_COLORS['text_secondary'],
                    width=10,
                    anchor='e'
                )
                qty_label.pack(side=tk.RIGHT, padx=(5, 10))

                # Orders count
                orders_label = tk.Label(
                    ask_frame,
                    text=f"({orders})",
                    font=('Segoe UI', 9),
                    bg=EXECUTIVE_COLORS['bg_card'],
                    fg=EXECUTIVE_COLORS['text_muted'],
                    width=6,
                    anchor='center'
                )
                orders_label.pack(side=tk.RIGHT, padx=2)

        # Spread separator
        spread_frame = tk.Frame(self.order_book_frame, bg=EXECUTIVE_COLORS['bg_card'], height=30)
        spread_frame.pack(fill=tk.X, pady=10)

        if bids and asks:
            spread = asks[0][0] - bids[0][2]  # ask_price - bid_price
            spread_label = tk.Label(
                spread_frame,
                text=f"📊 SPREAD: ₹{spread:.2f}",
                font=('Segoe UI', 10, 'bold'),
                bg=EXECUTIVE_COLORS['bg_card'],
                fg=EXECUTIVE_COLORS['warning']
            )
            spread_label.pack(expand=True)

        # Display bids (green, top to bottom - highest to lowest)
        if bids:
            # Header for bids
            bid_header = tk.Label(
                self.order_book_frame,
                text="🟢 BIDS (Buy Orders)",
                font=('Segoe UI', 11, 'bold'),
                bg=EXECUTIVE_COLORS['bg_card'],
                fg=EXECUTIVE_COLORS['success']
            )
            bid_header.pack(fill=tk.X, pady=(10, 10))

            # Show bids (highest price first)
            for i, (qty, orders, price) in enumerate(bids[:10]):
                bid_frame = tk.Frame(self.order_book_frame, bg=EXECUTIVE_COLORS['bg_card'])
                bid_frame.pack(fill=tk.X, pady=1)

                # Price (green)
                price_label = tk.Label(
                    bid_frame,
                    text=f"₹{price:.2f}",
                    font=('Segoe UI', 10, 'bold'),
                    bg=EXECUTIVE_COLORS['bg_card'],
                    fg=EXECUTIVE_COLORS['success'],
                    width=12,
                    anchor='w'
                )
                price_label.pack(side=tk.LEFT, padx=(10, 5))

                # Quantity
                qty_label = tk.Label(
                    bid_frame,
                    text=f"{qty:,}",
                    font=('Segoe UI', 10),
                    bg=EXECUTIVE_COLORS['bg_card'],
                    fg=EXECUTIVE_COLORS['text_secondary'],
                    width=10,
                    anchor='e'
                )
                qty_label.pack(side=tk.RIGHT, padx=(5, 10))

                # Orders count
                orders_label = tk.Label(
                    bid_frame,
                    text=f"({orders})",
                    font=('Segoe UI', 9),
                    bg=EXECUTIVE_COLORS['bg_card'],
                    fg=EXECUTIVE_COLORS['text_muted'],
                    width=6,
                    anchor='center'
                )
                orders_label.pack(side=tk.RIGHT, padx=2)

    def reset_all_data(self):
        """Reset all data displays to empty state when switching data sources"""
        try:
            print("🔄 Resetting all data displays...")

            # Reset order book display safely
            if hasattr(self, 'order_book_frame') and self.order_book_frame is not None:
                try:
                    for widget in self.order_book_frame.winfo_children():
                        widget.destroy()
                except Exception as e:
                    print(f"⚠️ Error clearing order book frame: {e}")

                # Initialize with empty data
                try:
                    self.update_order_book_display([], [])
                except Exception as e:
                    print(f"⚠️ Error updating order book display: {e}")

            # Reset quantity bars safely
            try:
                if hasattr(self, 'bid_qty_label') and self.bid_qty_label is not None:
                    self.bid_qty_label.configure(text="0")
                if hasattr(self, 'ask_qty_label') and self.ask_qty_label is not None:
                    self.ask_qty_label.configure(text="0")
            except Exception as e:
                print(f"⚠️ Error resetting quantity labels: {e}")

            # Reset quantity bar proportions safely
            try:
                if hasattr(self, 'bid_portion') and self.bid_portion is not None:
                    self.bid_portion.pack_forget()
                if hasattr(self, 'ask_portion') and self.ask_portion is not None:
                    self.ask_portion.pack_forget()

                # Reset to equal proportions
                if hasattr(self, 'bid_portion') and self.bid_portion is not None:
                    self.bid_portion.pack(side=tk.LEFT, fill=tk.Y)
                if hasattr(self, 'ask_portion') and self.ask_portion is not None:
                    self.ask_portion.pack(side=tk.RIGHT, fill=tk.Y)
            except Exception as e:
                print(f"⚠️ Error resetting quantity bar proportions: {e}")

            # Reset charts - REMOVED FOR ORDER FLOW ANALYSIS IMPLEMENTATION
            # if hasattr(self, 'trading_charts') and self.trading_charts is not None:
            #     try:
            #         # Check if reset_charts method exists
            #         if hasattr(self.trading_charts, 'reset_charts'):
            #             self.trading_charts.reset_charts()
            #         else:
            #             print("📊 Charts reset method not available - skipping chart reset")
            #     except Exception as e:
            #         print(f"⚠️ Error resetting charts: {e}")

            # Reset live data storage
            self.live_order_book_data = {'bids': [], 'asks': []}

            print("✅ All data displays reset successfully")

        except Exception as e:
            print(f"⚠️ Error resetting data displays: {e}")
            import traceback
            traceback.print_exc()

    def set_data_source(self, source):
        """Set data source and update button states"""
        print(f"🔄 Switching data source to: {source}")

        # Button state management is now handled by header UI
        print("🔄 Data source transition initiated")

        # Stop current connections first
        if hasattr(self, 'ws_connected') and self.ws_connected:
            print("🔌 Disconnecting current WebSocket...")
            self.disconnect_websocket()
            time.sleep(0.5)  # Brief pause for clean disconnection

        # Reset all data before switching
        self.reset_all_data()

        # Set new data source
        self.data_source = source

        # Update button states and connect in background thread
        def switch_source():
            try:
                if source == 'live':
                    print("📡 Switching to live mode...")
                    self.root.after(0, lambda: [
                        self.header_ui.update_connection_status("● CONNECTING...", '#ffaa00') if self.header_ui else None,
                        self.header_ui.update_market_condition_visibility(False) if self.header_ui else None
                    ])

                    # Connect to WebSocket in background
                    self.connect_websocket()

                elif source == 'simulated':
                    print("🎯 Switching to simulated mode...")
                    self.root.after(0, lambda: [
                        self.header_ui.update_connection_status("● SIMULATED DATA", '#0099ff') if self.header_ui else None,
                        self.header_ui.update_market_condition_visibility(True) if self.header_ui else None
                    ])

                else:  # disconnected
                    print("❌ Switching to disconnected mode...")
                    def update_disconnected_ui():
                        if self.header_ui:
                            self.header_ui.update_connection_status("● DISCONNECTED (No Data)", '#ff0844')
                            self.header_ui.update_market_condition_visibility(False)

                        # Reset metrics when disconnected
                        if hasattr(self, 'metrics_label'):
                            self.metrics_label.configure(text="VOL: -- | SPREAD: -- | LAST: --")

                    self.root.after(0, update_disconnected_ui)

                # Button state management is now handled by header UI

                print(f"✅ Data source switched to: {source}")

            except Exception as e:
                print(f"❌ Error switching data source: {e}")
                # Button state management is now handled by header UI

        # Run switch in background thread to prevent UI blocking
        switch_thread = threading.Thread(target=switch_source, daemon=True)
        switch_thread.start()

    def connect_websocket(self):
        """Connect to Smart API WebSocket for live market data"""
        try:
            # Check if we should stop
            if self.stop_all_functions:
                print("🛑 WebSocket connection cancelled - stopping all functions")
                return

            # Prevent multiple simultaneous connections
            if self.ws_connecting:
                print("⚠️ WebSocket connection already in progress...")
                return

            if self.ws_connected:
                print("⚠️ WebSocket already connected")
                return

            self.ws_connecting = True
            print("🔌 Connecting to Smart API WebSocket...")

            # Update UI to show connecting status
            if self.header_ui:
                self.root.after(0, lambda: self.header_ui.update_connection_status("● CONNECTING...", '#ffaa00'))

            # Initialize Smart API connection
            if not self.initialize_smart_api():
                print("❌ Smart API initialization failed")
                self.ws_connecting = False
                if self.header_ui:
                    self.root.after(0, lambda: self.header_ui.update_connection_status("● CONNECTION FAILED", '#ff0844'))
                return

            # Start Smart API WebSocket
            if not self.start_smart_api_websocket():
                print("❌ Smart API WebSocket connection failed")
                self.ws_connecting = False
                if self.header_ui:
                    self.root.after(0, lambda: self.header_ui.update_connection_status("● CONNECTION FAILED", '#ff0844'))
                return

            self.ws_connecting = False

        except Exception as e:
            print(f"❌ WebSocket connection failed: {e}")
            self.ws_connecting = False
            if self.header_ui:
                self.root.after(0, lambda: self.header_ui.update_connection_status("● CONNECTION ERROR", '#ff0844'))

    def initialize_smart_api(self):
        """Initialize Smart API connection with proper authentication"""
        try:
            # Import Smart API
            try:
                from SmartApi import SmartConnect
            except ImportError:
                print("❌ SmartApi package not found")
                print("📦 Install with: pip install SmartApi")
                return False

            print(f"🔐 Connecting to Smart API with API key: {self.api_credentials['api_key']}")
            self.smart_api = SmartConnect(api_key=self.api_credentials['api_key'])

            # Generate TOTP code
            try:
                import pyotp
                totp_secret = self.api_credentials['totp_secret']
                totp = pyotp.TOTP(totp_secret)
                totp_code = totp.now()
                print(f"🔑 Generated TOTP code: {totp_code}")
            except ImportError:
                print("❌ pyotp package not found")
                print("📦 Install with: pip install pyotp")
                return False
            except Exception as e:
                print(f"❌ TOTP generation failed: {e}")
                return False

            # Login to Smart API
            print("🔐 Logging into Smart API...")
            login_response = self.smart_api.generateSession(
                self.api_credentials['client_code'],
                self.api_credentials['password'],
                totp_code
            )

            if not login_response.get('status'):
                print(f"❌ Smart API login failed: {login_response.get('message', 'Unknown error')}")
                return False

            print("✅ Smart API login successful!")

            # Store session data
            session_data = login_response.get('data', {})
            self.smart_api.auth_token = session_data.get('jwtToken', '')
            self.smart_api.feed_token = session_data.get('feedToken', '')
            self.smart_api.userId = self.api_credentials['client_code']

            print(f"📊 Auth Token: {self.smart_api.auth_token[:20]}...")
            print(f"📡 Feed Token: {self.smart_api.feed_token[:20]}...")

            # Search for current stock token
            search_symbol = self.current_symbol.replace('-EQ', '')  # Remove -EQ for search
            print(f"🔍 Searching for {search_symbol} token...")
            search_results = self.smart_api.searchScrip("NSE", search_symbol)

            if search_results.get('status') and search_results.get('data'):
                print(f"📋 Found {len(search_results['data'])} {search_symbol} symbols:")
                for i, result in enumerate(search_results['data'], 1):
                    symbol = result.get('tradingsymbol', result.get('symbol', 'Unknown'))
                    token = result.get('symboltoken')
                    print(f"  {i}. {symbol} (Token: {token})")

                    # Look for the equity version (ending with -EQ)
                    if symbol == self.current_symbol:
                        self.cgcl_token = token
                        self.current_token = token
                        print(f"✅ Found {self.current_symbol} token: {self.cgcl_token}")
                        break

                if not self.cgcl_token:
                    print(f"❌ {self.current_symbol} token not found in search results")
                    print("🔍 Available symbols:", [r.get('tradingsymbol', r.get('symbol')) for r in search_results['data']])
                    return False
                else:
                    # Get previous day's closing price for percentage calculation
                    self.get_previous_close_price()

                    # Stock database is now handled by the separate stock search widget
                    print("✅ Stock search uses local database - no background loading needed")

                    return True
            else:
                print(f"❌ {search_symbol} search failed")
                return False

            return True

        except Exception as e:
            print(f"❌ Smart API initialization failed: {e}")
            return False

    def start_smart_api_websocket(self):
        """Start Smart API WebSocket using official SmartWebSocketV2"""
        try:
            # Import Smart API WebSocket
            try:
                from SmartApi.smartWebSocketV2 import SmartWebSocketV2
            except ImportError:
                print("❌ SmartWebSocketV2 not found")
                print("📦 Update SmartApi package: pip install --upgrade SmartApi")
                return False

            print("🚀 Starting Smart API WebSocket...")

            # Create WebSocket connection
            self.sws = SmartWebSocketV2(
                self.smart_api.auth_token,
                self.smart_api.api_key,
                self.smart_api.userId,
                self.smart_api.feed_token
            )

            def on_data(ws, message):
                try:
                    self.handle_smart_api_data(message)
                except Exception as e:
                    print(f"❌ Error handling WebSocket data: {e}")

            def on_open(ws):
                print("✅ Smart API WebSocket connected!")
                self.ws_connected = True
                self.ws_connecting = False  # Clear connecting flag

                # Update connection status in UI
                if self.header_ui:
                    self.root.after(0, lambda: self.header_ui.update_connection_status(
                        "● LIVE FEED (WebSocket Connected)", '#00ff41'
                    ))

                # Button state management is now handled by header UI

                # Subscribe to CGCL with QUOTE and DEPTH modes only
                try:
                    token_list = [{"exchangeType": 1, "tokens": [self.cgcl_token]}]

                    # Subscribe to QUOTE mode for LTP, OHLC data
                    clean_symbol = self.current_symbol.replace('-EQ', '')
                    print(f"📊 Subscribing to {clean_symbol} QUOTE mode (Token: {self.cgcl_token})")
                    self.sws.subscribe(f"{clean_symbol.lower()}_quote_001", 2, token_list)  # Mode 2 = QUOTE
                    print(f"✅ {clean_symbol} QUOTE subscription sent!")

                    # Subscribe to DEPTH mode for order book (if available)
                    print(f"📊 Subscribing to {clean_symbol} DEPTH mode (Token: {self.cgcl_token})")
                    self.sws.subscribe(f"{clean_symbol.lower()}_depth_001", 4, token_list)  # Mode 4 = DEPTH
                    print(f"✅ {clean_symbol} DEPTH subscription sent!")

                    print("🎯 Using DEPTH + LTP-based order book generation")

                except Exception as e:
                    clean_symbol = self.current_symbol.replace('-EQ', '')
                    print(f"❌ Error subscribing to {clean_symbol}: {e}")

            def on_error(ws, error):
                print(f"❌ WebSocket error: {error}")
                self.ws_connected = False
                self.ws_connecting = False
                # Update UI to show disconnected status and re-enable buttons
                def update_error_ui():
                    if self.header_ui:
                        self.header_ui.update_connection_status("● DISCONNECTED (WebSocket Error)", '#ff0844')
                    # Re-enable buttons (these will be managed by header UI in future)

                self.root.after(0, update_error_ui)

            def on_close(ws, close_status_code, close_msg):
                print("🔌 WebSocket connection closed")
                self.ws_connected = False
                self.ws_connecting = False
                # Update UI to show disconnected status and re-enable buttons
                def update_closed_ui():
                    if self.header_ui:
                        self.header_ui.update_connection_status("● DISCONNECTED (WebSocket Closed)", '#ff0844')
                    # Re-enable buttons (these will be managed by header UI in future)

                self.root.after(0, update_closed_ui)

            # Set callbacks
            self.sws.on_open = on_open
            self.sws.on_data = on_data
            self.sws.on_error = on_error
            self.sws.on_close = on_close

            # Connect
            self.sws.connect()

            return True

        except Exception as e:
            print(f"❌ Smart API WebSocket failed: {e}")
            return False

    def handle_smart_api_data(self, message):
        """Handle incoming Smart API WebSocket data"""
        try:
            if isinstance(message, dict) and 'token' in message and str(message['token']) == str(self.cgcl_token):
                subscription_mode = message.get('subscription_mode', 0)
                mode_name = {1: 'LTP', 2: 'QUOTE', 4: 'DEPTH'}.get(subscription_mode, 'UNKNOWN')
                clean_symbol = self.current_symbol.replace('-EQ', '')

                # CRITICAL FIX: Throttled UI updates to prevent main thread overload
                if subscription_mode == 4:  # DEPTH mode (20 levels if available)
                    self.latest_depth_data = message
                    self.schedule_throttled_depth_update()
                elif subscription_mode == 2:  # QUOTE mode (LTP for order book generation)
                    self.latest_quote_data = message
                    self.schedule_throttled_quote_update()
            else:
                print(f"🔍 [DEBUG] Message ignored - token mismatch or invalid format")

        except Exception as e:
            print(f"❌ Error parsing Smart API data: {e}")
            import traceback
            traceback.print_exc()



    def schedule_throttled_quote_update(self):
        """Schedule throttled QUOTE update to prevent main thread overload"""
        if not self.quote_update_pending:
            self.quote_update_pending = True
            # Use after() with 100ms delay instead of after_idle() to ensure execution
            self.root.after(100, self.process_throttled_quote_update)

    def schedule_throttled_depth_update(self):
        """Schedule throttled DEPTH update to prevent main thread overload"""
        if not self.depth_update_pending:
            self.depth_update_pending = True
            # Use after() with 100ms delay instead of after_idle() to ensure execution
            self.root.after(100, self.process_throttled_depth_update)

    def process_throttled_quote_update(self):
        """Process the latest QUOTE data on main thread"""
        try:
            self.quote_update_pending = False

            # Check if we should stop processing
            if self.stop_all_functions:
                return

            if self.latest_quote_data:
                self.handle_quote_mode_data(self.latest_quote_data)

        except Exception as e:
            print(f"❌ [DEBUG] Error in throttled QUOTE processing: {e}")
            self.quote_update_pending = False

    def process_throttled_depth_update(self):
        """Process the latest DEPTH data on main thread"""
        try:
            self.depth_update_pending = False

            # Check if we should stop processing
            if self.stop_all_functions:
                return

            if self.latest_depth_data:
                self.handle_depth_mode_data(self.latest_depth_data)

        except Exception as e:
            print(f"❌ [DEBUG] Error in throttled DEPTH processing: {e}")
            self.depth_update_pending = False

    def handle_quote_mode_data(self, message):
        """Handle QUOTE mode data (LTP, OHLC, Volume) - Smart API Format"""
        try:
            print("📊 [DEBUG] Processing QUOTE mode data...")

            # Extract price data using Smart API format (prices are in paise, divide by 100)
            ltp = float(message.get('last_traded_price', 0)) / 100
            open_price = float(message.get('open_price_of_the_day', 0)) / 100
            high_price = float(message.get('high_price_of_the_day', 0)) / 100
            low_price = float(message.get('low_price_of_the_day', 0)) / 100
            close_price = float(message.get('closed_price', 0)) / 100
            volume = int(message.get('volume_trade_for_the_day', 0))

            print(f"💰 Live LTP: ₹{ltp:.2f}")
            print(f"📊 Live OHLC: O:₹{open_price:.2f} H:₹{high_price:.2f} L:₹{low_price:.2f} C:₹{close_price:.2f}")
            print(f"📈 Live Volume: {volume:,}")

            # Store current LTP for depth data validation
            self.current_ltp = ltp
            print(f"DEBUG: Set current_ltp to Rs{ltp:.2f} for depth validation")

            # Check if we have valid data
            if ltp == 0 and open_price == 0:
                print("⚠️ WebSocket data invalid, using HTTP fallback for price")
                self.use_http_fallback_for_live_data()
                return

            # Update UI with live quote data
            def update_ui():
                try:
                    # Update enhanced price display with OHLC and volume data
                    ohlc_data = {
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price
                    }
                    self.update_price_display(ltp, ohlc_data, volume)

                    # Update connection status to show WebSocket data
                    if self.header_ui:
                        self.header_ui.update_connection_status(
                            "● LIVE FEED (WebSocket Real-time)", '#00ff41'
                        )

                    # Update spread in the enhanced status bar
                    if hasattr(self, 'spread_label'):
                        # Calculate spread from high-low
                        spread = abs(high_price - low_price)
                        self.spread_label.configure(text=f"₹{spread:.2f}")

                    # Update analytics - REMOVED FOR ORDER FLOW ANALYSIS IMPLEMENTATION
                    # if self.market_analytics:
                    #     analytics_data = {
                    #         'volume': f"{volume:,}",
                    #         'spread': abs(high_price - low_price),
                    #         'change': ltp - close_price,
                    #         'change_percent': ((ltp - close_price) / close_price) * 100 if close_price > 0 else 0
                    #     }
                    #     self.market_analytics.update_analytics(analytics_data)

                    # Update charts - REMOVED FOR ORDER FLOW ANALYSIS IMPLEMENTATION
                    # if self.trading_charts:
                    #     self.chart_update_counter += 1
                    #     print(f"🔄 Chart counter: {self.chart_update_counter}, LTP: ₹{ltp:.2f}")
                    #     if self.chart_update_counter % 3 == 0:  # Update every 3rd message for better responsiveness
                    #         try:
                    #             chart_data = {
                    #                 'timestamp': time.time(),
                    #                 'open': open_price,
                    #                 'high': high_price,
                    #                 'low': low_price,
                    #                 'close': ltp,
                    #                 'volume': volume
                    #             }
                    #             self.trading_charts.add_new_data(chart_data, volume)
                    #             print(f"📊 Chart updated successfully with LTP: ₹{ltp:.2f}, Volume: {volume:,}")
                    #             print(f"📈 Chart data sent: {chart_data}")
                    #         except Exception as chart_error:
                    #             print(f"⚠️ Chart update error: {chart_error}")
                    #             import traceback
                    #             traceback.print_exc()
                    else:
                        print(f"⚠️ No trading_charts object available for chart update")

                except Exception as e:
                    print(f"⚠️ UI update error in quote mode: {e}")

            self.root.after(0, update_ui)

            # Generate LTP-based order book (every few updates to avoid spam)
            if not hasattr(self, 'ltp_update_counter'):
                self.ltp_update_counter = 0
            self.ltp_update_counter += 1

            # Update order book every 3rd LTP update for better performance
            if self.ltp_update_counter % 3 == 0:
                self.generate_ltp_based_order_book()

        except Exception as e:
            print(f"❌ Error handling quote data: {e}")

    def use_http_fallback_for_live_data(self):
        """Use HTTP API data as fallback when WebSocket data is invalid"""
        try:
            if not hasattr(self, 'http_fallback_counter'):
                self.http_fallback_counter = 0

            self.http_fallback_counter += 1

            # Only run HTTP fallback every 30 seconds to avoid spam
            if self.http_fallback_counter % 30 == 0:
                print("🔄 Running HTTP fallback for live data...")

                def run_http_fallback():
                    try:
                        if self.smart_api and self.cgcl_token:
                            # Get latest HTTP data
                            ltp_response = self.smart_api.ltpData("NSE", "CGCL-EQ", self.cgcl_token)

                            if ltp_response.get('status') and ltp_response.get('data'):
                                ltp_data = ltp_response['data']
                                current_price = float(ltp_data.get('ltp', 178.20))

                                # Update price display with HTTP data
                                def update_http_ui():
                                    self.update_price_display(current_price)
                                    # Update connection status to show HTTP fallback
                                    if self.header_ui:
                                        self.header_ui.update_connection_status(
                                            "● LIVE FEED (HTTP Fallback)", '#ffaa00'
                                        )

                                self.root.after(0, update_http_ui)

                                # Generate simulated order book based on current price
                                self.generate_live_order_book_from_price(current_price)

                                print(f"✅ HTTP fallback updated price to ₹{current_price}")
                    except Exception as e:
                        print(f"⚠️ HTTP fallback error: {e}")

                # Run in background thread
                fallback_thread = threading.Thread(target=run_http_fallback, daemon=True)
                fallback_thread.start()

        except Exception as e:
            print(f"❌ HTTP fallback failed: {e}")

    def generate_ltp_based_order_book(self):
        """Generate complete 20-level order book based on LTP from QUOTE mode"""
        try:
            if not hasattr(self, 'current_ltp') or self.current_ltp <= 0:
                print("❌ No valid LTP available for order book generation")
                return

            ltp = self.current_ltp
            print(f"📊 Generating 20-level order book based on LTP: ₹{ltp:.2f}")

            # Calculate dynamic tick size (0.5% to 1.5% of LTP)
            base_tick = ltp * 0.001  # 0.1% of LTP as base tick
            tick_size = max(0.01, min(0.50, base_tick))  # Between 1 paisa and 50 paisa

            print(f"📊 Using tick size: ₹{tick_size:.3f} for order book generation")

            # Generate 20 bid levels (decreasing prices from LTP)
            bids = []
            for i in range(20):
                price = ltp - (tick_size * (i + 1))
                qty = random.randint(100, 2000)  # Realistic quantities
                orders = random.randint(1, 8)
                bids.append([qty, orders, price, 'LTP_GENERATED'])

            # Generate 20 ask levels (increasing prices from LTP)
            asks = []
            for i in range(20):
                price = ltp + (tick_size * (i + 1))
                qty = random.randint(100, 2000)  # Realistic quantities
                orders = random.randint(1, 8)
                asks.append([price, orders, qty, 'LTP_GENERATED'])

            print(f"✅ Generated 20-level order book: Bids ₹{bids[0][2]:.2f} to ₹{bids[-1][2]:.2f}, Asks ₹{asks[0][0]:.2f} to ₹{asks[-1][0]:.2f}")

            # Update the UI with the generated order book
            def update_complete_ui():
                print("🔄 [UI] Updating complete UI: order book + quantity bars")
                self.update_order_book_display(bids, asks)
                self.update_quantity_bars(bids, asks)
                print("✅ [UI] Complete UI update finished")

            self.root.after(0, update_complete_ui)

        except Exception as e:
            print(f"❌ Error generating LTP-based order book: {e}")
            import traceback
            traceback.print_exc()

    def extend_order_book_levels(self, bids, asks, ltp):
        """Extend order book to 20 levels if we don't have enough real data"""
        try:
            # Extend bids (buy orders) - prices decrease as we go further from LTP
            while len(bids) < 20:
                last_bid_price = bids[-1][2] if bids else ltp - 0.05
                new_price = last_bid_price - 0.05  # 5 paisa step
                new_qty = random.randint(50, 800)  # Smaller quantities for deeper levels
                new_orders = random.randint(1, 4)
                bids.append([new_qty, new_orders, new_price])

            # Extend asks (sell orders) - prices increase as we go further from LTP
            while len(asks) < 20:
                last_ask_price = asks[-1][0] if asks else ltp + 0.05
                new_price = last_ask_price + 0.05  # 5 paisa step
                new_qty = random.randint(50, 800)  # Smaller quantities for deeper levels
                new_orders = random.randint(1, 4)
                asks.append([new_price, new_orders, new_qty])

        except Exception as e:
            print(f"❌ Error extending order book levels: {e}")

    def fetch_real_order_book_http(self):
        """Try to fetch real order book data via Smart API HTTP endpoint"""
        try:
            if not hasattr(self, 'smart_api') or not self.smart_api:
                print("❌ Smart API not available for HTTP order book fetch")
                return

            print("🔄 Attempting to fetch order book via HTTP API...")

            # Try to get market depth via HTTP
            # Note: Smart API might not have a direct order book endpoint
            # This is a placeholder for when such an endpoint becomes available

            print("⚠️ HTTP order book endpoint not available in Smart API")
            print("� NOTE: Smart API's WebSocket depth data appears to be cached/stale")
            print("💡 SUGGESTION: Consider using NSE/BSE direct feeds or other data providers for real-time order book")
            print("�🔄 Falling back to realistic order book generation based on LTP")

            # Fallback to generating realistic order book
            current_ltp = getattr(self, 'current_ltp', 178.0)
            self.generate_live_order_book_from_price(current_ltp)

        except Exception as e:
            print(f"❌ Error fetching HTTP order book: {e}")
            # Fallback to generating realistic order book
            current_ltp = getattr(self, 'current_ltp', 178.0)
            self.generate_live_order_book_from_price(current_ltp)

    def generate_live_order_book_from_price(self, current_price):
        """Generate realistic order book data based on current price"""
        try:
            import random

            # Generate realistic bid/ask spread
            spread = random.uniform(0.05, 0.20)
            best_bid = current_price - (spread / 2)
            best_ask = current_price + (spread / 2)

            # Generate 20 levels of bids and asks
            bids = []
            asks = []

            for i in range(20):
                # Bids (decreasing prices)
                bid_price = best_bid - (i * random.uniform(0.05, 0.15))
                bid_qty = random.randint(50, 3000)  # Smaller quantities for deeper levels
                bids.append([bid_qty, 1, bid_price, "GENERATED"])

                # Asks (increasing prices)
                ask_price = best_ask + (i * random.uniform(0.05, 0.15))
                ask_qty = random.randint(50, 3000)  # Smaller quantities for deeper levels
                asks.append([ask_price, 1, ask_qty, "GENERATED"])

            # Update live order book data
            self.live_order_book_data = {'bids': bids, 'asks': asks}

            # Update UI
            def update_order_book_ui():
                try:
                    if hasattr(self, 'order_book_frame'):
                        self.update_order_book_display(bids, asks)
                        self.update_quantity_bars(bids, asks)
                        print(f"📊 Generated live order book: {len(bids)} bids, {len(asks)} asks")
                except Exception as e:
                    print(f"⚠️ Order book UI update error: {e}")

            self.root.after(0, update_order_book_ui)

        except Exception as e:
            print(f"❌ Error generating live order book: {e}")

    def update_price_display(self, price, ohlc_data=None, volume=None):
        """Update enhanced price display with change calculation and market data"""
        try:
            if hasattr(self, 'main_price_label'):
                # Update main price
                self.main_price_label.configure(text=f"₹{price:.2f}", text_color='white')

                # Calculate and display price change based on mode
                if hasattr(self, 'change_label'):
                    if self.data_source == 'live' and hasattr(self, 'previous_close_price') and self.previous_close_price:
                        # Live mode: Calculate change from previous day's closing price
                        change = price - self.previous_close_price
                        change_pct = (change / self.previous_close_price) * 100

                        # Color coding for change
                        if change > 0:
                            change_color = "#00ff41"
                            change_text = f"+{change:.2f} (+{change_pct:.2f}%)"
                        elif change < 0:
                            change_color = "#ff4444"
                            change_text = f"{change:.2f} ({change_pct:.2f}%)"
                        else:
                            change_color = "#888888"
                            change_text = "0.00 (0.00%)"

                        self.change_label.configure(text=change_text, text_color=change_color)

                    elif self.data_source == 'simulated' and hasattr(self, 'previous_price') and self.previous_price:
                        # Simulated mode: Calculate change from previous tick (for demo purposes)
                        change = price - self.previous_price
                        change_pct = (change / self.previous_price) * 100

                        # Color coding for change
                        if change > 0:
                            change_color = "#00ff41"
                            change_text = f"+{change:.2f} (+{change_pct:.2f}%)"
                        elif change < 0:
                            change_color = "#ff4444"
                            change_text = f"{change:.2f} ({change_pct:.2f}%)"
                        else:
                            change_color = "#888888"
                            change_text = "0.00 (0.00%)"

                        self.change_label.configure(text=change_text, text_color=change_color)
                    else:
                        # No reference price available
                        self.change_label.configure(text="0.00 (0.00%)", text_color="#888888")

                # Update OHLC data if provided
                if ohlc_data:
                    if hasattr(self, 'high_label'):
                        self.high_label.configure(text=f"₹{ohlc_data.get('high', 0):.2f}")
                    if hasattr(self, 'low_label'):
                        self.low_label.configure(text=f"₹{ohlc_data.get('low', 0):.2f}")

                # Update volume if provided
                if volume and hasattr(self, 'volume_label'):
                    if volume >= 1000000:
                        vol_text = f"{volume/1000000:.1f}M"
                    elif volume >= 1000:
                        vol_text = f"{volume/1000:.0f}K"
                    else:
                        vol_text = str(volume)
                    self.volume_label.configure(text=vol_text)

                # Update timestamp
                import datetime
                current_time = datetime.datetime.now().strftime("%H:%M:%S")
                if hasattr(self, 'last_update_label'):
                    self.last_update_label.configure(text=current_time)

                # Calculate and update VWAP (simplified calculation)
                if hasattr(self, 'vwap_label') and ohlc_data:
                    # Simple VWAP approximation using OHLC
                    vwap = (ohlc_data.get('open', price) + ohlc_data.get('high', price) +
                           ohlc_data.get('low', price) + price) / 4
                    self.vwap_label.configure(text=f"₹{vwap:.2f}")

                # Update market session status
                if hasattr(self, 'session_label'):
                    current_hour = datetime.datetime.now().hour
                    if 9 <= current_hour < 15:  # Market hours 9:15 AM to 3:30 PM
                        if current_hour == 9 or (current_hour == 15 and datetime.datetime.now().minute <= 30):
                            session_text = "OPEN"
                            session_color = "#00ff41"
                        else:
                            session_text = "OPEN"
                            session_color = "#00ff41"
                    else:
                        session_text = "CLOSED"
                        session_color = "#ff4444"
                    self.session_label.configure(text=session_text, text_color=session_color)

                # Update volatility tracking for intelligent spread analysis
                self.update_volatility_tracking(price)

                # Store current price for next change calculation
                self.previous_price = price

        except Exception as e:
            print(f"⚠️ Enhanced price display update error: {e}")

    def update_volatility_tracking(self, price):
        """Update volatility tracking for intelligent spread analysis"""
        try:
            # Add current price to history
            self.price_history.append(price)

            # Keep only last 50 prices for volatility calculation
            if len(self.price_history) > 50:
                self.price_history = self.price_history[-50:]

            # Calculate volatility factor if we have enough data
            if len(self.price_history) >= 10:
                # Calculate recent price changes
                price_changes = []
                for i in range(1, len(self.price_history)):
                    change_pct = abs((self.price_history[i] - self.price_history[i-1]) / self.price_history[i-1]) * 100
                    price_changes.append(change_pct)

                # Calculate average volatility
                avg_volatility = sum(price_changes) / len(price_changes)

                # Normalize volatility factor (0.5 to 2.0 range)
                # Low volatility (< 0.1%) = 0.5, High volatility (> 1%) = 2.0
                if avg_volatility < 0.1:
                    self.recent_volatility_factor = 0.5
                elif avg_volatility > 1.0:
                    self.recent_volatility_factor = 2.0
                else:
                    # Linear interpolation between 0.5 and 2.0
                    self.recent_volatility_factor = 0.5 + (avg_volatility / 1.0) * 1.5

                # Debug info (can be removed in production)
                if len(self.price_history) % 20 == 0:  # Print every 20 updates
                    print(f"📊 Volatility Factor: {self.recent_volatility_factor:.2f} (Avg Change: {avg_volatility:.3f}%)")

        except Exception as e:
            print(f"⚠️ Volatility tracking error: {e}")
            self.recent_volatility_factor = 1.0  # Default fallback

    def get_previous_close_price(self):
        """Get previous day's closing price for percentage calculation"""
        try:
            print("📊 Fetching previous day's closing price...")

            # Use Smart API's LTP endpoint to get current and previous data
            ltp_data = self.smart_api.ltpData("NSE", self.current_symbol, self.cgcl_token)

            if ltp_data.get('status') and ltp_data.get('data'):
                data = ltp_data['data']

                # Get previous close from the API response
                prev_close = data.get('close', 0)  # Previous day's closing price
                current_ltp = data.get('ltp', 0)   # Current LTP

                if prev_close > 0:
                    self.previous_close_price = prev_close
                    print(f"📊 Previous Close: ₹{prev_close:.2f}")
                    print(f"💰 Current LTP: ₹{current_ltp:.2f}")

                    # Calculate initial change
                    if current_ltp > 0:
                        change = current_ltp - prev_close
                        change_pct = (change / prev_close) * 100
                        print(f"📈 Initial Change: ₹{change:.2f} ({change_pct:+.2f}%)")
                else:
                    print("⚠️ Previous close price not available in API response")
                    self.previous_close_price = None
            else:
                print(f"❌ Failed to get LTP data: {ltp_data.get('message', 'Unknown error')}")
                self.previous_close_price = None

        except Exception as e:
            print(f"⚠️ Error fetching previous close price: {e}")
            self.previous_close_price = None



    def select_stock(self, symbol, token):
        """Select a new stock by restarting the application with the selected stock"""
        try:
            print(f"🔄 [RESTART SWITCH] Restarting application with: {symbol} (Token: {token})")

            # Save the selected stock to a file for the restart
            self.save_selected_stock_for_restart(symbol, token)

            # Show user notification
            if hasattr(self, 'stock_search_widget'):
                try:
                    self.stock_search_widget.status_label.configure(
                        text=f"🔄 Restarting with {symbol}...",
                        text_color="#FFA500"
                    )
                except:
                    pass

            # Schedule application restart
            print(f"🚀 [RESTART SWITCH] Scheduling application restart in 1 second...")
            self.root.after(1000, lambda: self.restart_application_with_stock(symbol, token))

        except Exception as e:
            print(f"❌ [RESTART SWITCH] Error selecting stock: {e}")
            import traceback
            traceback.print_exc()

    def save_selected_stock_for_restart(self, symbol, token):
        """Save selected stock info to file for restart"""
        try:
            import json
            import os

            restart_data = {
                'symbol': symbol,
                'token': token,
                'data_source': getattr(self, 'data_source', 'live'),
                'timestamp': time.time()
            }

            restart_file = os.path.join(os.path.dirname(__file__), 'restart_stock.json')
            with open(restart_file, 'w') as f:
                json.dump(restart_data, f)

            print(f"💾 [RESTART] Saved restart data: {symbol} (Token: {token})")

        except Exception as e:
            print(f"❌ [RESTART] Error saving restart data: {e}")

    def restart_application_with_stock(self, symbol, token):
        """Restart the application with the selected stock and close old window"""
        try:
            print(f"🔄 [RESTART] Closing current application...")

            # Disconnect all connections cleanly
            self.disconnect_all_websockets()

            # Start new instance FIRST before closing current window
            print(f"🚀 [RESTART] Starting new instance with {symbol}...")

            import subprocess
            import sys
            import os

            # Get the current script path
            script_path = os.path.abspath(__file__)

            # Start new instance in a new console window
            subprocess.Popen([sys.executable, script_path],
                           cwd=os.path.dirname(script_path),
                           creationflags=subprocess.CREATE_NEW_CONSOLE if sys.platform == 'win32' else 0)

            print(f"✅ [RESTART] New instance started with {symbol}")

            # Give the new instance a moment to start
            time.sleep(0.5)

            # Now close the current window
            print(f"🗑️ [RESTART] Closing old window...")
            self.root.quit()
            self.root.destroy()

        except Exception as e:
            print(f"❌ [RESTART] Error restarting application: {e}")
            import traceback
            traceback.print_exc()

    def load_restart_stock_if_exists(self):
        """Load restart stock data if it exists"""
        try:
            import json
            import os

            restart_file = os.path.join(os.path.dirname(__file__), 'restart_stock.json')

            if os.path.exists(restart_file):
                with open(restart_file, 'r') as f:
                    restart_data = json.load(f)

                # Check if restart data is recent (within 30 seconds)
                if time.time() - restart_data.get('timestamp', 0) < 30:
                    symbol = restart_data.get('symbol')
                    token = restart_data.get('token')
                    data_source = restart_data.get('data_source', 'live')

                    if symbol and token:
                        print(f"🔄 [RESTART] Loading from restart: {symbol} (Token: {token})")

                        # Set the stock data BEFORE UI creation
                        self.current_symbol = symbol
                        self.current_token = token
                        self.cgcl_token = token
                        self.data_source = data_source

                        print(f"✅ [RESTART] Stock data set: {self.current_symbol} (Token: {self.current_token})")

                        # Set restart flag for UI updates
                        self._restart_loaded = True

                        # Clean up restart file
                        os.remove(restart_file)

                        print(f"✅ [RESTART] Successfully loaded {symbol}")
                        return True
                else:
                    # Remove old restart file
                    os.remove(restart_file)
                    print("🗑️ [RESTART] Removed old restart file")

        except Exception as e:
            print(f"❌ [RESTART] Error loading restart data: {e}")

        return False

    def disconnect_all_websockets(self):
        """STEP 1: Disconnect all WebSocket connections completely"""
        try:
            print("🔌 [DISCONNECT] Stopping all WebSocket connections...")

            # Stop all functions first
            self.stop_all_functions = True

            # Clear any pending updates
            self.quote_update_pending = False
            self.depth_update_pending = False
            self.latest_quote_data = None
            self.latest_depth_data = None

            # Disconnect WebSocket connections
            self.ws_connected = False
            self.ws_connecting = False

            # Close Smart API WebSocket
            if hasattr(self, 'sws') and self.sws:
                try:
                    self.sws.close_connection()
                    print("✅ [DISCONNECT] Smart API WebSocket closed")
                except Exception as e:
                    print(f"⚠️ [DISCONNECT] Error closing Smart API WebSocket: {e}")
                finally:
                    self.sws = None

            # Close regular WebSocket
            if hasattr(self, 'ws') and self.ws:
                try:
                    self.ws.close()
                    print("✅ [DISCONNECT] Regular WebSocket closed")
                except Exception as e:
                    print(f"⚠️ [DISCONNECT] Error closing regular WebSocket: {e}")
                finally:
                    self.ws = None

            # Update connection status in UI
            if self.header_ui:
                self.header_ui.update_connection_status("● DISCONNECTED", '#FF6B6B')

            print("✅ [DISCONNECT] All WebSocket connections closed")

        except Exception as e:
            print(f"❌ [DISCONNECT] Error disconnecting WebSockets: {e}")

    def reset_ui_for_new_stock(self, symbol, token):
        """STEP 2: Reset UI and prepare for new stock"""
        try:
            print(f"🔄 [UI RESET] Preparing UI for {symbol}...")

            # Update header UI immediately
            if self.header_ui:
                self.header_ui.update_current_stock(symbol)
                self.header_ui.update_connection_status("🔄 SWITCHING STOCK...", '#FFA500')

            # Clear current data
            self.live_order_book_data = {'bids': [], 'asks': []}
            self.current_ltp = 0.0

            # Reset order book display
            if hasattr(self, 'order_book_ui'):
                # Clear order book with placeholder data
                empty_bids = [(0, 0, 0, 'LOADING') for _ in range(20)]
                empty_asks = [(0, 0, 0, 'LOADING') for _ in range(20)]
                self.order_book_ui.update_display(empty_bids, empty_asks)

            # Reset quantity bars
            if hasattr(self, 'bid_qty_label') and hasattr(self, 'ask_qty_label'):
                self.bid_qty_label.configure(text="Loading...")
                self.ask_qty_label.configure(text="Loading...")

            # Update any price displays
            if hasattr(self, 'price_label'):
                self.price_label.configure(text="🔄 Loading...")

            print(f"✅ [UI RESET] UI prepared for {symbol}")

        except Exception as e:
            print(f"❌ [UI RESET] Error resetting UI: {e}")

    def connect_websocket_for_new_stock(self, symbol):
        """STEP 4: Connect WebSocket for the new selected stock"""
        try:
            print(f"🔌 [CONNECT] Connecting WebSocket for {symbol}...")

            # Reset stop flag
            self.stop_all_functions = False

            # Update connection status
            if self.header_ui:
                self.header_ui.update_connection_status("🔄 CONNECTING...", '#FFA500')

            # Only connect if we're in live mode
            if self.data_source == 'live':
                print(f"📡 [CONNECT] Starting live WebSocket connection for {symbol}...")
                self.connect_websocket()
            else:
                print(f"📊 [CONNECT] Using simulated data for {symbol}")
                if self.header_ui:
                    self.header_ui.update_connection_status("● SIMULATED DATA", '#7ED321')

            # Restart data updates
            self.restart_all_functions()

            print(f"✅ [CONNECT] Stock switch to {symbol} completed successfully")

        except Exception as e:
            print(f"❌ [CONNECT] Error connecting WebSocket for new stock: {e}")

    def _finalize_stock_switch(self, symbol):
        """Legacy method - now handled by connect_websocket_for_new_stock"""
        print(f"✅ [LEGACY] Stock switched to {symbol}")
        if hasattr(self, 'price_label'):
            self.price_label.configure(text="✅ Ready")

    def _revert_stock_switch(self, old_symbol):
        """Revert stock switch on error"""
        try:
            self.current_symbol = old_symbol
            if self.header_ui:
                self.header_ui.update_current_stock(old_symbol)
            if hasattr(self, 'price_label'):
                self.price_label.configure(text="❌ Error")
            print(f"⚠️ Reverted to {old_symbol}")
        except Exception as e:
            print(f"❌ Error reverting stock switch: {e}")



    def get_popular_stocks(self):
        """Get a curated list of popular stocks for quick access"""
        popular_stocks = [
            ('RELIANCE-EQ', '2885', 'NSE'),
            ('TCS-EQ', '11536', 'NSE'),
            ('INFY-EQ', '1594', 'NSE'),
            ('HDFCBANK-EQ', '1333', 'NSE'),
            ('ICICIBANK-EQ', '4963', 'NSE'),
            ('SBIN-EQ', '3045', 'NSE'),
            ('BHARTIARTL-EQ', '10604', 'NSE'),
            ('ITC-EQ', '424', 'NSE'),
            ('HINDUNILVR-EQ', '356', 'NSE'),
            ('LT-EQ', '11483', 'NSE'),
            ('KOTAKBANK-EQ', '1922', 'NSE'),
            ('ASIANPAINT-EQ', '236', 'NSE'),
            ('MARUTI-EQ', '10999', 'NSE'),
            ('BAJFINANCE-EQ', '317', 'NSE'),
            ('TITAN-EQ', '3506', 'NSE'),
            ('CGCL-EQ', '20329', 'NSE'),  # Current stock
        ]
        return popular_stocks

    def handle_depth_mode_data(self, message):
        """Handle DEPTH mode data (Order Book) - Log for reference, use LTP-based generation"""
        try:
            print("📊 Processing DEPTH mode data...")

            # Extract bid/ask data using Smart API format
            bids_raw = message.get('depth_20_buy_data', [])
            asks_raw = message.get('depth_20_sell_data', [])

            if bids_raw and asks_raw:
                # Log the first few levels for reference
                print(f"📊 DEPTH data available: {len(bids_raw)} bids, {len(asks_raw)} asks")

                if bids_raw:
                    best_bid = float(bids_raw[0].get('price', 0)) / 100
                    print(f"📈 DEPTH Best Bid: ₹{best_bid:.2f}")

                if asks_raw:
                    best_ask = float(asks_raw[0].get('price', 0)) / 100
                    print(f"📉 DEPTH Best Ask: ₹{best_ask:.2f}")

                # Store for potential future use, but continue using LTP-based generation
                self.depth_data = {
                    'bids': bids_raw,
                    'asks': asks_raw,
                    'timestamp': time.time()
                }

                print("✅ DEPTH data logged - continuing with LTP-based order book generation")
            else:
                print("⚠️ No valid bid/ask data in depth message")
                print(f"📊 Raw depth keys: {list(message.keys())}")

        except Exception as e:
            print(f"❌ Error processing DEPTH data: {e}")
            import traceback
            traceback.print_exc()



    def fallback_to_simulated(self):
        """Fallback to simulated data when live connection fails"""
        print("🎯 Falling back to simulated data")
        self.data_source = "simulated"
        self.ws_connected = False

        # Button state management is now handled by header UI

        # Update connection status
        if self.header_ui:
            self.header_ui.update_connection_status(
                "● SIMULATED DATA (Fallback Mode)", '#00aaff'
            )

    def refresh_http_data(self):
        """Refresh LTP and other non-real-time data using HTTP API"""
        print("🔄 Refreshing HTTP data (LTP, OHLC, Volume)...")

        # Change button state to show loading
        self.refresh_btn.configure(text="⏳ Loading...", state='disabled')

        # Run HTTP refresh in background thread
        refresh_thread = threading.Thread(target=self._perform_http_refresh, daemon=True)
        refresh_thread.start()

    def _perform_http_refresh(self):
        """Perform HTTP API calls to refresh data"""
        try:
            # Always try Smart API first, initialize if needed
            if not self.smart_api or not self.cgcl_token:
                print("🔄 Initializing Smart API for HTTP refresh...")
                if not self.initialize_smart_api():
                    print("❌ Smart API initialization failed for HTTP refresh")
                    self._refresh_demo_data()
                    return

            # Use Smart API HTTP endpoints for real data refresh
            self._refresh_smart_api_data()

        except Exception as e:
            print(f"❌ HTTP refresh failed: {e}")
            self._refresh_demo_data()
        finally:
            # Reset button state
            self.root.after(0, lambda: self.refresh_btn.configure(text="🔄 Refresh", state='normal'))

    def _refresh_smart_api_data(self):
        """Refresh data using Smart API HTTP endpoints"""
        try:
            print("📡 Fetching latest CGCL data from Smart API HTTP...")

            # Method 1: Try LTP data first
            try:
                print("🔍 Getting LTP data...")
                ltp_response = self.smart_api.ltpData("NSE", "CGCL-EQ", self.cgcl_token)
                print(f"📊 LTP Response: {ltp_response}")

                if ltp_response.get('status') and ltp_response.get('data'):
                    ltp_data = ltp_response['data']
                    current_price = float(ltp_data.get('ltp', 0))
                    print(f"💰 Current LTP: ₹{current_price}")
                else:
                    print("⚠️ LTP data not available, trying alternative method...")
                    current_price = 0
            except Exception as e:
                print(f"⚠️ LTP fetch failed: {e}")
                current_price = 0

            # Method 2: Extract OHLC from LTP response if available
            if ltp_response.get('status') and ltp_response.get('data'):
                ltp_data = ltp_response['data']

                # Extract all available market information from LTP response
                open_price = float(ltp_data.get('open', 0))
                high_price = float(ltp_data.get('high', 0))
                low_price = float(ltp_data.get('low', 0))
                close_price = float(ltp_data.get('close', 0))

                print(f"📊 OHLC from LTP: O:₹{open_price} H:₹{high_price} L:₹{low_price} C:₹{close_price}")

                if open_price > 0 and high_price > 0:  # Valid OHLC data available
                    # Update UI with LTP data
                    self.root.after(0, lambda: self._update_ui_with_http_data({
                        'ltp': current_price,
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price,
                        'volume': 0,  # Volume not available in LTP
                        'timestamp': time.time()
                    }))

                    print("✅ Smart API LTP data refresh completed successfully!")
                    return

            # Method 3: Try historical data as fallback
            try:
                print("🔍 Trying historical data as fallback...")
                from datetime import datetime, timedelta

                # Get yesterday's date for historical data
                yesterday = datetime.now() - timedelta(days=1)
                hist_data = self.smart_api.getCandleData({
                    "exchange": "NSE",
                    "symboltoken": self.cgcl_token,
                    "interval": "ONE_DAY",
                    "fromdate": yesterday.strftime("%Y-%m-%d 09:15"),
                    "todate": datetime.now().strftime("%Y-%m-%d 15:30")
                })

                print(f"📊 Historical Response: {hist_data}")

                if hist_data.get('status') and hist_data.get('data'):
                    latest_candle = hist_data['data'][-1]  # Get latest candle

                    # Extract OHLC from historical data
                    hist_open = float(latest_candle[1])
                    hist_high = float(latest_candle[2])
                    hist_low = float(latest_candle[3])
                    hist_close = float(latest_candle[4])
                    hist_volume = int(latest_candle[5])

                    print(f"📊 Historical OHLC: O:₹{hist_open} H:₹{hist_high} L:₹{hist_low} C:₹{hist_close}")
                    print(f"📈 Historical Volume: {hist_volume:,}")

                    # Update UI with historical data
                    self.root.after(0, lambda: self._update_ui_with_http_data({
                        'ltp': hist_close,  # Use close as LTP when market is closed
                        'open': hist_open,
                        'high': hist_high,
                        'low': hist_low,
                        'close': hist_close,
                        'volume': hist_volume,
                        'timestamp': time.time()
                    }))

                    print("✅ Historical data refresh completed successfully!")
                    return

            except Exception as e:
                print(f"⚠️ Historical data fetch failed: {e}")

            print("❌ All Smart API HTTP methods failed")

        except Exception as e:
            print(f"❌ Smart API HTTP refresh failed: {e}")
            raise e

    def _refresh_demo_data(self):
        """Refresh with simulated/demo data when Smart API not available"""
        try:
            print("🎯 Refreshing with demo data...")

            # Generate realistic demo data
            import random
            base_price = 850.0

            # Simulate realistic price movements
            price_change = random.uniform(-2.0, 2.0)
            current_price = base_price + price_change

            demo_data = {
                'ltp': round(current_price, 2),
                'open': round(base_price + random.uniform(-1.0, 1.0), 2),
                'high': round(current_price + random.uniform(0, 3.0), 2),
                'low': round(current_price - random.uniform(0, 3.0), 2),
                'close': round(base_price + random.uniform(-1.5, 1.5), 2),
                'volume': random.randint(50000, 200000),
                'timestamp': time.time()
            }

            print(f"💰 Demo LTP: ₹{demo_data['ltp']}")
            print(f"📊 Demo OHLC: O:{demo_data['open']} H:{demo_data['high']} L:{demo_data['low']} C:{demo_data['close']}")
            print(f"📈 Demo Volume: {demo_data['volume']:,}")

            # Update UI with demo data
            self.root.after(0, lambda: self._update_ui_with_http_data(demo_data))

            print("✅ Demo data refresh completed!")

        except Exception as e:
            print(f"❌ Demo data refresh failed: {e}")

    def _update_ui_with_http_data(self, data):
        """Update UI components with refreshed HTTP data"""
        try:
            # Update price display in header
            if hasattr(self, 'price_label'):
                price_color = '#00ff41' if data['ltp'] >= data['close'] else '#ff0844'
                self.price_label.configure(
                    text=f"₹{data['ltp']:.2f}",
                    fg=price_color
                )

            # Update market analytics - REMOVED FOR ORDER FLOW ANALYSIS IMPLEMENTATION
            # if self.market_analytics:
            #     analytics_data = {
            #         'volume': f"{data['volume']:,}",
            #         'spread': abs(data['high'] - data['low']),
            #         'change': data['ltp'] - data['close'],
            #         'change_percent': ((data['ltp'] - data['close']) / data['close']) * 100 if data['close'] > 0 else 0
            #     }
            #     self.market_analytics.update_analytics(analytics_data)

            # Update trading charts - REMOVED FOR ORDER FLOW ANALYSIS IMPLEMENTATION
            # if self.trading_charts:
            #     try:
            #         chart_data = {
            #             'timestamp': data['timestamp'],
            #             'open': data['open'],
            #             'high': data['high'],
            #             'low': data['low'],
            #             'close': data['ltp'],
            #             'volume': data['volume']
            #         }
            #         self.trading_charts.add_new_data(chart_data, data['volume'])
            #         print(f"📊 HTTP Chart updated with LTP: ₹{data['ltp']:.2f}, Volume: {data['volume']:,}")
            #     except Exception as e:
            #         print(f"⚠️ Chart update error: {e}")
            #         import traceback
            #         traceback.print_exc()

            # Update connection status to show last refresh time
            if self.header_ui:
                import datetime
                refresh_time = datetime.datetime.now().strftime("%H:%M:%S")

                if self.data_source == 'live':
                    status_text = f"● LIVE FEED (HTTP: {refresh_time})"
                elif self.data_source == 'simulated':
                    status_text = f"● SIMULATED (HTTP: {refresh_time})"
                else:
                    status_text = f"● HTTP REFRESH: {refresh_time}"

                self.header_ui.update_connection_status(status_text)

            print(f"✅ UI updated with HTTP data at {datetime.datetime.now().strftime('%H:%M:%S')}")

        except Exception as e:
            print(f"❌ UI update failed: {e}")

    def disconnect_websocket(self):
        """Disconnect from WebSocket safely - delegates to clean disconnection method"""
        try:
            print("🔌 [LEGACY] Disconnecting WebSocket...")
            self.disconnect_all_websockets()
        except Exception as e:
            print(f"❌ [LEGACY] Error disconnecting WebSocket: {e}")

    def on_ws_open(self, ws):
        """WebSocket connection opened"""
        print("✅ WebSocket connected successfully!")
        self.ws_connected = True

        # Subscribe to CGCL market depth
        subscribe_message = {
            "a": "subscribe",
            "v": [[1, "26000"]]  # CGCL token (you'll need the correct token)
        }
        ws.send(json.dumps(subscribe_message))
        print("📡 Subscribed to CGCL market depth")

    def on_ws_message(self, ws, message):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(message)

            # Parse market depth data (format depends on Smart API response)
            if 'tk' in data and data['tk'] == '26000':  # CGCL token
                # Extract bid/ask data from Smart API response
                # This is a placeholder - you'll need to adapt to actual Smart API format
                if 'bp1' in data and 'ap1' in data:
                    # Build order book from Smart API data
                    bids = []
                    asks = []

                    # Extract up to 5 levels of bid/ask data
                    for i in range(1, 6):
                        bp_key = f'bp{i}'
                        bq_key = f'bq{i}'
                        ap_key = f'ap{i}'
                        aq_key = f'aq{i}'

                        if bp_key in data and bq_key in data:
                            bids.append([
                                int(data[bq_key]),  # quantity
                                1,  # orders (not provided by Smart API)
                                float(data[bp_key])  # price
                            ])

                        if ap_key in data and aq_key in data:
                            asks.append([
                                float(data[ap_key]),  # price
                                1,  # orders (not provided by Smart API)
                                int(data[aq_key])  # quantity
                            ])

                    # Update live order book data
                    self.live_order_book_data = {'bids': bids, 'asks': asks}

        except Exception as e:
            print(f"❌ Error parsing WebSocket message: {e}")

    def on_ws_error(self, ws, error):
        """WebSocket error handler"""
        print(f"❌ WebSocket error: {error}")
        self.ws_connected = False

    def on_ws_close(self, ws, close_status_code, close_msg):
        """WebSocket connection closed"""
        print("🔌 WebSocket connection closed")
        self.ws_connected = False

    def update_ping_monitoring(self):
        """Update ping monitoring with accurate network latency"""
        try:
            # Test ping to multiple targets for reliability
            ping_results = []
            for target in self.ping_targets:
                try:
                    # Use ping3 for accurate ping measurement (timeout 2 seconds)
                    ping_time = ping3.ping(target, timeout=2)
                    if ping_time is not None:
                        ping_results.append(ping_time * 1000)  # Convert to milliseconds
                except Exception:
                    continue

            if ping_results:
                # Use average ping from successful results
                avg_ping = sum(ping_results) / len(ping_results)
                self.ping_data['current_ping'] = avg_ping
                self.ping_data['last_update'] = datetime.now()

                # Determine status and color based on ping
                if avg_ping < 50:
                    self.ping_data['status'] = 'excellent'
                    self.ping_data['color'] = '#00ff41'  # Green
                    status_text = f"🟢 {avg_ping:.0f}ms (Excellent)"
                elif avg_ping < 100:
                    self.ping_data['status'] = 'good'
                    self.ping_data['color'] = '#00cc33'  # Light green
                    status_text = f"🟢 {avg_ping:.0f}ms (Good)"
                elif avg_ping < 200:
                    self.ping_data['status'] = 'fair'
                    self.ping_data['color'] = '#ffaa00'  # Orange
                    status_text = f"🟡 {avg_ping:.0f}ms (Fair)"
                elif avg_ping < 500:
                    self.ping_data['status'] = 'poor'
                    self.ping_data['color'] = '#ff6600'  # Orange-red
                    status_text = f"🟠 {avg_ping:.0f}ms (Poor)"
                else:
                    self.ping_data['status'] = 'bad'
                    self.ping_data['color'] = '#ff0033'  # Red
                    status_text = f"🔴 {avg_ping:.0f}ms (Bad)"
            else:
                # No successful pings
                self.ping_data['status'] = 'offline'
                self.ping_data['color'] = '#ff0033'
                status_text = "🔴 Offline"

            # Update UI in main thread
            if self.ping_label:
                self.ping_label.configure(text=status_text, text_color=self.ping_data['color'])

        except Exception as e:
            print(f"❌ Ping monitoring error: {e}")
            if self.ping_label:
                self.ping_label.configure(text="🔴 Error", text_color="#ff0033")

    def start_data_updates(self):
        """Start background data updates"""
        self.running = True
        update_thread = threading.Thread(target=self.data_update_loop, daemon=True)
        update_thread.start()

    def data_update_loop(self):
        """Background data update loop with ping monitoring"""
        ping_counter = 0  # Counter for ping updates

        while self.running:
            try:
                # Update ping monitoring every second (every loop iteration)
                ping_counter += 1
                if ping_counter >= 1:  # Update ping every second
                    self.update_ping_monitoring()
                    ping_counter = 0

                if self.data_source == 'simulated':
                    # Update simulator
                    self.simulator.update_order_book()

                    # Get data
                    bids, asks = self.simulator.get_order_book_data()
                    signals = self.simulator.get_trading_signals()
                    analytics = self.simulator.get_market_analytics()

                    # Update UI in main thread
                    def update_ui():
                        # Update HFT-style order book display
                        self.update_order_book_display(bids, asks)

                        # Update quantity bars
                        self.update_quantity_bars(bids, asks)

                        # Update Order Flow Analysis
                        self.update_order_flow_analysis(bids, asks)

                        # Update market status
                        if bids and asks:
                            current_price = (bids[0][2] + asks[0][0]) / 2

                            # Generate simulated OHLC data
                            import random
                            price_range = current_price * 0.02  # 2% range
                            sim_ohlc = {
                                'open': current_price + random.uniform(-price_range/2, price_range/2),
                                'high': current_price + random.uniform(0, price_range),
                                'low': current_price - random.uniform(0, price_range),
                                'close': current_price
                            }
                            sim_volume = random.randint(800000, 1500000)

                            self.update_price_display(current_price, sim_ohlc, sim_volume)

                            # Update connection status for simulated mode with market condition
                            if self.header_ui:
                                condition_name = self.simulator.get_current_condition_name()
                                self.header_ui.update_connection_status(
                                    f"● SIMULATED DATA ({condition_name})", '#00aaff'
                                )

                            # Update spread for simulated mode
                            if hasattr(self, 'spread_label') and not self.ws_connected:
                                # Calculate simulated spread
                                spread = asks[0][0] - bids[0][2] if bids and asks else 0.15
                                self.spread_label.configure(text=f"₹{spread:.2f}")

                        # Update trading analysis and signals - REMOVED FOR ORDER FLOW ANALYSIS IMPLEMENTATION
                        # self.trading_signals.update_signals(signals)
                        # self.market_analytics.update_analytics(analytics)

                        # Update trading charts - REMOVED FOR ORDER FLOW ANALYSIS IMPLEMENTATION
                        # if bids and asks:
                        #     # Update order book data for trading charts
                        #     self.trading_charts.update_order_book_data(bids, asks)
                        #
                        #     # Get current price from best bid/ask
                        #     best_bid = bids[0][2] if bids else 870.0  # price from (qty, orders, price)
                        #     best_ask = asks[0][0] if asks else 872.0  # price from (price, orders, qty)
                        #     current_price = (best_bid + best_ask) / 2
                        #
                        #     # Generate OHLC data (simplified for demo)
                        #     price_data = {
                        #         'open': current_price - 0.5,
                        #         'high': current_price + 0.8,
                        #         'low': current_price - 0.8,
                        #         'close': current_price
                        #     }
                        #     # Handle both old format (3 elements) and new format (4 elements with source)
                        #     bid_volume = 0
                        #     for bid in bids[:5]:
                        #         if len(bid) == 4:
                        #             qty, _, _, _ = bid  # qty, orders, price, source
                        #         else:
                        #             qty, _, _ = bid  # qty, orders, price
                        #         bid_volume += qty
                        #
                        #     ask_volume = 0
                        #     for ask in asks[:5]:
                        #         if len(ask) == 4:
                        #             _, _, qty, _ = ask  # price, orders, qty, source
                        #         else:
                        #             _, _, qty = ask  # price, orders, qty
                        #         ask_volume += qty
                        #
                        #     volume = bid_volume + ask_volume
                        #
                        #     self.trading_charts.add_new_data(price_data, volume)

                    self.root.after(0, update_ui)

                elif self.data_source == 'live':
                    # Live mode - WebSocket data is handled in real-time via callbacks
                    if self.ws_connected:
                        # WebSocket is connected, data updates happen via callbacks
                        # Just sleep and let the WebSocket callbacks handle UI updates
                        time.sleep(1)
                    else:
                        # Only show waiting message every 30 seconds to reduce spam
                        if not hasattr(self, 'wait_counter'):
                            self.wait_counter = 0
                        self.wait_counter += 1

                        if self.wait_counter % 30 == 0:
                            print("⏳ Waiting for WebSocket connection...")
                        time.sleep(1)

                elif self.data_source == 'disconnected':
                    # No updates when disconnected
                    time.sleep(1)

                # Wait for next update
                time.sleep(APP_CONFIG['update_interval'])

            except Exception as e:
                print(f"❌ Error in data update loop: {e}")
                time.sleep(1)

    def run(self):
        """Run the application"""
        try:
            print("🚀 Starting Ultimate Trading Analysis v2...")

            # Enable stock search immediately since we use local database
            if hasattr(self, 'stock_search_widget'):
                self.stock_search_widget.enable_search()
                print("✅ Stock search enabled in separate section")

            self.root.mainloop()
        except KeyboardInterrupt:
            print("👋 Application stopped by user")
        finally:
            self.running = False


if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        print("🧪 Testing Smart API initialization fix...")
        print("=" * 50)

        try:
            # Create a test instance
            app = UltimateTrading()

            # Test the initialization
            result = app.initialize_smart_api()

            if result:
                print("✅ Smart API initialization returned True - FIX SUCCESSFUL!")
                print(f"📊 CGCL Token: {app.cgcl_token}")
                print(f"💰 Previous Close: ₹{app.previous_close_price}")
                print("🎉 API FIX VERIFICATION: SUCCESS!")
            else:
                print("❌ Smart API initialization returned False - FIX FAILED!")
                print("💥 API FIX VERIFICATION: FAILED!")

        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            print("💥 API FIX VERIFICATION: FAILED!")

        print("=" * 50)
    else:
        app = UltimateTrading()
        app.run()
