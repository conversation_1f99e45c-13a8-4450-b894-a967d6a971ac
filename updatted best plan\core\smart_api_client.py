"""
Smart API Client Integration
"""

import json
import pyotp
import requests
import websocket
import threading
import time
from datetime import datetime
from typing import Dict, Optional, Callable

from config.credentials import credentials, initialize_credentials


class SmartAPIClient:
    """Smart API client for authentication and data access"""
    
    def __init__(self):
        self.base_url = "https://apiconnect.angelbroking.com"
        self.websocket_url = "wss://smartapisocket.angelone.in/smart-stream"
        
        # Authentication
        self.auth_token = None
        self.refresh_token = None
        self.feed_token = None
        self.client_code = None
        
        # Session
        self.session = requests.Session()
        self.is_authenticated = False
        
        # CGCL token
        self.cgcl_token = None
        
    def authenticate(self) -> bool:
        """Authenticate with Smart API"""
        try:
            if not initialize_credentials():
                return False
            
            print("🔐 Authenticating with Smart API...")
            
            # Generate TOTP
            totp = pyotp.TOTP(credentials.totp_token)
            totp_code = totp.now()
            
            # Login request
            login_data = {
                "clientcode": credentials.client_code,
                "password": credentials.password,
                "totp": totp_code
            }
            
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "X-UserType": "USER",
                "X-SourceID": "WEB",
                "X-ClientLocalIP": "***********",
                "X-ClientPublicIP": "**************",
                "X-MACAddress": "fe80::216:3eff:fe00:1",
                "X-PrivateKey": credentials.api_key
            }
            
            response = self.session.post(
                f"{self.base_url}/rest/auth/angelbroking/user/v1/loginByPassword",
                json=login_data,
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    data = result.get('data', {})
                    self.auth_token = data.get('jwtToken')
                    self.refresh_token = data.get('refreshToken')
                    self.feed_token = data.get('feedToken')
                    self.client_code = credentials.client_code
                    self.is_authenticated = True
                    
                    print("✅ Smart API authentication successful")
                    
                    # Get CGCL token
                    self._get_cgcl_token()
                    
                    return True
                else:
                    print(f"❌ Authentication failed: {result.get('message', 'Unknown error')}")
                    return False
            else:
                print(f"❌ Authentication request failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def _get_cgcl_token(self):
        """Get CGCL instrument token"""
        try:
            # Search for CGCL instrument
            search_data = {
                "exchange": "NSE",
                "searchscrip": "CGCL"
            }
            
            headers = {
                "Authorization": f"Bearer {self.auth_token}",
                "Content-Type": "application/json",
                "Accept": "application/json",
                "X-UserType": "USER",
                "X-SourceID": "WEB",
                "X-ClientLocalIP": "***********",
                "X-ClientPublicIP": "**************",
                "X-MACAddress": "fe80::216:3eff:fe00:1",
                "X-PrivateKey": credentials.api_key
            }
            
            response = self.session.post(
                f"{self.base_url}/rest/secure/angelbroking/order/v1/searchScrip",
                json=search_data,
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') and result.get('data'):
                    # Find CGCL in results
                    for instrument in result['data']:
                        if instrument.get('tradingsymbol') == 'CGCL-EQ':
                            self.cgcl_token = instrument.get('symboltoken')
                            print(f"✅ CGCL token found: {self.cgcl_token}")
                            return
                    
                    print("⚠️ CGCL instrument not found in search results")
                else:
                    print("⚠️ No instruments found for CGCL")
            else:
                print(f"⚠️ Instrument search failed: {response.status_code}")
                
        except Exception as e:
            print(f"Error getting CGCL token: {e}")
    
    def get_market_data(self, symbol: str = "CGCL") -> Optional[Dict]:
        """Get current market data"""
        try:
            if not self.is_authenticated:
                print("❌ Not authenticated")
                return None
            
            # LTP request
            ltp_data = {
                "exchange": "NSE",
                "tradingsymbol": f"{symbol}-EQ",
                "symboltoken": self.cgcl_token
            }
            
            headers = {
                "Authorization": f"Bearer {self.auth_token}",
                "Content-Type": "application/json",
                "Accept": "application/json",
                "X-UserType": "USER",
                "X-SourceID": "WEB",
                "X-ClientLocalIP": "***********",
                "X-ClientPublicIP": "**************",
                "X-MACAddress": "fe80::216:3eff:fe00:1",
                "X-PrivateKey": credentials.api_key
            }
            
            response = self.session.post(
                f"{self.base_url}/rest/secure/angelbroking/order/v1/getLTP",
                json=ltp_data,
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    return result.get('data')
                    
        except Exception as e:
            print(f"Error getting market data: {e}")
        
        return None
    
    def subscribe_websocket(self, on_message: Callable, on_connect: Callable = None, on_error: Callable = None):
        """Subscribe to WebSocket data feed"""
        try:
            if not self.is_authenticated or not self.cgcl_token:
                print("❌ Authentication or token missing")
                return False
            
            print("🔌 Connecting to Smart API WebSocket...")
            
            def on_open(ws):
                print("✅ WebSocket connected")
                if on_connect:
                    on_connect()
                
                # Subscribe to CGCL
                subscribe_message = {
                    "correlationID": "abcde12345",
                    "action": 1,  # Subscribe
                    "params": {
                        "mode": 3,  # DEPTH mode for order book
                        "tokenList": [
                            {
                                "exchangeType": 1,  # NSE
                                "tokens": [self.cgcl_token]
                            }
                        ]
                    }
                }
                
                ws.send(json.dumps(subscribe_message))
                print(f"📡 Subscribed to CGCL (Token: {self.cgcl_token})")
            
            def on_ws_message(ws, message):
                try:
                    if on_message:
                        on_message(message)
                except Exception as e:
                    print(f"Error processing WebSocket message: {e}")
            
            def on_ws_error(ws, error):
                print(f"❌ WebSocket error: {error}")
                if on_error:
                    on_error(error)
            
            def on_close(ws, close_status_code, close_msg):
                print("🔌 WebSocket connection closed")
            
            # Create WebSocket connection
            headers = [
                f"Authorization: Bearer {self.auth_token}",
                f"x-api-key: {credentials.api_key}",
                f"x-client-code: {self.client_code}",
                f"x-feed-token: {self.feed_token}"
            ]
            
            self.ws = websocket.WebSocketApp(
                self.websocket_url,
                header=headers,
                on_open=on_open,
                on_message=on_ws_message,
                on_error=on_ws_error,
                on_close=on_close
            )
            
            # Start WebSocket in separate thread
            ws_thread = threading.Thread(target=self.ws.run_forever, daemon=True)
            ws_thread.start()
            
            return True
            
        except Exception as e:
            print(f"❌ WebSocket subscription error: {e}")
            return False
    
    def logout(self):
        """Logout from Smart API"""
        try:
            if not self.is_authenticated:
                return True
            
            logout_data = {
                "clientcode": self.client_code
            }
            
            headers = {
                "Authorization": f"Bearer {self.auth_token}",
                "Content-Type": "application/json",
                "Accept": "application/json",
                "X-UserType": "USER",
                "X-SourceID": "WEB",
                "X-ClientLocalIP": "***********",
                "X-ClientPublicIP": "**************",
                "X-MACAddress": "fe80::216:3eff:fe00:1",
                "X-PrivateKey": credentials.api_key
            }
            
            response = self.session.post(
                f"{self.base_url}/rest/secure/angelbroking/user/v1/logout",
                json=logout_data,
                headers=headers
            )
            
            if response.status_code == 200:
                print("✅ Logged out successfully")
            
            self.is_authenticated = False
            self.auth_token = None
            self.refresh_token = None
            self.feed_token = None
            
            return True
            
        except Exception as e:
            print(f"Error during logout: {e}")
            return False
