#!/usr/bin/env python3
"""
Enhanced Market Data Simulator with Multiple Market Conditions
"""

import random
import time
from typing import List, Tu<PERSON>, Dict
from config import APP_CONFIG, SIMULATION_CONFIG
from market_conditions import MARKET_CONDITIONS, MARKET_CONDITION_NAMES


class MarketDataSimulator:
    """Enhanced simulator with multiple market conditions"""

    def __init__(self, market_condition: str = "sideways"):
        self.market_condition_type = market_condition
        self.market_condition = None
        self.set_market_condition(market_condition)

    def set_market_condition(self, condition_type: str):
        """Set the market condition type"""
        if condition_type in MARKET_CONDITIONS:
            self.market_condition_type = condition_type
            self.market_condition = MARKET_CONDITIONS[condition_type]()
            print(f"🎯 [SIMULATOR] Market condition set to: {MARKET_CONDITION_NAMES[condition_type]}")
        else:
            print(f"⚠️ [SIMULATOR] Unknown market condition: {condition_type}, using sideways")
            self.market_condition_type = "sideways"
            self.market_condition = MARKET_CONDITIONS["sideways"]()

    def get_current_condition_name(self) -> str:
        """Get the display name of current market condition"""
        return MARKET_CONDITION_NAMES.get(self.market_condition_type, "Unknown")

    def get_current_price(self) -> float:
        """Get current market price"""
        return self.market_condition.current_price if self.market_condition else APP_CONFIG['base_price']

    def update_order_book(self):
        """Update order book using current market condition"""
        if self.market_condition:
            self.market_condition.update_order_book()

    def get_order_book_data(self) -> Tuple[List[Tuple], List[Tuple]]:
        """Get current order book data from market condition"""
        if self.market_condition:
            return self.market_condition.get_order_book_data()
        return [], []

    def get_trading_signals(self) -> Dict:
        """Generate trading signals based on market condition"""
        if not self.market_condition:
            return {'entry': 0, 'target': 0, 'stop': 0, 'strength': 0}

        current_price = self.market_condition.current_price
        tick_size = self.market_condition.tick_size

        # Market condition specific signal generation
        if self.market_condition_type == "trending_up":
            entry_price = current_price
            target_price = entry_price + (3.0 * tick_size)
            stop_price = entry_price - (1.5 * tick_size)
            strength = random.randint(75, 90)
        elif self.market_condition_type == "trending_down":
            entry_price = current_price
            target_price = entry_price - (3.0 * tick_size)
            stop_price = entry_price + (1.5 * tick_size)
            strength = random.randint(75, 90)
        elif self.market_condition_type == "volatile":
            entry_price = current_price
            target_price = entry_price + (4.0 * tick_size)
            stop_price = entry_price - (4.0 * tick_size)
            strength = random.randint(60, 85)
        elif self.market_condition_type == "breakout":
            entry_price = current_price
            target_price = entry_price + (5.0 * tick_size)
            stop_price = entry_price - (2.0 * tick_size)
            strength = random.randint(80, 95)
        else:  # sideways
            entry_price = current_price
            target_price = entry_price + (1.5 * tick_size)
            stop_price = entry_price - (1.5 * tick_size)
            strength = random.randint(65, 80)

        return {
            'entry': entry_price,
            'target': target_price,
            'stop': stop_price,
            'strength': strength
        }

    def get_market_analytics(self) -> Dict:
        """Generate market analytics from current condition"""
        if self.market_condition:
            return self.market_condition.get_market_analytics()

        # Fallback analytics
        return {
            'volume': "1.2M",
            'spread': 0.05,
            'volatility': "Medium",
            'trend': "Sideways",
            'rsi': 50.0
        }
