# CGCL Advanced Trading System Requirements

# Core dependencies
websocket-client>=1.6.0
requests>=2.31.0
pyotp>=2.8.0
psutil>=5.9.0

# GUI framework (built-in with Python)
# tkinter - included with Python standard library

# Data processing
numpy>=1.24.0
pandas>=2.0.0

# Optional: For advanced analytics (if needed later)
# scikit-learn>=1.3.0
# matplotlib>=3.7.0

# Development dependencies (optional)
# pytest>=7.4.0
# black>=23.0.0
# flake8>=6.0.0
