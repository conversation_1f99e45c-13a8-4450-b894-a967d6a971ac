<?xml version="1.0" encoding="UTF-8"?>
<PAGE_PROJECT>
    <PROJECT_NAME>CGCL_Trading_System</PROJECT_NAME>
    <PROJECT_TYPE>Tkinter</PROJECT_TYPE>
    <MAIN_WINDOW>
        <TITLE>CGCL Advanced Trading System</TITLE>
        <GEOMETRY>1200x800</GEOMETRY>
        <BACKGROUND>#1a1a1a</BACKGROUND>
    </MAIN_WINDOW>
    <WIDGETS>
        <!-- Top Panel -->
        <FRAME name="top_panel" x="10" y="10" width="1180" height="100">
            <BACKGROUND>#2d2d2d</BACKGROUND>
            
            <!-- Performance Panel -->
            <FRAME name="performance_panel" x="10" y="10" width="300" height="80">
                <LABEL name="perf_title" text="📊 Performance" x="10" y="5"/>
                <LABEL name="latency_label" text="Latency: --ms" x="10" y="25"/>
                <LABEL name="throughput_label" text="Throughput: --/s" x="10" y="45"/>
            </FRAME>
            
            <!-- Clock Widget -->
            <FRAME name="clock_panel" x="320" y="10" width="200" height="80">
                <LABEL name="time_label" text="🕐 --:--:--" x="10" y="5"/>
                <LABEL name="date_label" text="-- --- ----" x="10" y="25"/>
                <LABEL name="session_label" text="Market Status" x="10" y="45"/>
            </FRAME>
            
            <!-- Status Panel -->
            <FRAME name="status_panel" x="530" y="10" width="300" height="80">
                <LABEL name="symbol_label" text="CGCL" x="10" y="5"/>
                <LABEL name="price_label" text="₹0.00" x="10" y="25"/>
                <LABEL name="change_label" text="0.00 (0.00%)" x="10" y="45"/>
            </FRAME>
        </FRAME>
        
        <!-- Order Book Panel -->
        <FRAME name="orderbook_panel" x="10" y="120" width="800" height="600">
            <BACKGROUND>#1e1e1e</BACKGROUND>
            <LABEL name="ob_title" text="📈 Order Book - CGCL" x="10" y="5"/>
            
            <!-- Order Book Content -->
            <FRAME name="ob_content" x="10" y="30" width="780" height="560">
                <SCROLLBAR orientation="vertical"/>
            </FRAME>
        </FRAME>
        
        <!-- Analytics Panel -->
        <FRAME name="analytics_panel" x="820" y="120" width="370" height="600">
            <BACKGROUND>#1e1e1e</BACKGROUND>
            <LABEL name="analytics_title" text="🔍 Analytics" x="10" y="5"/>
            
            <!-- Flow Analysis -->
            <FRAME name="flow_panel" x="10" y="30" width="350" height="150">
                <LABEL name="flow_title" text="Order Flow" x="5" y="5"/>
                <LABEL name="imbalance_label" text="Imbalance: --%" x="5" y="25"/>
                <LABEL name="momentum_label" text="Momentum: --" x="5" y="45"/>
            </FRAME>
            
            <!-- Price Prediction -->
            <FRAME name="prediction_panel" x="10" y="190" width="350" height="150">
                <LABEL name="pred_title" text="30-Min Prediction" x="5" y="5"/>
                <LABEL name="pred_price_label" text="Target: ₹--" x="5" y="25"/>
                <LABEL name="confidence_label" text="Confidence: --%%" x="5" y="45"/>
            </FRAME>
            
            <!-- Trading Signals -->
            <FRAME name="signals_panel" x="10" y="350" width="350" height="150">
                <LABEL name="signals_title" text="Trading Signals" x="5" y="5"/>
                <LABEL name="signal_label" text="No signals" x="5" y="25"/>
                <LABEL name="signal_conf_label" text="Confidence: --%%" x="5" y="45"/>
            </FRAME>
        </FRAME>
        
        <!-- Control Panel -->
        <FRAME name="control_panel" x="10" y="730" width="1180" height="60">
            <BACKGROUND>#2d2d2d</BACKGROUND>
            
            <BUTTON name="connect_btn" text="🔌 Connect" x="10" y="15" width="100" height="30"/>
            <BUTTON name="analytics_btn" text="📊 Analytics" x="120" y="15" width="100" height="30"/>
            <BUTTON name="settings_btn" text="⚙️ Settings" x="230" y="15" width="100" height="30"/>
            <BUTTON name="exit_btn" text="❌ Exit" x="1070" y="15" width="100" height="30"/>
        </FRAME>
    </WIDGETS>
</PAGE_PROJECT>