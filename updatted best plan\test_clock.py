#!/usr/bin/env python3
"""
Test Clock Widget
"""

import tkinter as tk
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.components import ClockWidget
from config.settings import COLORS


def test_clock():
    """Test the clock widget"""
    root = tk.Tk()
    root.title("Clock Widget Test")
    root.geometry("400x300")
    root.configure(bg=COLORS['bg_dark'])
    
    # Create clock widget
    clock = ClockWidget(root)
    clock.pack(pady=50)
    
    # Add label
    label = tk.Label(
        root,
        text="Clock Widget Test - Market Session Aware",
        bg=COLORS['bg_dark'],
        fg=COLORS['text_white'],
        font=('Arial', 14)
    )
    label.pack(pady=20)
    
    # Start the GUI
    root.mainloop()


if __name__ == "__main__":
    test_clock()
