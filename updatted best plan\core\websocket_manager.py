"""
WebSocket Connection Management for Smart API
"""

import json
import struct
import threading
import time
import websocket
from datetime import datetime
from typing import Dict, List, Optional, Callable, Tuple
from collections import deque

from config.credentials import credentials, initialize_credentials
from config.settings import SYSTEM_CONFIG
from .data_structures import MarketData, PerformanceMetrics
from .smart_api_client import SmartAPIClient


class ConnectionHealthMonitor:
    """Monitor WebSocket connection health and performance"""
    
    def __init__(self):
        self.connection_start_time = None
        self.last_message_time = None
        self.message_count = 0
        self.error_count = 0
        self.reconnection_count = 0
        self.latency_history = deque(maxlen=100)
        self.data_quality_score = 100.0
        
    def record_message(self, message_size: int = 0):
        """Record a received message"""
        now = datetime.now()
        self.last_message_time = now
        self.message_count += 1
        
        # Calculate latency (simplified)
        if self.connection_start_time:
            latency = (now - self.connection_start_time).total_seconds() * 1000
            self.latency_history.append(min(latency, 1000))  # Cap at 1 second
    
    def record_error(self):
        """Record an error"""
        self.error_count += 1
        self.data_quality_score = max(0, self.data_quality_score - 5)
    
    def record_reconnection(self):
        """Record a reconnection"""
        self.reconnection_count += 1
        self.connection_start_time = datetime.now()
    
    def get_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics"""
        now = datetime.now()
        
        # Calculate update rate
        if self.connection_start_time:
            uptime_seconds = (now - self.connection_start_time).total_seconds()
            update_rate = self.message_count / max(1, uptime_seconds)
        else:
            update_rate = 0.0
        
        # Calculate average latency
        avg_latency = sum(self.latency_history) / len(self.latency_history) if self.latency_history else 0.0
        
        return PerformanceMetrics(
            timestamp=now,
            latency_ms=avg_latency,
            update_rate_hz=update_rate,
            memory_usage_mb=0.0,  # Would be calculated by system monitor
            cpu_usage_percent=0.0,  # Would be calculated by system monitor
            data_quality_percent=self.data_quality_score,
            connection_status="CONNECTED" if self.last_message_time else "DISCONNECTED",
            message_count=self.message_count,
            error_count=self.error_count
        )


class SmartAPIWebSocketManager:
    """Advanced WebSocket connection management for Smart API"""

    def __init__(self):
        self.connection_state = "DISCONNECTED"
        self.ws_connection = None
        self.health_monitor = ConnectionHealthMonitor()

        # Smart API client
        self.smart_api_client = SmartAPIClient()

        # Connection settings
        self.websocket_url = "wss://smartapisocket.angelone.in/smart-stream"
        self.reconnect_delay = SYSTEM_CONFIG['reconnect_delay']
        self.max_reconnect_attempts = SYSTEM_CONFIG.get('max_reconnect_attempts', 10)
        self.ping_interval = SYSTEM_CONFIG['ping_interval']

        # Callbacks
        self.on_market_data: Optional[Callable] = None
        self.on_order_book_data: Optional[Callable] = None
        self.on_connection_status: Optional[Callable] = None

        # Threading
        self._stop_event = threading.Event()
        self._connection_thread = None

        # Data parsing
        self.cgcl_token = None
        
    def set_callbacks(self, on_market_data: Callable = None, 
                     on_order_book_data: Callable = None,
                     on_connection_status: Callable = None):
        """Set callback functions"""
        self.on_market_data = on_market_data
        self.on_order_book_data = on_order_book_data
        self.on_connection_status = on_connection_status
    
    def connect(self) -> bool:
        """Establish WebSocket connection"""
        try:
            print("🔌 Connecting to Smart API...")

            # First authenticate with Smart API
            if not self.smart_api_client.authenticate():
                print("❌ Smart API authentication failed")
                return False

            # Subscribe to WebSocket
            success = self.smart_api_client.subscribe_websocket(
                on_message=self._on_message,
                on_connect=self._on_connect,
                on_error=self._on_error
            )

            if success:
                self.connection_state = "CONNECTED"
                self.cgcl_token = self.smart_api_client.cgcl_token
                self.health_monitor.record_reconnection()

                if self.on_connection_status:
                    self.on_connection_status("CONNECTED")

                print("✅ Smart API WebSocket connected successfully")
                return True
            else:
                print("❌ WebSocket subscription failed")
                return False

        except Exception as e:
            print(f"❌ WebSocket connection failed: {e}")
            return False
    
    def disconnect(self):
        """Disconnect WebSocket"""
        self._stop_event.set()
        if self.smart_api_client:
            self.smart_api_client.logout()
        self.connection_state = "DISCONNECTED"
    
    def _on_connect(self):
        """WebSocket connection established"""
        print("✅ WebSocket connected successfully")
        self.connection_state = "CONNECTED"
        self.health_monitor.record_reconnection()

        if self.on_connection_status:
            self.on_connection_status("CONNECTED")
    
    def _on_message(self, message):
        """Process incoming WebSocket message"""
        try:
            self.health_monitor.record_message(len(message) if isinstance(message, (bytes, str)) else 0)

            # Parse binary message from Smart API
            if isinstance(message, bytes):
                self._parse_binary_message(message)
            else:
                # JSON message
                try:
                    data = json.loads(message)
                    self._process_json_message(data)
                except json.JSONDecodeError:
                    # Handle non-JSON string messages
                    print(f"📡 WebSocket message: {message}")

        except Exception as e:
            print(f"Error processing message: {e}")
            self.health_monitor.record_error()
    
    def _on_error(self, ws, error):
        """WebSocket error occurred"""
        print(f"❌ WebSocket error: {error}")
        self.health_monitor.record_error()
        
        if self.on_connection_status:
            self.on_connection_status("ERROR")
    
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket connection closed"""
        print("🔌 WebSocket connection closed")
        self.connection_state = "DISCONNECTED"
        
        if self.on_connection_status:
            self.on_connection_status("DISCONNECTED")
        
        # Auto-reconnect if not intentionally stopped
        if not self._stop_event.is_set():
            self._attempt_reconnection()
    
    def _subscribe_to_cgcl(self):
        """Subscribe to CGCL market data and order book"""
        try:
            # This would be the actual Smart API subscription
            # For now, we'll simulate the subscription
            print("📡 Subscribing to CGCL market data...")
            print("📊 Subscribing to CGCL order book (DEPTH mode)...")
            
            # Set CGCL token (would be obtained from Smart API)
            self.cgcl_token = "12345"  # Placeholder
            
        except Exception as e:
            print(f"❌ Subscription failed: {e}")
    
    def _parse_binary_message(self, message: bytes):
        """Parse binary market data message"""
        try:
            # Smart API binary format parsing
            # This is a simplified version - actual implementation would be more complex
            
            if len(message) < 8:
                return
            
            # Extract basic fields (simplified)
            token = struct.unpack('<I', message[0:4])[0]
            ltp = struct.unpack('<f', message[4:8])[0]
            
            # Store current price for order book generation
            self.last_market_price = ltp

            # Create market data
            market_data = MarketData(
                symbol="CGCL",
                timestamp=datetime.now(),
                ltp=ltp,
                open_price=ltp,  # Simplified
                high_price=ltp,
                low_price=ltp,
                close_price=ltp,
                volume=0,
                prev_close=180.75
            )

            if self.on_market_data:
                self.on_market_data(market_data)
            
            # Parse order book data if available
            if len(message) > 100:  # Has order book data
                self._parse_order_book_data(message[8:])
                
        except Exception as e:
            print(f"Error parsing binary message: {e}")
            self.health_monitor.record_error()
    
    def _parse_order_book_data(self, data: bytes):
        """Parse order book data from binary message"""
        try:
            # Simplified order book parsing
            # Actual implementation would parse 20 levels of bids/asks

            bids = []
            asks = []

            # Use current market price for realistic order book
            # Get current price from recent market data or default to CGCL range
            base_price = getattr(self, 'last_market_price', 184.75)

            # Generate realistic order book around current price
            import random
            import numpy as np

            for i in range(10):
                # Bid levels (decreasing price from current)
                bid_price = base_price - (i + 1) * random.uniform(0.05, 0.15)
                bid_qty = int(np.random.exponential(300) + 50)  # More realistic volume distribution
                bid_orders = max(1, int(bid_qty / random.randint(50, 200)))
                bids.append((bid_qty, bid_orders, round(bid_price, 2)))

                # Ask levels (increasing price from current)
                ask_price = base_price + (i + 1) * random.uniform(0.05, 0.15)
                ask_qty = int(np.random.exponential(300) + 50)  # More realistic volume distribution
                ask_orders = max(1, int(ask_qty / random.randint(50, 200)))
                asks.append((round(ask_price, 2), ask_orders, ask_qty))

            if self.on_order_book_data:
                self.on_order_book_data(bids, asks)

        except Exception as e:
            print(f"Error parsing order book data: {e}")
    
    def _process_json_message(self, data: Dict):
        """Process JSON message"""
        try:
            # Handle different message types
            if 'type' in data:
                if data['type'] == 'connection':
                    print(f"📡 Connection status: {data.get('status', 'unknown')}")
                elif data['type'] == 'error':
                    print(f"❌ Server error: {data.get('message', 'unknown')}")
                    
        except Exception as e:
            print(f"Error processing JSON message: {e}")
    
    def _attempt_reconnection(self):
        """Attempt to reconnect"""
        if self.health_monitor.reconnection_count >= self.max_reconnect_attempts:
            print("❌ Max reconnection attempts reached")
            return
        
        print(f"🔄 Attempting reconnection in {self.reconnect_delay} seconds...")
        time.sleep(self.reconnect_delay)
        
        # Exponential backoff
        self.reconnect_delay = min(self.reconnect_delay * 2, 30)
        
        self.connect()
    
    def get_performance_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics"""
        return self.health_monitor.get_metrics()
    
    def is_connected(self) -> bool:
        """Check if WebSocket is connected"""
        return self.connection_state == "CONNECTED"
