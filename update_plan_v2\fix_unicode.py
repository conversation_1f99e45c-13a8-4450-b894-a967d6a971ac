#!/usr/bin/env python3
"""
Fix Unicode characters in main_app.py
"""

def fix_unicode_quotes():
    """Replace Unicode quotes with ASCII quotes"""
    
    # Read the file with UTF-8 encoding
    with open('main_app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace Unicode quotes with ASCII quotes
    replacements = {
        '"': '"',  # Left double quotation mark
        '"': '"',  # Right double quotation mark
        ''': "'",  # Left single quotation mark
        ''': "'",  # Right single quotation mark
        '—': '-',  # Em dash
        '–': '-',  # En dash
    }
    
    for unicode_char, ascii_char in replacements.items():
        content = content.replace(unicode_char, ascii_char)
    
    # Write back with UTF-8 encoding
    with open('main_app.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Fixed Unicode characters in main_app.py")

if __name__ == "__main__":
    fix_unicode_quotes()
