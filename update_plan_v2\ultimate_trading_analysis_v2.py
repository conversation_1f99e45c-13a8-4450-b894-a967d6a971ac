#!/usr/bin/env python3
"""
Ultimate Trading Analysis v2 - CGCL Live Market Analysis
Fixed version with working order book display and live/simulated data switching
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import random
from datetime import datetime
from dataclasses import dataclass
from typing import List, Tuple, Optional
from enum import Enum
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np

# Executive styling
EXECUTIVE_COLORS = {
    'bg_primary': '#1a1a1a',
    'bg_secondary': '#2a2a2a', 
    'bg_card': '#333333',
    'text_primary': '#ffffff',
    'text_secondary': '#b0b0b0',
    'accent_blue': '#00d4ff',
    'success': '#00ff88',
    'danger': '#ff4757',
    'warning': '#ffa502'
}

@dataclass
class OrderBookLevel:
    """Order book level data"""
    price: float
    quantity: int
    orders: int

@dataclass 
class OrderBookSnapshot:
    """Complete order book snapshot"""
    symbol: str
    bids: List[OrderBookLevel]
    asks: List[OrderBookLevel]
    timestamp: datetime

class DataSourceType(Enum):
    LIVE = "live"
    SIMULATED = "simulated"

class MarketSimulator:
    """Simulates realistic CGCL market data"""
    
    def __init__(self, symbol: str = "CGCL", base_price: float = 850.0):
        self.symbol = symbol
        self.base_price = base_price
        self.current_price = base_price
        self.is_running = False
        self.thread = None
        self.on_order_book_update = None
        
    def start_simulation(self):
        """Start market simulation"""
        if not self.is_running:
            self.is_running = True
            self.thread = threading.Thread(target=self._simulation_loop, daemon=True)
            self.thread.start()
            print(f"🚀 {self.symbol} Market Simulation started - Updates every second")
    
    def stop_simulation(self):
        """Stop market simulation"""
        self.is_running = False
        if self.thread:
            self.thread.join(timeout=1)
        print(f"⏹️ {self.symbol} Market Simulation stopped")
    
    def _simulation_loop(self):
        """Main simulation loop"""
        while self.is_running:
            try:
                # Generate realistic price movement
                price_change = random.uniform(-2.0, 2.0)
                self.current_price = max(self.base_price * 0.9, 
                                       min(self.base_price * 1.1, 
                                           self.current_price + price_change))
                
                # Generate order book
                order_book = self._generate_order_book()
                
                # Call callback if set
                if self.on_order_book_update:
                    self.on_order_book_update(self.symbol, order_book)
                
                time.sleep(1)  # Update every second
                
            except Exception as e:
                print(f"❌ Simulation error: {e}")
                break
    
    def _generate_order_book(self) -> OrderBookSnapshot:
        """Generate realistic 10-level order book"""
        tick_size = 0.05
        spread = 0.10
        
        bid_price = self.current_price - spread/2
        ask_price = self.current_price + spread/2
        
        bids = []
        asks = []
        
        # Generate 10 levels each side
        for i in range(10):
            # Bid levels (descending prices)
            bid_level_price = bid_price - (i * tick_size)
            bid_qty = random.randint(100, 50000)
            bid_orders = random.randint(1, 25)
            bids.append(OrderBookLevel(bid_level_price, bid_qty, bid_orders))
            
            # Ask levels (ascending prices) 
            ask_level_price = ask_price + (i * tick_size)
            ask_qty = random.randint(100, 50000)
            ask_orders = random.randint(1, 25)
            asks.append(OrderBookLevel(ask_level_price, ask_qty, ask_orders))
        
        return OrderBookSnapshot(
            symbol=self.symbol,
            bids=bids,
            asks=asks,
            timestamp=datetime.now()
        )

# OLD VERSION - REPLACED WITH SEPARATE MODULE
# class UnifiedOrderBookWidget(tk.Frame):
# """Unified order book widget with data table overlaid on chart"""
#
#     def __init__(self, parent, **kwargs):
#         super().__init__(parent, bg=EXECUTIVE_COLORS['bg_card'], **kwargs)
#
#         self.bid_data = []
#         self.ask_data = []
#         self.max_levels = 10
#
#         self.create_unified_order_book()
#
#     def create_unified_order_book(self):
#         """Create unified order book with chart and data overlay"""
#         # Title
#         title_label = tk.Label(self, text="📊 Order Book Depth",
#                               bg=EXECUTIVE_COLORS['bg_card'],
#                               fg=EXECUTIVE_COLORS['text_primary'],
#                               font=('Arial', 14, 'bold'))
#         title_label.pack(pady=(10, 5))
#
#         # Main container for chart and data overlay
#         self.main_container = tk.Frame(self, bg=EXECUTIVE_COLORS['bg_card'])
#         self.main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
#
#         # Create matplotlib chart as background
#         self.create_background_chart()
#
#         # Create data table overlay
#         self.create_data_table_overlay()
#
#     def create_background_chart(self):
#         """Create matplotlib chart as background"""
#         # Create matplotlib figure with tight layout
#         self.figure = Figure(figsize=(14, 6), dpi=100, facecolor=EXECUTIVE_COLORS['bg_primary'])
#         self.figure.patch.set_facecolor(EXECUTIVE_COLORS['bg_primary'])
#
#         # Create axis with specific positioning to leave room for data overlay
#         self.ax = self.figure.add_subplot(111)
#         self.ax.set_facecolor(EXECUTIVE_COLORS['bg_primary'])
#
#         # Style the chart
#         self.ax.tick_params(colors=EXECUTIVE_COLORS['text_secondary'], labelsize=8)
#         for spine in self.ax.spines.values():
#             spine.set_color(EXECUTIVE_COLORS['text_secondary'])
#
#         # Remove axis labels and ticks to make room for data overlay
#         self.ax.set_xticks([])
#         self.ax.set_yticks([])
#         self.ax.set_xlabel('')
#         self.ax.set_ylabel('')
#
#         # Create canvas
#         self.canvas = FigureCanvasTkAgg(self.figure, self.main_container)
#         self.canvas_widget = self.canvas.get_tk_widget()
#         self.canvas_widget.pack(fill=tk.BOTH, expand=True)
#
#         # Initialize with empty data
#         self.update_background_chart([], [])

#     def create_data_table_overlay(self):
#         """Create data table overlay on top of chart"""
#         # Create overlay frame that sits on top of the chart
#         self.overlay_frame = tk.Frame(self.main_container)
#         self.overlay_frame.place(x=0, y=0, relwidth=1, relheight=1)
#
#         # Headers frame
#         header_frame = tk.Frame(self.overlay_frame, bg=EXECUTIVE_COLORS['bg_secondary'], height=30)
#         header_frame.pack(fill=tk.X, padx=20, pady=(20, 0))
#         header_frame.pack_propagate(False)
#
#         # Column headers
#         headers = ['TOTAL', 'SIZE', 'PRICE', 'PRICE', 'SIZE', 'TOTAL']
#         colors = [EXECUTIVE_COLORS['success']] * 3 + [EXECUTIVE_COLORS['danger']] * 3
#
#         for header, color in zip(headers, colors):
#             label = tk.Label(header_frame, text=header,
#                            bg=EXECUTIVE_COLORS['bg_secondary'], fg=color,
#                            font=('Arial', 10, 'bold'), width=12)
#             label.pack(side=tk.LEFT, expand=True)
#
#         # Scrollable data container
#         self.create_scrollable_data_container()
#
#         # Data rows
#         self.order_rows = []
#         self.create_data_rows()

    def create_scrollable_data_container(self):
        """Create scrollable container for data rows"""
        # Container frame
        container = tk.Frame(self.overlay_frame)
        container.pack(fill=tk.BOTH, expand=True, padx=20, pady=(5, 20))

        # Canvas for scrolling
        self.data_canvas = tk.Canvas(container, bg=EXECUTIVE_COLORS['bg_primary'], highlightthickness=0, height=400)

        # Scrollbar
        scrollbar = ttk.Scrollbar(container, orient="vertical", command=self.data_canvas.yview)

        # Scrollable frame
        self.scrollable_frame = tk.Frame(self.data_canvas, bg=EXECUTIVE_COLORS['bg_primary'])

        # Configure scrolling
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.data_canvas.configure(scrollregion=self.data_canvas.bbox("all"))
        )

        self.data_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.data_canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        self.data_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas
        self.data_canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.scrollable_frame.bind("<MouseWheel>", self._on_mousewheel)

    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        self.data_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def create_data_rows(self):
        """Create data rows for order book levels"""
        for i in range(self.max_levels):
            row_frame = tk.Frame(self.scrollable_frame, bg=EXECUTIVE_COLORS['bg_primary'], height=25)
            row_frame.pack(fill=tk.X, pady=1)
            row_frame.pack_propagate(False)

            # Create labels for each column
            row_widgets = {}

            # Bid side (TOTAL | SIZE | PRICE)
            row_widgets['bid_total'] = tk.Label(row_frame, text="",
                                              bg=EXECUTIVE_COLORS['bg_primary'],
                                              fg=EXECUTIVE_COLORS['success'],
                                              font=('Consolas', 9), width=12, anchor='e')
            row_widgets['bid_total'].pack(side=tk.LEFT, expand=True)

            row_widgets['bid_size'] = tk.Label(row_frame, text="",
                                             bg=EXECUTIVE_COLORS['bg_primary'],
                                             fg=EXECUTIVE_COLORS['success'],
                                             font=('Consolas', 9), width=12, anchor='e')
            row_widgets['bid_size'].pack(side=tk.LEFT, expand=True)

            row_widgets['bid_price'] = tk.Label(row_frame, text="",
                                              bg=EXECUTIVE_COLORS['bg_primary'],
                                              fg=EXECUTIVE_COLORS['success'],
                                              font=('Consolas', 9, 'bold'), width=12, anchor='e')
            row_widgets['bid_price'].pack(side=tk.LEFT, expand=True)

            # Ask side (PRICE | SIZE | TOTAL)
            row_widgets['ask_price'] = tk.Label(row_frame, text="",
                                              bg=EXECUTIVE_COLORS['bg_primary'],
                                              fg=EXECUTIVE_COLORS['danger'],
                                              font=('Consolas', 9, 'bold'), width=12, anchor='w')
            row_widgets['ask_price'].pack(side=tk.LEFT, expand=True)

            row_widgets['ask_size'] = tk.Label(row_frame, text="",
                                             bg=EXECUTIVE_COLORS['bg_primary'],
                                             fg=EXECUTIVE_COLORS['danger'],
                                             font=('Consolas', 9), width=12, anchor='w')
            row_widgets['ask_size'].pack(side=tk.LEFT, expand=True)

            row_widgets['ask_total'] = tk.Label(row_frame, text="",
                                              bg=EXECUTIVE_COLORS['bg_primary'],
                                              fg=EXECUTIVE_COLORS['danger'],
                                              font=('Consolas', 9), width=12, anchor='w')
            row_widgets['ask_total'].pack(side=tk.LEFT, expand=True)

            self.order_rows.append(row_widgets)

    def update_background_chart(self, bids: List[Tuple], asks: List[Tuple]):
        """Update background chart"""
        try:
            self.ax.clear()

            if bids and asks:
                # Prepare bid data (cumulative)
                bid_prices = [price for _, _, price in bids[:10]]
                bid_quantities = [qty for qty, _, _ in bids[:10]]
                bid_cumulative = np.cumsum(bid_quantities)

                # Prepare ask data (cumulative)
                ask_prices = [price for price, _, _ in asks[:10]]
                ask_quantities = [qty for _, _, qty in asks[:10]]
                ask_cumulative = np.cumsum(ask_quantities)

                # Plot bid side (green, left side) - more transparent
                self.ax.fill_between(bid_prices, 0, bid_cumulative,
                                   step='post', alpha=0.15,
                                   color=EXECUTIVE_COLORS['success'])

                # Plot ask side (red, right side) - more transparent
                self.ax.fill_between(ask_prices, 0, ask_cumulative,
                                   step='pre', alpha=0.15,
                                   color=EXECUTIVE_COLORS['danger'])

                # Very subtle grid
                self.ax.grid(True, alpha=0.1, color=EXECUTIVE_COLORS['text_secondary'])

            # Remove all axis elements to make it pure background
            self.ax.set_xticks([])
            self.ax.set_yticks([])
            self.ax.set_xlabel('')
            self.ax.set_ylabel('')

            # Style the chart
            self.ax.set_facecolor(EXECUTIVE_COLORS['bg_primary'])
            for spine in self.ax.spines.values():
                spine.set_visible(False)

            # Tight layout to maximize chart area
            self.figure.tight_layout(pad=0)
            self.canvas.draw()

        except Exception as e:
            print(f"❌ Error updating background chart: {e}")

    def update_data_table(self, bids: List[Tuple], asks: List[Tuple]):
        """Update data table with order book numbers"""
        try:
            # Calculate cumulative totals
            bid_cumulative = 0
            ask_cumulative = 0

            # Update each row
            for i, row_widgets in enumerate(self.order_rows):
                # Update bid side
                if i < len(bids):
                    qty, orders, price = bids[i]
                    bid_cumulative += qty

                    # Format numbers
                    size_str = f"{qty:,}"
                    total_str = f"{bid_cumulative:,}"
                    price_str = f"₹{price:.2f}"

                    # Update labels with semi-transparent background
                    row_widgets['bid_total'].config(text=total_str, bg=EXECUTIVE_COLORS['bg_secondary'])
                    row_widgets['bid_size'].config(text=size_str, bg=EXECUTIVE_COLORS['bg_secondary'])
                    row_widgets['bid_price'].config(text=price_str, bg=EXECUTIVE_COLORS['bg_secondary'])
                else:
                    # Clear empty rows
                    row_widgets['bid_total'].config(text="", bg=EXECUTIVE_COLORS['bg_primary'])
                    row_widgets['bid_size'].config(text="", bg=EXECUTIVE_COLORS['bg_primary'])
                    row_widgets['bid_price'].config(text="", bg=EXECUTIVE_COLORS['bg_primary'])

                # Update ask side
                if i < len(asks):
                    price, orders, qty = asks[i]
                    ask_cumulative += qty

                    # Format numbers
                    size_str = f"{qty:,}"
                    total_str = f"{ask_cumulative:,}"
                    price_str = f"₹{price:.2f}"

                    # Update labels with semi-transparent background
                    row_widgets['ask_price'].config(text=price_str, bg=EXECUTIVE_COLORS['bg_secondary'])
                    row_widgets['ask_size'].config(text=size_str, bg=EXECUTIVE_COLORS['bg_secondary'])
                    row_widgets['ask_total'].config(text=total_str, bg=EXECUTIVE_COLORS['bg_secondary'])
                else:
                    # Clear empty rows
                    row_widgets['ask_price'].config(text="", bg=EXECUTIVE_COLORS['bg_primary'])
                    row_widgets['ask_size'].config(text="", bg=EXECUTIVE_COLORS['bg_primary'])
                    row_widgets['ask_total'].config(text="", bg=EXECUTIVE_COLORS['bg_primary'])

            # Force update
            self.update_idletasks()

        except Exception as e:
            print(f"❌ Error updating data table: {e}")

#     def update_order_book(self, bids: List[Tuple], asks: List[Tuple]):
#         """Update both chart and data table"""
#         self.update_background_chart(bids, asks)
#         self.update_data_table(bids, asks)


    

    


class TradingSignalWidget(tk.Frame):
    """Trading signals display widget"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, bg=EXECUTIVE_COLORS['bg_card'], **kwargs)
        self.create_signal_display()

    def create_signal_display(self):
        """Create signal display"""
        # Title
        title_label = tk.Label(self, text="⚡ Trading Signals",
                              bg=EXECUTIVE_COLORS['bg_card'],
                              fg=EXECUTIVE_COLORS['text_primary'],
                              font=('Arial', 14, 'bold'))
        title_label.pack(pady=(10, 5))

        # Signal box
        signal_frame = tk.Frame(self, bg=EXECUTIVE_COLORS['success'], height=60)
        signal_frame.pack(fill=tk.X, padx=10, pady=5)
        signal_frame.pack_propagate(False)

        signal_label = tk.Label(signal_frame, text="BUY",
                               bg=EXECUTIVE_COLORS['success'],
                               fg='white',
                               font=('Arial', 18, 'bold'))
        signal_label.pack(expand=True)

        # Details
        details_frame = tk.Frame(self, bg=EXECUTIVE_COLORS['bg_card'])
        details_frame.pack(fill=tk.X, padx=10, pady=5)

        entry_label = tk.Label(details_frame, text="Entry: ₹184.75",
                              bg=EXECUTIVE_COLORS['bg_card'],
                              fg=EXECUTIVE_COLORS['text_secondary'],
                              font=('Arial', 10))
        entry_label.pack()

        target_label = tk.Label(details_frame, text="Target: ₹186.50",
                               bg=EXECUTIVE_COLORS['bg_card'],
                               fg=EXECUTIVE_COLORS['success'],
                               font=('Arial', 10))
        target_label.pack()

        stop_label = tk.Label(details_frame, text="Stop: ₹183.50",
                             bg=EXECUTIVE_COLORS['bg_card'],
                             fg=EXECUTIVE_COLORS['danger'],
                             font=('Arial', 10))
        stop_label.pack()

        # Strength
        strength_frame = tk.Frame(self, bg=EXECUTIVE_COLORS['bg_card'])
        strength_frame.pack(fill=tk.X, padx=10, pady=5)

        strength_title = tk.Label(strength_frame, text="Signal Strength:",
                                 bg=EXECUTIVE_COLORS['bg_card'],
                                 fg=EXECUTIVE_COLORS['text_secondary'],
                                 font=('Arial', 9))
        strength_title.pack()

        strength_value = tk.Label(strength_frame, text="85%",
                                 bg=EXECUTIVE_COLORS['bg_card'],
                                 fg=EXECUTIVE_COLORS['accent_blue'],
                                 font=('Arial', 12, 'bold'))
        strength_value.pack()

class MarketAnalyticsWidget(tk.Frame):
    """Market analytics display widget"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, bg=EXECUTIVE_COLORS['bg_card'], **kwargs)
        self.create_analytics_display()

    def create_analytics_display(self):
        """Create analytics display"""
        # Title
        title_label = tk.Label(self, text="📈 Market Analytics",
                              bg=EXECUTIVE_COLORS['bg_card'],
                              fg=EXECUTIVE_COLORS['text_primary'],
                              font=('Arial', 14, 'bold'))
        title_label.pack(pady=(10, 5))

        # Metrics grid
        metrics_frame = tk.Frame(self, bg=EXECUTIVE_COLORS['bg_card'])
        metrics_frame.pack(fill=tk.X, padx=10, pady=5)

        # Create metric boxes
        metrics = [
            ("Volume", "1.2M", EXECUTIVE_COLORS['accent_blue']),
            ("Spread", "₹0.05", EXECUTIVE_COLORS['warning']),
            ("Volatility", "Low", EXECUTIVE_COLORS['success']),
            ("Trend", "Bullish", EXECUTIVE_COLORS['success']),
            ("RSI", "65.2", EXECUTIVE_COLORS['warning'])
        ]

        for i, (name, value, color) in enumerate(metrics):
            metric_frame = tk.Frame(metrics_frame, bg=EXECUTIVE_COLORS['bg_secondary'],
                                   relief=tk.RAISED, bd=1)
            metric_frame.grid(row=i//3, column=i%3, padx=5, pady=5, sticky='ew')

            name_label = tk.Label(metric_frame, text=name,
                                 bg=EXECUTIVE_COLORS['bg_secondary'],
                                 fg=EXECUTIVE_COLORS['text_secondary'],
                                 font=('Arial', 9))
            name_label.pack(pady=(5, 0))

            value_label = tk.Label(metric_frame, text=value,
                                  bg=EXECUTIVE_COLORS['bg_secondary'],
                                  fg=color,
                                  font=('Arial', 12, 'bold'))
            value_label.pack(pady=(0, 5))

        # Configure grid weights
        for i in range(3):
            metrics_frame.columnconfigure(i, weight=1)

class UltimateTradingAnalysisV2:
    """Main application class - Version 2 with fixes"""

    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()

        # Data source management
        self.current_source = DataSourceType.SIMULATED
        self.is_connected = False
        self.market_simulator = MarketSimulator("CGCL", 850.0)

        # UI components
        self.unified_order_book = None
        self.trading_signals = None
        self.market_analytics = None

        # Connection controls
        self.connect_btn = None
        self.status_label = None
        self.live_btn = None
        self.sim_btn = None

        # Setup callbacks
        self.market_simulator.on_order_book_update = self.on_order_book_received

        self.create_ui()
        self.start_simulation()

    def setup_window(self):
        """Setup main window"""
        self.root.title("Ultimate Trading Analysis v2 - CGCL Live Market")
        self.root.geometry("1400x900")
        self.root.configure(bg=EXECUTIVE_COLORS['bg_primary'])

        # Make window resizable
        self.root.rowconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)

    def create_ui(self):
        """Create main UI layout"""
        # Main container
        main_frame = tk.Frame(self.root, bg=EXECUTIVE_COLORS['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Top control bar
        self.create_control_bar(main_frame)

        # Main content area
        content_frame = tk.Frame(main_frame, bg=EXECUTIVE_COLORS['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # Left panel - Unified Order Book
        left_panel = tk.Frame(content_frame, bg=EXECUTIVE_COLORS['bg_primary'])
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # Professional order book table
        from unified_order_book import UnifiedOrderBookWidget as OrderBookTable
        self.unified_order_book = OrderBookTable(left_panel)
        self.unified_order_book.pack(fill=tk.BOTH, expand=True)

        # Right panel - Signals and Analytics
        right_panel = tk.Frame(content_frame, bg=EXECUTIVE_COLORS['bg_primary'])
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))

        # Trading signals (top right)
        self.trading_signals = TradingSignalWidget(right_panel, width=300)
        self.trading_signals.pack(fill=tk.X, pady=(0, 10))

        # Market analytics (bottom right)
        self.market_analytics = MarketAnalyticsWidget(right_panel, width=300)
        self.market_analytics.pack(fill=tk.BOTH, expand=True)

    def create_control_bar(self, parent):
        """Create top control bar"""
        control_frame = tk.Frame(parent, bg=EXECUTIVE_COLORS['bg_secondary'], height=60)
        control_frame.pack(fill=tk.X)
        control_frame.pack_propagate(False)

        # Left side - Title
        title_label = tk.Label(control_frame,
                              text="🚀 Ultimate Trading Analysis v2 - CGCL Live Market",
                              bg=EXECUTIVE_COLORS['bg_secondary'],
                              fg=EXECUTIVE_COLORS['text_primary'],
                              font=('Arial', 16, 'bold'))
        title_label.pack(side=tk.LEFT, padx=20, pady=15)

        # Right side - Controls
        controls_frame = tk.Frame(control_frame, bg=EXECUTIVE_COLORS['bg_secondary'])
        controls_frame.pack(side=tk.RIGHT, padx=20, pady=10)

        # Data source label
        source_label = tk.Label(controls_frame, text="Data Source:",
                               bg=EXECUTIVE_COLORS['bg_secondary'],
                               fg=EXECUTIVE_COLORS['text_secondary'],
                               font=('Arial', 10))
        source_label.pack(side=tk.LEFT, padx=(0, 10))

        # Live/Sim buttons
        self.live_btn = tk.Button(controls_frame, text="📡 Live",
                                 bg=EXECUTIVE_COLORS['bg_card'],
                                 fg=EXECUTIVE_COLORS['text_primary'],
                                 font=('Arial', 10), width=8,
                                 command=self.switch_to_live)
        self.live_btn.pack(side=tk.LEFT, padx=2)

        self.sim_btn = tk.Button(controls_frame, text="🎯 Sim",
                                bg=EXECUTIVE_COLORS['success'],
                                fg='white',
                                font=('Arial', 10), width=8,
                                command=self.switch_to_sim)
        self.sim_btn.pack(side=tk.LEFT, padx=2)

        # Connect/Disconnect button
        self.connect_btn = tk.Button(controls_frame, text="🔌 Disconnect",
                                    bg=EXECUTIVE_COLORS['danger'],
                                    fg='white',
                                    font=('Arial', 10), width=12,
                                    command=self.toggle_connection)
        self.connect_btn.pack(side=tk.LEFT, padx=(10, 0))

        # Status indicator
        self.status_label = tk.Label(controls_frame, text="🟢 Simulated",
                                    bg=EXECUTIVE_COLORS['bg_secondary'],
                                    fg=EXECUTIVE_COLORS['success'],
                                    font=('Arial', 10, 'bold'))
        self.status_label.pack(side=tk.LEFT, padx=(10, 0))

    def switch_to_live(self):
        """Switch to live data source"""
        if self.current_source != DataSourceType.LIVE:
            print("🔄 Switching to live data source")
            self.current_source = DataSourceType.LIVE
            self.update_data_source_buttons()

            # Stop simulation if running
            if self.market_simulator.is_running:
                self.market_simulator.stop_simulation()
                self.is_connected = False
                self.update_connection_status("DISCONNECTED")

            print("⚠️ Live data source selected - Connect to start live data")

    def switch_to_sim(self):
        """Switch to simulated data source"""
        if self.current_source != DataSourceType.SIMULATED:
            print("🔄 Switching to simulated data source")
            self.current_source = DataSourceType.SIMULATED
            self.update_data_source_buttons()

            # Start simulation automatically
            self.start_simulation()

    def update_data_source_buttons(self):
        """Update data source button states"""
        if self.current_source == DataSourceType.LIVE:
            self.live_btn.config(bg=EXECUTIVE_COLORS['accent_blue'], fg='white')
            self.sim_btn.config(bg=EXECUTIVE_COLORS['bg_card'], fg=EXECUTIVE_COLORS['text_primary'])
        else:
            self.live_btn.config(bg=EXECUTIVE_COLORS['bg_card'], fg=EXECUTIVE_COLORS['text_primary'])
            self.sim_btn.config(bg=EXECUTIVE_COLORS['success'], fg='white')

    def toggle_connection(self):
        """Toggle connection based on current data source"""
        if self.is_connected:
            # Disconnect
            if self.current_source == DataSourceType.SIMULATED:
                self.market_simulator.stop_simulation()
            self.is_connected = False
            self.update_connection_status("DISCONNECTED")
        else:
            # Connect
            if self.current_source == DataSourceType.SIMULATED:
                self.start_simulation()
            else:
                print("⚠️ Live data connection not implemented yet")
                # Here you would implement live data connection

    def start_simulation(self):
        """Start market simulation"""
        if self.current_source == DataSourceType.SIMULATED:
            self.market_simulator.start_simulation()
            self.is_connected = True
            self.update_connection_status("CONNECTED_SIMULATED")

    def update_connection_status(self, status: str):
        """Update connection status display"""
        if status == "CONNECTED_SIMULATED":
            self.is_connected = True
            self.connect_btn.config(text="🔌 Disconnect", bg=EXECUTIVE_COLORS['danger'])
            self.status_label.config(text="🟢 Simulated", fg=EXECUTIVE_COLORS['success'])
        elif status == "CONNECTED_LIVE":
            self.is_connected = True
            self.connect_btn.config(text="🔌 Disconnect", bg=EXECUTIVE_COLORS['danger'])
            self.status_label.config(text="🟢 Live Data", fg=EXECUTIVE_COLORS['success'])
        elif status == "DISCONNECTED":
            self.is_connected = False
            self.connect_btn.config(text="🔌 Connect", bg=EXECUTIVE_COLORS['success'])
            self.status_label.config(text="⚫ Disconnected", fg=EXECUTIVE_COLORS['text_secondary'])

    def on_order_book_received(self, symbol: str, order_book: OrderBookSnapshot):
        """Handle order book data from data source"""
        try:
            # Convert to format expected by UI
            bids = [(level.quantity, level.orders, level.price) for level in order_book.bids]
            asks = [(level.price, level.orders, level.quantity) for level in order_book.asks]

            # Update UI in main thread
            def update_ui():
                self.unified_order_book.update_order_book(bids, asks)

            self.root.after(0, update_ui)

            # Show periodic updates
            if hasattr(self, '_update_count'):
                self._update_count += 1
            else:
                self._update_count = 1

            if self._update_count % 10 == 0:  # Every 10th update
                best_bid = order_book.bids[0].price if order_book.bids else 0
                best_ask = order_book.asks[0].price if order_book.asks else 0
                spread = best_ask - best_bid if best_bid and best_ask else 0
                print(f"📊 {symbol} - Best Bid: ₹{best_bid:.2f}, Best Ask: ₹{best_ask:.2f}, Spread: ₹{spread:.2f}")

        except Exception as e:
            print(f"❌ Error processing order book: {e}")

    def run(self):
        """Start the application"""
        print("🚀 Starting Ultimate Trading Analysis v2...")
        print("✅ Order book display with 10 levels each side")
        print("✅ Live/Simulated data source switching")
        print("✅ Real-time updates every second")
        print("🎯 CGCL simulation started automatically")

        self.root.mainloop()

def main():
    """Main entry point"""
    try:
        app = UltimateTradingAnalysisV2()
        app.run()
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Application error: {e}")

if __name__ == "__main__":
    main()
