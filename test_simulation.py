"""
Test script for CGCL Market Simulation
"""

import sys
import os

# Add the project root to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'updatted best plan'))

from gui.main_window import CGCLMainWindow


def main():
    """Test the CGCL simulation"""
    print("🚀 Starting CGCL Market Simulation Test")
    print("="*50)
    
    try:
        # Create main window
        app = CGCLMainWindow()
        
        print("✅ Application initialized successfully")
        print("📊 Simulation should start automatically")
        print("🎯 CGCL order book data updating every second")
        print("🔄 Use the Live/Sim buttons to switch data sources")
        print("🔌 Use Connect/Disconnect to control data flow")
        print("\nPress Ctrl+C or close window to exit")
        
        # Run the application
        app.run()
        
    except KeyboardInterrupt:
        print("\n⏹️ Simulation stopped by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
