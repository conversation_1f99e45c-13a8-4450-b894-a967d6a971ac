"""
Market Hours and Trading Session Utilities
"""

from datetime import datetime, time
from typing import Dict, Tuple


class MarketHoursManager:
    """Manage market hours and trading sessions"""
    
    def __init__(self):
        # Indian market hours (IST)
        self.market_start = time(9, 15)  # 9:15 AM
        self.market_end = time(15, 30)   # 3:30 PM
        
        # Pre-market and post-market hours
        self.pre_market_start = time(9, 0)   # 9:00 AM
        self.post_market_end = time(16, 0)   # 4:00 PM
        
        # Last known valid market data
        self.last_valid_price = None
        self.last_valid_data = None
        self.last_market_close_price = None
        
    def is_market_open(self, current_time: datetime = None) -> bool:
        """Check if market is currently open"""
        if current_time is None:
            current_time = datetime.now()
        
        # Check if it's a weekend
        if current_time.weekday() >= 5:  # Saturday=5, Sunday=6
            return False
        
        current_time_only = current_time.time()
        return self.market_start <= current_time_only <= self.market_end
    
    def is_trading_day(self, current_time: datetime = None) -> bool:
        """Check if today is a trading day (weekday)"""
        if current_time is None:
            current_time = datetime.now()
        
        return current_time.weekday() < 5  # Monday=0 to Friday=4
    
    def get_market_status(self, current_time: datetime = None) -> Dict:
        """Get comprehensive market status"""
        if current_time is None:
            current_time = datetime.now()
        
        status = {
            'is_open': False,
            'is_trading_day': self.is_trading_day(current_time),
            'session': 'CLOSED',
            'status_text': 'Market Closed',
            'color': '#FF4444',  # Red
            'time_to_next_session': None,
            'should_process_data': False
        }
        
        if not status['is_trading_day']:
            status.update({
                'session': 'WEEKEND',
                'status_text': 'Weekend - Market Closed',
                'should_process_data': False
            })
            return status
        
        current_time_only = current_time.time()
        
        if current_time_only < self.market_start:
            # Pre-market
            status.update({
                'session': 'PRE_MARKET',
                'status_text': 'Pre-Market',
                'color': '#FFA500',  # Orange
                'should_process_data': False
            })
            
            # Calculate time to market open
            market_open_today = datetime.combine(current_time.date(), self.market_start)
            time_diff = market_open_today - current_time
            status['time_to_next_session'] = time_diff
            
        elif self.market_start <= current_time_only <= self.market_end:
            # Market is open
            status.update({
                'is_open': True,
                'session': 'MARKET_OPEN',
                'status_text': 'Market Open',
                'color': '#00AA00',  # Green
                'should_process_data': True
            })
            
            # Calculate time to market close
            market_close_today = datetime.combine(current_time.date(), self.market_end)
            time_diff = market_close_today - current_time
            status['time_to_next_session'] = time_diff
            
            # Special handling for closing time
            if time_diff.total_seconds() <= 1800:  # Last 30 minutes
                status.update({
                    'status_text': 'Market Closing Soon',
                    'color': '#FF6600'  # Orange-Red
                })
                
        else:
            # Post-market
            status.update({
                'session': 'POST_MARKET',
                'status_text': 'Market Closed',
                'color': '#FF4444',  # Red
                'should_process_data': False
            })
        
        return status
    
    def validate_market_data(self, price: float, volume: int = 0, 
                           current_time: datetime = None) -> bool:
        """Validate if market data is valid for current market status"""
        if current_time is None:
            current_time = datetime.now()
        
        market_status = self.get_market_status(current_time)
        
        # If market is closed, don't accept new price data
        if not market_status['should_process_data']:
            return False
        
        # Validate price data
        if price <= 0 or price > 10000:  # Reasonable price range for CGCL
            return False
        
        # Additional validation
        if self.last_valid_price:
            # Reject prices that are too different from last valid price
            price_change_pct = abs((price - self.last_valid_price) / self.last_valid_price) * 100
            if price_change_pct > 20:  # More than 20% change is suspicious
                return False
        
        return True
    
    def update_last_valid_data(self, price: float, market_data: Dict = None):
        """Update last known valid market data"""
        if price > 0:
            self.last_valid_price = price
            
        if market_data:
            self.last_valid_data = market_data.copy()
            
        # If market just closed, store the closing price
        market_status = self.get_market_status()
        if not market_status['is_open'] and self.last_valid_price:
            self.last_market_close_price = self.last_valid_price
    
    def get_display_price(self, current_price: float = None) -> Tuple[float, str]:
        """Get price to display based on market status"""
        market_status = self.get_market_status()
        
        if market_status['should_process_data'] and current_price and current_price > 0:
            # Market is open and we have valid current price
            return current_price, "LIVE"
        
        elif self.last_market_close_price:
            # Market is closed, show last closing price
            return self.last_market_close_price, "CLOSED"
        
        elif self.last_valid_price:
            # Fallback to last valid price
            return self.last_valid_price, "LAST_KNOWN"
        
        else:
            # No valid price data available
            return 180.0, "ESTIMATED"  # CGCL typical price range
    
    def should_update_order_book(self, current_time: datetime = None) -> bool:
        """Check if order book should be updated"""
        market_status = self.get_market_status(current_time)
        return market_status['should_process_data']
    
    def get_market_session_info(self, current_time: datetime = None) -> Dict:
        """Get detailed market session information for display"""
        if current_time is None:
            current_time = datetime.now()
        
        market_status = self.get_market_status(current_time)
        
        info = {
            'status': market_status['status_text'],
            'color': market_status['color'],
            'is_open': market_status['is_open'],
            'session': market_status['session'],
            'time_info': ''
        }
        
        if market_status['time_to_next_session']:
            time_diff = market_status['time_to_next_session']
            hours = int(time_diff.total_seconds() // 3600)
            minutes = int((time_diff.total_seconds() % 3600) // 60)
            
            if market_status['is_open']:
                if hours > 0:
                    info['time_info'] = f"Closes in {hours}h {minutes}m"
                else:
                    info['time_info'] = f"Closes in {minutes}m"
            else:
                if hours > 0:
                    info['time_info'] = f"Opens in {hours}h {minutes}m"
                else:
                    info['time_info'] = f"Opens in {minutes}m"
        
        return info


# Global instance
market_hours = MarketHoursManager()
