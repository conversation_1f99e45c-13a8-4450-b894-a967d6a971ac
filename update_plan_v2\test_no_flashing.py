"""
Test script to verify NO FLASHING occurs with optimized UI updates
Tests rapid updates to ensure only values change, not structure
"""

import tkinter as tk
import random
import time
from order_book_ui import OrderBookUI
from depth_chart_ui import DepthChartUI

def test_no_flashing():
    """Test rapid updates to ensure no flashing occurs"""
    print("🧪 Testing NO FLASHING with rapid updates...")
    
    # Create test window
    root = tk.Tk()
    root.title("NO FLASHING Test - Rapid Updates")
    root.geometry("1000x800")
    root.configure(bg='#0a0a0a')
    
    # Create main container
    main_frame = tk.Frame(root, bg='#0a0a0a')
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # Title
    title_label = tk.Label(main_frame, text="🚀 NO FLASHING TEST - RAPID UPDATES", 
                          font=('Consolas', 16, 'bold'),
                          bg='#0a0a0a', fg='#00ff88')
    title_label.pack(pady=(0, 10))
    
    # Status
    status_label = tk.Label(main_frame, text="Testing: Only VALUES should change, structure should remain stable", 
                           font=('Consolas', 12),
                           bg='#0a0a0a', fg='#ffaa00')
    status_label.pack(pady=(0, 10))
    
    # Order Book Container
    book_container = tk.Frame(main_frame, bg='#0a0a0a')
    book_container.pack(fill=tk.X, pady=(0, 5))
    
    # Initialize OrderBookUI
    order_book_ui = OrderBookUI(book_container)
    
    # Depth Chart Container
    chart_container = tk.Frame(main_frame, bg='#0a0a0a')
    chart_container.pack(fill=tk.X, pady=(5, 5))
    
    # Initialize DepthChartUI
    depth_chart_ui = DepthChartUI(chart_container)
    
    # Test data generation with small variations
    base_price = 186.50
    update_count = 0
    
    def generate_slightly_different_data():
        """Generate data with small variations to test update efficiency"""
        nonlocal update_count
        update_count += 1
        
        # Small price variations
        price_variation = random.uniform(-0.10, 0.10)
        current_base = base_price + price_variation
        
        # Generate 20 bid levels
        bids = []
        for i in range(20):
            price = current_base - (0.05 * (i + 1))
            # Small quantity variations
            qty = 1000 + random.randint(-100, 100)
            orders = random.randint(5, 15)
            
            # Mark top 5 as DEPTH for highlighting
            source = 'DEPTH' if i < 5 else 'LTP_GENERATED'
            bids.append([qty, orders, price, source])
        
        # Generate 20 ask levels
        asks = []
        for i in range(20):
            price = current_base + (0.05 * (i + 1))
            # Small quantity variations
            qty = 1000 + random.randint(-100, 100)
            orders = random.randint(5, 15)
            
            # Mark top 5 as DEPTH for highlighting
            source = 'DEPTH' if i < 5 else 'LTP_GENERATED'
            asks.append([price, orders, qty, source])
        
        return bids, asks
    
    # Rapid update function
    def rapid_update():
        """Update both components rapidly to test for flashing"""
        try:
            bids, asks = generate_slightly_different_data()
            
            # Update both components
            order_book_ui.update_display(bids, asks)
            depth_chart_ui.update_chart(bids, asks)
            
            # Update status
            status_label.config(text=f"Update #{update_count} - No flashing should occur!")
            
        except Exception as e:
            print(f"❌ Error in rapid update: {e}")
        
        # Schedule next rapid update (every 100ms for stress test)
        root.after(100, rapid_update)
    
    # Control buttons
    control_frame = tk.Frame(main_frame, bg='#0a0a0a')
    control_frame.pack(fill=tk.X, pady=(10, 0))
    
    def start_stress_test():
        """Start stress test with very rapid updates"""
        print("🚀 Starting stress test - 10 updates per second")
        rapid_update()
    
    def stop_test():
        """Stop the test"""
        root.quit()
    
    start_btn = tk.Button(control_frame, text="Start Stress Test (10/sec)", 
                         command=start_stress_test,
                         bg='#00ff88', fg='black', font=('Consolas', 10, 'bold'))
    start_btn.pack(side=tk.LEFT, padx=(0, 10))
    
    stop_btn = tk.Button(control_frame, text="Stop Test", 
                        command=stop_test,
                        bg='#ff0844', fg='white', font=('Consolas', 10, 'bold'))
    stop_btn.pack(side=tk.LEFT, padx=(0, 10))
    
    # Instructions
    instructions = tk.Label(control_frame, 
                           text="Watch for flashing - there should be NONE! Only numbers should change.", 
                           font=('Consolas', 10),
                           bg='#0a0a0a', fg='#888888')
    instructions.pack(side=tk.RIGHT)
    
    print("✅ NO FLASHING test ready")
    print("📊 Features being tested:")
    print("   - Rapid updates (10 per second)")
    print("   - Structure stability (no recreation)")
    print("   - Value-only updates")
    print("   - Top 5 highlighting preservation")
    print("   - Depth chart optimization")
    print("\n🎯 Expected result: NO FLASHING, only smooth value updates")
    
    # Run the test
    root.mainloop()

if __name__ == "__main__":
    test_no_flashing()
