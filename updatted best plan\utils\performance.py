"""
Performance Monitoring and Optimization
"""

import time
import threading
import psutil
import gc
from collections import deque
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass

from config.settings import PERFORMANCE_THRESHOLDS
from .helpers import CircularBuffer, timing_decorator


@dataclass
class LatencyMetrics:
    """Latency measurement data"""
    operation: str
    start_time: float
    end_time: float
    duration_ms: float
    timestamp: datetime


class LatencyTracker:
    """Track and optimize system latency"""
    
    def __init__(self, max_samples: int = 1000):
        self.metrics = CircularBuffer(max_samples)
        self.operation_stats = {}
        self.lock = threading.Lock()
        
    def start_timing(self, operation: str) -> float:
        """Start timing an operation"""
        return time.perf_counter()
    
    def end_timing(self, operation: str, start_time: float):
        """End timing and record metrics"""
        end_time = time.perf_counter()
        duration_ms = (end_time - start_time) * 1000
        
        metric = LatencyMetrics(
            operation=operation,
            start_time=start_time,
            end_time=end_time,
            duration_ms=duration_ms,
            timestamp=datetime.now()
        )
        
        with self.lock:
            self.metrics.append(metric)
            
            # Update operation statistics
            if operation not in self.operation_stats:
                self.operation_stats[operation] = {
                    'count': 0,
                    'total_time': 0.0,
                    'min_time': float('inf'),
                    'max_time': 0.0,
                    'recent_times': deque(maxlen=100)
                }
            
            stats = self.operation_stats[operation]
            stats['count'] += 1
            stats['total_time'] += duration_ms
            stats['min_time'] = min(stats['min_time'], duration_ms)
            stats['max_time'] = max(stats['max_time'], duration_ms)
            stats['recent_times'].append(duration_ms)
    
    def get_operation_stats(self, operation: str) -> Dict:
        """Get statistics for a specific operation"""
        with self.lock:
            if operation not in self.operation_stats:
                return {}
            
            stats = self.operation_stats[operation]
            recent_times = list(stats['recent_times'])
            
            return {
                'operation': operation,
                'count': stats['count'],
                'avg_time_ms': stats['total_time'] / stats['count'],
                'min_time_ms': stats['min_time'],
                'max_time_ms': stats['max_time'],
                'recent_avg_ms': sum(recent_times) / len(recent_times) if recent_times else 0,
                'p95_ms': sorted(recent_times)[int(len(recent_times) * 0.95)] if recent_times else 0,
                'p99_ms': sorted(recent_times)[int(len(recent_times) * 0.99)] if recent_times else 0
            }
    
    def get_all_stats(self) -> Dict:
        """Get statistics for all operations"""
        return {op: self.get_operation_stats(op) for op in self.operation_stats.keys()}
    
    def get_slow_operations(self, threshold_ms: float = 50) -> List[Dict]:
        """Get operations that exceed latency threshold"""
        slow_ops = []
        for operation, stats in self.get_all_stats().items():
            if stats.get('recent_avg_ms', 0) > threshold_ms:
                slow_ops.append(stats)
        return sorted(slow_ops, key=lambda x: x['recent_avg_ms'], reverse=True)


class MemoryOptimizer:
    """Memory usage optimization and monitoring"""
    
    def __init__(self):
        self.memory_history = CircularBuffer(300)  # 5 minutes of samples
        self.gc_stats = {
            'collections': 0,
            'objects_collected': 0,
            'time_spent_ms': 0
        }
        self.last_gc_time = time.time()
        
    def get_memory_usage(self) -> Dict:
        """Get current memory usage"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            usage = {
                'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
                'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
                'percent': process.memory_percent(),
                'available_mb': psutil.virtual_memory().available / 1024 / 1024,
                'timestamp': datetime.now()
            }
            
            self.memory_history.append(usage)
            return usage
            
        except Exception as e:
            print(f"Error getting memory usage: {e}")
            return {}
    
    def optimize_memory(self, force: bool = False) -> Dict:
        """Optimize memory usage"""
        start_time = time.perf_counter()
        
        # Get memory before optimization
        before_memory = self.get_memory_usage()
        
        # Force garbage collection
        if force or self._should_run_gc():
            collected = gc.collect()
            self.gc_stats['collections'] += 1
            self.gc_stats['objects_collected'] += collected
            self.last_gc_time = time.time()
        
        # Get memory after optimization
        after_memory = self.get_memory_usage()
        
        end_time = time.perf_counter()
        optimization_time = (end_time - start_time) * 1000
        self.gc_stats['time_spent_ms'] += optimization_time
        
        return {
            'before_mb': before_memory.get('rss_mb', 0),
            'after_mb': after_memory.get('rss_mb', 0),
            'freed_mb': before_memory.get('rss_mb', 0) - after_memory.get('rss_mb', 0),
            'optimization_time_ms': optimization_time,
            'objects_collected': collected if 'collected' in locals() else 0
        }
    
    def _should_run_gc(self) -> bool:
        """Determine if garbage collection should run"""
        # Run GC if memory usage is high or it's been a while
        current_memory = self.get_memory_usage()
        memory_threshold = PERFORMANCE_THRESHOLDS.get('max_memory_usage_mb', 512)
        time_threshold = 300  # 5 minutes
        
        return (
            current_memory.get('rss_mb', 0) > memory_threshold * 0.8 or
            time.time() - self.last_gc_time > time_threshold
        )
    
    def get_memory_trend(self, minutes: int = 5) -> Dict:
        """Get memory usage trend"""
        try:
            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            recent_samples = [
                sample for sample in self.memory_history.get_all()
                if sample and sample.get('timestamp', datetime.min) > cutoff_time
            ]
            
            if len(recent_samples) < 2:
                return {'trend': 'INSUFFICIENT_DATA'}
            
            # Calculate trend
            first_sample = recent_samples[0]['rss_mb']
            last_sample = recent_samples[-1]['rss_mb']
            change_mb = last_sample - first_sample
            change_percent = (change_mb / first_sample) * 100 if first_sample > 0 else 0
            
            # Determine trend direction
            if change_percent > 10:
                trend = 'INCREASING'
            elif change_percent < -10:
                trend = 'DECREASING'
            else:
                trend = 'STABLE'
            
            return {
                'trend': trend,
                'change_mb': change_mb,
                'change_percent': change_percent,
                'current_mb': last_sample,
                'samples': len(recent_samples)
            }
            
        except Exception as e:
            print(f"Error calculating memory trend: {e}")
            return {'trend': 'ERROR'}


class CPUOptimizer:
    """CPU usage optimization and monitoring"""
    
    def __init__(self):
        self.cpu_history = CircularBuffer(300)
        self.thread_pool_size = 4
        self.adaptive_threading = True
        
    def get_cpu_usage(self) -> Dict:
        """Get current CPU usage"""
        try:
            cpu_percent = psutil.cpu_percent(interval=0.1)
            cpu_count = psutil.cpu_count()
            load_avg = psutil.getloadavg() if hasattr(psutil, 'getloadavg') else (0, 0, 0)
            
            usage = {
                'cpu_percent': cpu_percent,
                'cpu_count': cpu_count,
                'load_avg_1min': load_avg[0],
                'load_avg_5min': load_avg[1],
                'load_avg_15min': load_avg[2],
                'timestamp': datetime.now()
            }
            
            self.cpu_history.append(usage)
            return usage
            
        except Exception as e:
            print(f"Error getting CPU usage: {e}")
            return {}
    
    def optimize_thread_pool(self) -> int:
        """Optimize thread pool size based on CPU usage"""
        if not self.adaptive_threading:
            return self.thread_pool_size
        
        cpu_usage = self.get_cpu_usage()
        cpu_percent = cpu_usage.get('cpu_percent', 50)
        
        # Adjust thread pool size based on CPU usage
        if cpu_percent > 80:
            # High CPU usage - reduce threads
            self.thread_pool_size = max(2, self.thread_pool_size - 1)
        elif cpu_percent < 30:
            # Low CPU usage - can increase threads
            self.thread_pool_size = min(8, self.thread_pool_size + 1)
        
        return self.thread_pool_size
    
    def get_cpu_trend(self, minutes: int = 5) -> Dict:
        """Get CPU usage trend"""
        try:
            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            recent_samples = [
                sample for sample in self.cpu_history.get_all()
                if sample and sample.get('timestamp', datetime.min) > cutoff_time
            ]
            
            if len(recent_samples) < 2:
                return {'trend': 'INSUFFICIENT_DATA'}
            
            # Calculate average CPU usage
            avg_cpu = sum(sample['cpu_percent'] for sample in recent_samples) / len(recent_samples)
            
            # Determine trend
            if avg_cpu > 70:
                trend = 'HIGH'
            elif avg_cpu > 40:
                trend = 'MODERATE'
            else:
                trend = 'LOW'
            
            return {
                'trend': trend,
                'avg_cpu_percent': avg_cpu,
                'current_cpu_percent': recent_samples[-1]['cpu_percent'],
                'samples': len(recent_samples),
                'recommended_threads': self.optimize_thread_pool()
            }
            
        except Exception as e:
            print(f"Error calculating CPU trend: {e}")
            return {'trend': 'ERROR'}


class PerformanceOptimizer:
    """Main performance optimization coordinator"""
    
    def __init__(self):
        self.latency_tracker = LatencyTracker()
        self.memory_optimizer = MemoryOptimizer()
        self.cpu_optimizer = CPUOptimizer()
        
        # Optimization settings
        self.auto_optimize = True
        self.optimization_interval = 60  # seconds
        self.last_optimization = time.time()
        
        # Performance alerts
        self.alert_callbacks: List[Callable] = []
        
    def add_alert_callback(self, callback: Callable):
        """Add callback for performance alerts"""
        self.alert_callbacks.append(callback)
    
    def track_operation(self, operation: str):
        """Context manager for tracking operation latency"""
        class OperationTracker:
            def __init__(self, tracker, op_name):
                self.tracker = tracker
                self.operation = op_name
                self.start_time = None
            
            def __enter__(self):
                self.start_time = self.tracker.latency_tracker.start_timing(self.operation)
                return self
            
            def __exit__(self, exc_type, exc_val, exc_tb):
                self.tracker.latency_tracker.end_timing(self.operation, self.start_time)
        
        return OperationTracker(self, operation)
    
    def get_system_health(self) -> Dict:
        """Get overall system health metrics"""
        try:
            memory_usage = self.memory_optimizer.get_memory_usage()
            cpu_usage = self.cpu_optimizer.get_cpu_usage()
            latency_stats = self.latency_tracker.get_all_stats()
            
            # Calculate health scores
            memory_score = max(0, 100 - memory_usage.get('percent', 0))
            cpu_score = max(0, 100 - cpu_usage.get('cpu_percent', 0))
            
            # Latency score based on average response times
            avg_latencies = [stats.get('recent_avg_ms', 0) for stats in latency_stats.values()]
            avg_latency = sum(avg_latencies) / len(avg_latencies) if avg_latencies else 0
            latency_score = max(0, 100 - min(100, avg_latency))
            
            overall_score = (memory_score + cpu_score + latency_score) / 3
            
            # Determine health status
            if overall_score >= 80:
                health_status = 'EXCELLENT'
            elif overall_score >= 60:
                health_status = 'GOOD'
            elif overall_score >= 40:
                health_status = 'FAIR'
            else:
                health_status = 'POOR'
            
            return {
                'overall_score': overall_score,
                'health_status': health_status,
                'memory_score': memory_score,
                'cpu_score': cpu_score,
                'latency_score': latency_score,
                'memory_usage_mb': memory_usage.get('rss_mb', 0),
                'cpu_usage_percent': cpu_usage.get('cpu_percent', 0),
                'avg_latency_ms': avg_latency,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            print(f"Error getting system health: {e}")
            return {'health_status': 'ERROR'}
    
    def auto_optimize_system(self) -> Dict:
        """Automatically optimize system performance"""
        if not self.auto_optimize:
            return {'status': 'AUTO_OPTIMIZATION_DISABLED'}
        
        current_time = time.time()
        if current_time - self.last_optimization < self.optimization_interval:
            return {'status': 'TOO_SOON'}
        
        optimization_results = {}
        
        try:
            # Memory optimization
            memory_result = self.memory_optimizer.optimize_memory()
            optimization_results['memory'] = memory_result
            
            # CPU optimization
            cpu_result = self.cpu_optimizer.optimize_thread_pool()
            optimization_results['cpu'] = {'thread_pool_size': cpu_result}
            
            # Check for performance alerts
            self._check_performance_alerts()
            
            self.last_optimization = current_time
            optimization_results['status'] = 'SUCCESS'
            optimization_results['timestamp'] = datetime.now()
            
            return optimization_results
            
        except Exception as e:
            print(f"Error in auto optimization: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    def _check_performance_alerts(self):
        """Check for performance issues and trigger alerts"""
        try:
            health = self.get_system_health()
            
            # Memory alert
            if health.get('memory_usage_mb', 0) > PERFORMANCE_THRESHOLDS.get('max_memory_usage_mb', 512):
                self._trigger_alert('HIGH_MEMORY_USAGE', health)
            
            # CPU alert
            if health.get('cpu_usage_percent', 0) > PERFORMANCE_THRESHOLDS.get('max_cpu_usage_percent', 80):
                self._trigger_alert('HIGH_CPU_USAGE', health)
            
            # Latency alert
            if health.get('avg_latency_ms', 0) > PERFORMANCE_THRESHOLDS.get('max_latency_ms', 100):
                self._trigger_alert('HIGH_LATENCY', health)
            
        except Exception as e:
            print(f"Error checking performance alerts: {e}")
    
    def _trigger_alert(self, alert_type: str, data: Dict):
        """Trigger performance alert"""
        alert_data = {
            'type': alert_type,
            'timestamp': datetime.now(),
            'data': data
        }
        
        for callback in self.alert_callbacks:
            try:
                callback(alert_data)
            except Exception as e:
                print(f"Error in alert callback: {e}")
    
    def get_optimization_recommendations(self) -> List[str]:
        """Get performance optimization recommendations"""
        recommendations = []
        
        try:
            health = self.get_system_health()
            slow_operations = self.latency_tracker.get_slow_operations()
            memory_trend = self.memory_optimizer.get_memory_trend()
            cpu_trend = self.cpu_optimizer.get_cpu_trend()
            
            # Memory recommendations
            if health.get('memory_usage_mb', 0) > 400:
                recommendations.append("Consider reducing historical data retention")
                recommendations.append("Enable more aggressive garbage collection")
            
            # CPU recommendations
            if health.get('cpu_usage_percent', 0) > 70:
                recommendations.append("Reduce GUI update frequency")
                recommendations.append("Optimize data processing algorithms")
            
            # Latency recommendations
            if slow_operations:
                recommendations.append(f"Optimize slow operation: {slow_operations[0]['operation']}")
            
            # Trend-based recommendations
            if memory_trend.get('trend') == 'INCREASING':
                recommendations.append("Memory usage is increasing - check for memory leaks")
            
            if cpu_trend.get('trend') == 'HIGH':
                recommendations.append("CPU usage is high - consider reducing processing load")
            
            return recommendations
            
        except Exception as e:
            print(f"Error getting recommendations: {e}")
            return ["Error generating recommendations"]


# Global performance optimizer instance
performance_optimizer = PerformanceOptimizer()
