"""
Order Flow Engine Setup and Configuration
=========================================

This script sets up the order flow engine with proper configuration
and provides utilities for getting started.
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OrderFlowSetup:
    """Setup and configuration manager for order flow engine"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
    def create_default_config(self):
        """Create default configuration files"""
        
        # Main configuration
        main_config = {
            'api': {
                'smart_api_key': 'your_smart_api_key_here',
                'access_token': 'your_access_token_here',
                'base_url': 'https://apiconnect.angelone.in',
                'websocket_url': 'wss://smartapisocket.angelone.in/smart-stream'
            },
            'symbols': {
                'default_list': ['RELIANCE', 'TCS', 'INFY', 'HDFCBANK', 'ICICIBANK'],
                'max_symbols': 10,
                'market_cap_filter': 10000  # Minimum market cap in crores
            },
            'order_flow': {
                'lookback_minutes': 30,
                'volume_spike_threshold': 2.0,
                'large_order_threshold': 1000000,  # ₹10 lakh
                'imbalance_threshold': 0.7,
                'vwap_deviation_threshold': 0.005,
                'institutional_volume_threshold': 5000000  # ₹50 lakh
            },
            'signals': {
                'strength_threshold': 0.7,
                'confidence_threshold': 0.6,
                'max_signals_per_hour': 20
            },
            'risk': {
                'max_position_size': 0.025,  # 2.5% of capital
                'stop_loss_percentage': 0.005,  # 0.5%
                'profit_target_percentage': 0.012,  # 1.2%
                'max_daily_loss': 0.02,  # 2%
                'max_simultaneous_positions': 3
            },
            'logging': {
                'level': 'INFO',
                'file_path': 'logs/order_flow.log',
                'max_file_size_mb': 100,
                'backup_count': 5
            }
        }
        
        # Save main config
        config_file = self.config_dir / 'config.yaml'
        with open(config_file, 'w') as f:
            yaml.dump(main_config, f, default_flow_style=False, indent=2)
        
        logger.info(f"Created main config: {config_file}")
        
        # Stock universe configuration
        stock_universe = {
            'large_cap': [
                'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'HINDUNILVR',
                'ICICIBANK', 'KOTAKBANK', 'SBIN', 'BHARTIARTL', 'ITC',
                'ASIANPAINT', 'LT', 'AXISBANK', 'MARUTI', 'SUNPHARMA',
                'ULTRACEMCO', 'TITAN', 'WIPRO', 'NESTLEIND', 'POWERGRID'
            ],
            'mid_cap': [
                'ADANIPORTS', 'NTPC', 'JSWSTEEL', 'TATAMOTORS', 'INDUSINDBK',
                'BAJFINANCE', 'HCLTECH', 'COALINDIA', 'BAJAJFINSV', 'GRASIM'
            ],
            'sectors': {
                'banking': ['HDFCBANK', 'ICICIBANK', 'KOTAKBANK', 'SBIN', 'AXISBANK'],
                'it': ['TCS', 'INFY', 'WIPRO', 'HCLTECH'],
                'auto': ['MARUTI', 'TATAMOTORS'],
                'pharma': ['SUNPHARMA'],
                'fmcg': ['HINDUNILVR', 'ITC', 'NESTLEIND']
            }
        }
        
        universe_file = self.config_dir / 'stock_universe.yaml'
        with open(universe_file, 'w') as f:
            yaml.dump(stock_universe, f, default_flow_style=False, indent=2)
        
        logger.info(f"Created stock universe: {universe_file}")
        
        # Daily stock selection template
        daily_stocks = {
            'date': '2025-01-XX',
            'selected_stocks': [],
            'selection_criteria': {
                'gap_up_percentage': 1.0,
                'pre_market_volume_ratio': 1.5,
                'sector_diversification': True,
                'max_stocks_per_sector': 2
            },
            'notes': 'Manual selection based on pre-market analysis'
        }
        
        daily_file = self.config_dir / 'daily_stocks_template.yaml'
        with open(daily_file, 'w') as f:
            yaml.dump(daily_stocks, f, default_flow_style=False, indent=2)
        
        logger.info(f"Created daily stocks template: {daily_file}")
        
    def load_config(self) -> Dict:
        """Load configuration from file"""
        config_file = self.config_dir / 'config.yaml'
        
        if not config_file.exists():
            logger.warning("Config file not found, creating default...")
            self.create_default_config()
        
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        return config
    
    def update_api_credentials(self, api_key: str, access_token: str):
        """Update API credentials in config"""
        config = self.load_config()
        config['api']['smart_api_key'] = api_key
        config['api']['access_token'] = access_token
        
        config_file = self.config_dir / 'config.yaml'
        with open(config_file, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
        
        logger.info("API credentials updated")
    
    def get_daily_stocks(self, date: str = None) -> List[str]:
        """Get daily selected stocks"""
        if date is None:
            from datetime import datetime
            date = datetime.now().strftime('%Y-%m-%d')
        
        daily_file = self.config_dir / f'daily_stocks_{date}.yaml'
        
        if not daily_file.exists():
            logger.warning(f"Daily stocks file not found for {date}")
            return []
        
        with open(daily_file, 'r') as f:
            daily_data = yaml.safe_load(f)
        
        return daily_data.get('selected_stocks', [])
    
    def save_daily_stocks(self, stocks: List[str], date: str = None, notes: str = ""):
        """Save daily selected stocks"""
        if date is None:
            from datetime import datetime
            date = datetime.now().strftime('%Y-%m-%d')
        
        daily_data = {
            'date': date,
            'selected_stocks': stocks,
            'selection_time': datetime.now().isoformat(),
            'notes': notes
        }
        
        daily_file = self.config_dir / f'daily_stocks_{date}.yaml'
        with open(daily_file, 'w') as f:
            yaml.dump(daily_data, f, default_flow_style=False, indent=2)
        
        logger.info(f"Saved {len(stocks)} stocks for {date}")
    
    def setup_logging(self, config: Dict):
        """Setup logging configuration"""
        log_config = config.get('logging', {})
        
        # Create logs directory
        log_path = Path(log_config.get('file_path', 'logs/order_flow.log'))
        log_path.parent.mkdir(exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, log_config.get('level', 'INFO')),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_path),
                logging.StreamHandler()
            ]
        )
        
        logger.info(f"Logging configured: {log_path}")
    
    def validate_config(self, config: Dict) -> bool:
        """Validate configuration"""
        required_keys = [
            'api.smart_api_key',
            'api.access_token',
            'symbols.default_list',
            'order_flow.volume_spike_threshold'
        ]
        
        for key in required_keys:
            keys = key.split('.')
            value = config
            
            try:
                for k in keys:
                    value = value[k]
                
                if not value or value == f'your_{keys[-1]}_here':
                    logger.error(f"Configuration key '{key}' not properly set")
                    return False
                    
            except KeyError:
                logger.error(f"Missing configuration key: {key}")
                return False
        
        logger.info("Configuration validation passed")
        return True
    
    def create_project_structure(self):
        """Create complete project structure"""
        directories = [
            'config',
            'logs',
            'data',
            'exports',
            'backups'
        ]
        
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
            logger.info(f"Created directory: {directory}")
        
        # Create .gitignore
        gitignore_content = """
# Configuration files with sensitive data
config/config.yaml
config/daily_stocks_*.yaml

# Log files
logs/
*.log

# Data files
data/
exports/
backups/

# Python cache
__pycache__/
*.pyc
*.pyo

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db
"""
        
        with open('.gitignore', 'w') as f:
            f.write(gitignore_content.strip())
        
        logger.info("Created .gitignore")
    
    def install_dependencies(self):
        """Generate requirements.txt"""
        requirements = [
            'numpy>=1.21.0',
            'pandas>=1.3.0',
            'pyyaml>=6.0',
            'websocket-client>=1.0.0',
            'requests>=2.25.0',
            'asyncio',
            'dataclasses',
            'typing-extensions',
            'python-dateutil>=2.8.0'
        ]
        
        with open('requirements.txt', 'w') as f:
            f.write('\n'.join(requirements))
        
        logger.info("Created requirements.txt")
        print("To install dependencies, run: pip install -r requirements.txt")


def main():
    """Setup the order flow engine"""
    print("🚀 Order Flow Engine Setup")
    print("="*40)
    
    setup = OrderFlowSetup()
    
    # Create project structure
    setup.create_project_structure()
    
    # Create default configuration
    setup.create_default_config()
    
    # Generate requirements
    setup.install_dependencies()
    
    print("\n✅ Setup completed successfully!")
    print("\nNext steps:")
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. Update config/config.yaml with your Smart API credentials")
    print("3. Run the demo: python order_flow_demo.py")
    print("4. For daily trading, update daily stock selection in config/")
    
    # Load and validate config
    config = setup.load_config()
    if not setup.validate_config(config):
        print("\n⚠️  Please update the configuration file with proper values")
    
    print(f"\nConfiguration files created in: {setup.config_dir}")


if __name__ == "__main__":
    main()
