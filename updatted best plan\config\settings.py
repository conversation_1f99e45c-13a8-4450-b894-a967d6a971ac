"""
System Configuration Settings
"""

# System Configuration
SYSTEM_CONFIG = {
    # Application Settings
    'app_title': 'CGCL - WebSocket Real-time Market Depth & Order Flow',
    'app_geometry': '1400x900',
    'symbol': 'CGCL',
    
    # WebSocket Settings
    'websocket_timeout': 30,
    'reconnect_delay': 1,
    'ping_interval': 30,
    'max_reconnect_attempts': 10,
    
    # Order Book Settings
    'max_order_book_levels': 20,
    'update_interval_ms': 1000,
    'historical_retention_minutes': 10,
    'max_snapshots_per_minute': 60,
    
    # Analytics Settings
    'flow_analysis_window': 300,  # 5 minutes
    'prediction_window': 1800,    # 30 minutes
    'signal_confidence_threshold': 60,
    'imbalance_threshold': 15,
    'momentum_threshold': 2.0,
    
    # GUI Settings
    'gui_update_interval': 100,   # milliseconds
    'performance_update_interval': 1000,
    'clock_update_interval': 1000,
    
    # Performance Settings
    'enable_compression': True,
    'enable_caching': True,
    'max_memory_usage_mb': 512,
    'gc_interval': 300,  # seconds
}

# Color Scheme (Angel One Style)
COLORS = {
    'bg_dark': '#1e1e1e',
    'bg_medium': '#2a2a2a',
    'bg_light': '#333333',
    'text_white': '#ffffff',
    'text_gray': '#b0b0b0',
    'text_light_gray': '#808080',
    'text_green': '#00d084',
    'bid_green': '#00d084',
    'bid_green_bg': '#1a4d3a',
    'ask_red': '#ff4757',
    'ask_red_bg': '#4d1a1a',
    'spread_bg': '#3d3d00',
    'border': '#404040',
    'analysis_bg': '#2d2d2d'
}

# Trading Signal Configuration
SIGNAL_CONFIG = {
    'min_confidence': 60,
    'strong_signal_threshold': 80,
    'imbalance_threshold': 15,
    'momentum_threshold': 2.0,
    'volume_threshold': 1000,
    'spread_threshold': 0.5,
    'level_proximity_threshold': 0.02  # 2%
}

# Model Weights for Ensemble Prediction
MODEL_WEIGHTS = {
    'order_book_imbalance': 0.25,
    'flow_momentum': 0.20,
    'volume_profile': 0.15,
    'spread_dynamics': 0.10,
    'institutional_flow': 0.15,
    'historical_patterns': 0.15
}

# Performance Thresholds
PERFORMANCE_THRESHOLDS = {
    'max_latency_ms': 100,
    'min_update_rate_hz': 1,
    'max_memory_usage_mb': 512,
    'max_cpu_usage_percent': 80,
    'min_data_quality_percent': 60
}
