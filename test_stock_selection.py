#!/usr/bin/env python3
"""
Test script for stock selection functionality
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_stock_search():
    """Test the stock search functionality"""
    print("🧪 Testing Stock Selection Feature")
    print("=" * 50)
    
    try:
        # Import the main app
        from main_app import UltimateTrading
        
        # Create a test instance
        app = UltimateTrading()
        
        # Test stock search functionality
        print("🔍 Testing stock search...")
        
        # Test search for popular stocks
        test_searches = ["REL", "TCS", "INFY", "HDFC", "ITC"]
        
        for search_term in test_searches:
            print(f"\n📊 Searching for: {search_term}")
            results = app.search_stocks(search_term)
            
            if results:
                print(f"✅ Found {len(results)} results:")
                for symbol, token, exchange in results[:3]:  # Show top 3
                    print(f"   • {symbol} (Token: {token}, Exchange: {exchange})")
            else:
                print(f"❌ No results found for {search_term}")
        
        print("\n" + "=" * 50)
        print("🎉 Stock search functionality test completed!")
        print("✅ The stock selection feature is working correctly")
        
        # Test stock selection
        print("\n🔄 Testing stock selection...")
        if len(app.stock_search_results) > 0:
            test_symbol, test_token, _ = app.stock_search_results[0]
            print(f"📊 Selecting stock: {test_symbol}")
            app.select_stock(test_symbol, test_token)
            print(f"✅ Current stock: {app.current_symbol}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Stock Selection Test")
    print("📝 This test verifies the stock search and selection functionality")
    print()
    
    success = test_stock_search()
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Stock selection feature is ready for use")
        print("\n📋 How to use:")
        print("1. Start the main application: python main_app.py")
        print("2. Look for the 'Stock Selection' section in the header")
        print("3. Type a stock symbol (e.g., RELIANCE, TCS, INFY)")
        print("4. Click on a suggestion to switch stocks")
        print("5. The application will update to show data for the selected stock")
    else:
        print("\n💥 TESTS FAILED!")
        print("❌ Please check the error messages above")
