"""
Simple Trading System Template for PAGE GUI Builder
"""

import tkinter as tk
from tkinter import ttk


class SimpleTradingTemplate:
    """Simple template showing trading system layout"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("CGCL Trading System - PAGE Template")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1a1a1a')
        
        # Colors
        self.colors = {
            'bg_dark': '#1a1a1a',
            'bg_medium': '#2d2d2d',
            'text_white': '#ffffff',
            'text_green': '#00ff88',
            'ask_red': '#ff4444'
        }
        
        self.create_ui()
    
    def create_ui(self):
        """Create the UI layout"""
        
        # Main frame
        main_frame = tk.Frame(self.root, bg=self.colors['bg_dark'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Top panel
        top_frame = tk.Frame(main_frame, bg=self.colors['bg_medium'], height=100)
        top_frame.pack(fill=tk.X, pady=(0, 10))
        top_frame.pack_propagate(False)
        
        # Performance section
        perf_label = tk.Label(top_frame, text="📊 Performance: 25ms | 150/s", 
                             bg=self.colors['bg_medium'], fg=self.colors['text_green'])
        perf_label.pack(side=tk.LEFT, padx=10, pady=10)
        
        # Clock section
        clock_label = tk.Label(top_frame, text="🕐 16:31:45 | 25 Jul 2025 | 🔴 Market Closed", 
                              bg=self.colors['bg_medium'], fg=self.colors['text_white'])
        clock_label.pack(side=tk.LEFT, padx=20, pady=10)
        
        # Status section
        status_label = tk.Label(top_frame, text="CGCL: ₹180.75 | Market Closed", 
                               bg=self.colors['bg_medium'], fg=self.colors['text_white'])
        status_label.pack(side=tk.RIGHT, padx=10, pady=10)
        
        # Middle panel
        middle_frame = tk.Frame(main_frame, bg=self.colors['bg_dark'])
        middle_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Order Book
        ob_frame = tk.LabelFrame(middle_frame, text="📊 Order Book", 
                                bg=self.colors['bg_medium'], fg=self.colors['text_white'])
        ob_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Sample order book
        ob_text = tk.Text(ob_frame, bg=self.colors['bg_dark'], fg=self.colors['text_white'], 
                         height=20, font=('Courier', 10))
        ob_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        sample_data = """
Qty    Orders  Price     Price    Orders  Qty
150    2       180.80    180.75   3       200
120    1       180.85    180.70   2       180
200    3       180.90    180.65   1       150
100    1       180.95    180.60   4       250
180    2       181.00    180.55   2       120
        """
        ob_text.insert(tk.END, sample_data)
        ob_text.config(state=tk.DISABLED)
        
        # Analytics panel
        analytics_frame = tk.LabelFrame(middle_frame, text="🔍 Analytics",
                                       bg=self.colors['bg_medium'], fg=self.colors['text_white'],
                                       width=350)
        analytics_frame.pack(side=tk.RIGHT, fill=tk.Y)
        analytics_frame.pack_propagate(False)
        
        # Analytics content
        analytics_text = tk.Text(analytics_frame, bg=self.colors['bg_dark'], 
                                fg=self.colors['text_white'], height=20, font=('Arial', 10))
        analytics_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        analytics_content = """
📈 Order Flow Analysis:
   Imbalance: +15.2%
   Momentum: Bullish
   Institutional: 65%

🎯 30-Min Prediction:
   Target: ₹182.50
   Confidence: 78%
   Direction: UP

⚡ Trading Signals:
   Signal: BUY
   Strength: 85%
   Type: Flow Imbalance
   
🔍 Market Depth:
   Liquidity Score: 82/100
   Spread: 0.05 (0.03%)
   Efficiency: High
        """
        analytics_text.insert(tk.END, analytics_content)
        analytics_text.config(state=tk.DISABLED)
        
        # Bottom panel
        bottom_frame = tk.Frame(main_frame, bg=self.colors['bg_medium'], height=60)
        bottom_frame.pack(fill=tk.X)
        bottom_frame.pack_propagate(False)
        
        # Control buttons
        tk.Button(bottom_frame, text="🔌 Connect", bg=self.colors['text_green'], 
                 fg='black', font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=10, pady=15)
        
        tk.Button(bottom_frame, text="📊 Analytics", bg='#666666', 
                 fg=self.colors['text_white']).pack(side=tk.LEFT, padx=10, pady=15)
        
        tk.Button(bottom_frame, text="⚙️ Settings", bg='#666666', 
                 fg=self.colors['text_white']).pack(side=tk.LEFT, padx=10, pady=15)
        
        tk.Button(bottom_frame, text="❌ Exit", bg=self.colors['ask_red'], 
                 fg='white', font=('Arial', 10, 'bold')).pack(side=tk.RIGHT, padx=10, pady=15)
        
        # Instructions
        self.show_instructions()
    
    def show_instructions(self):
        """Show PAGE instructions"""
        instructions = tk.Toplevel(self.root)
        instructions.title("PAGE GUI Builder - Quick Start")
        instructions.geometry("700x500")
        instructions.configure(bg='white')
        
        text = tk.Text(instructions, wrap=tk.WORD, padx=10, pady=10, font=('Arial', 11))
        text.pack(fill=tk.BOTH, expand=True)
        
        instructions_text = """
🎨 PAGE GUI BUILDER - QUICK START GUIDE

1. DOWNLOAD PAGE:
   • Visit: https://sourceforge.net/projects/page/
   • Download the latest version
   • Extract and run page.exe

2. CREATE YOUR TRADING UI:
   • File → New Project
   • Choose "Tkinter" framework
   • Set window size: 1200x800
   • Set background color: #1a1a1a

3. DRAG & DROP WIDGETS:
   • Frame: For panels and sections
   • Label: For displaying data (prices, time, etc.)
   • Button: For controls (Connect, Exit, etc.)
   • Text: For order book display
   • LabelFrame: For grouped sections

4. LAYOUT YOUR TRADING SYSTEM:
   
   TOP PANEL (Height: 100px):
   ├── Performance Frame (Left)
   ├── Clock Frame (Center)  
   └── Status Frame (Right)
   
   MIDDLE PANEL (Expandable):
   ├── Order Book Frame (Left, expandable)
   └── Analytics Frame (Right, width: 350px)
   
   BOTTOM PANEL (Height: 60px):
   └── Control Buttons (Connect, Analytics, Settings, Exit)

5. GENERATE CODE:
   • File → Generate Python Code
   • Creates: your_ui.py and your_ui_support.py
   • Copy the generated code to your project

6. INTEGRATION STEPS:
   • Replace create_interface() in main_window.py
   • Connect event handlers to your trading logic
   • Update widget references for real-time data

7. RECOMMENDED COLORS:
   • Background: #1a1a1a
   • Panels: #2d2d2d  
   • Text: #ffffff
   • Green (Bids): #00ff88
   • Red (Asks): #ff4444
   • Gray: #cccccc

8. WIDGET NAMING CONVENTION:
   • performance_label
   • clock_label
   • price_label
   • orderbook_text
   • analytics_text
   • connect_button
   • exit_button

9. TIPS FOR TRADING UI:
   • Use monospace fonts for order book (Courier)
   • Make price displays prominent
   • Group related information
   • Use color coding for buy/sell
   • Leave space for real-time updates

10. AFTER GENERATING CODE:
    • Test the UI first
    • Integrate gradually
    • Keep backup of working version
    • Test all event handlers

This template window shows the layout structure.
Use it as reference when designing in PAGE!

Close this window and examine the template layout.
"""
        
        text.insert(tk.END, instructions_text)
        text.config(state=tk.DISABLED)
    
    def run(self):
        """Run the template"""
        self.root.mainloop()


if __name__ == "__main__":
    print("🎨 Creating Simple Trading Template for PAGE...")
    template = SimpleTradingTemplate()
    print("✅ Template created! Use this layout as reference in PAGE.")
    template.run()
