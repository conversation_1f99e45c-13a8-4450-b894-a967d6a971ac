# CGCL Advanced Trading System

Professional-grade order book analysis with real-time WebSocket data from Smart API.

## 🚀 Features

### Core Features
- **Real-time WebSocket Data**: Live CGCL market data from Angel One Smart API
- **20-Level Order Book**: Complete market depth analysis
- **Sub-second Latency**: Optimized for high-frequency trading
- **Professional UI**: Angel One style interface

### Advanced Analytics
- **Order Flow Analysis**: Real-time imbalance detection
- **30-Minute Price Prediction**: ML-based ensemble models
- **Support/Resistance Detection**: Automatic level identification
- **Trading Signal Generation**: Actionable 5-minute timeframe signals

### Performance
- **Historical Data**: 5+ minute retention with compression
- **Event-Driven Architecture**: Real-time notifications
- **Memory Efficient**: Optimized data structures
- **Error Resilient**: Automatic reconnection and failover

## 📁 Project Structure

```
├── main.py                     # Main entry point
├── config/
│   ├── settings.py            # Configuration settings
│   └── credentials.py         # API credentials
├── core/
│   ├── websocket_manager.py   # WebSocket connections
│   ├── order_book.py          # Order book state management
│   └── data_structures.py     # Core data structures
├── analytics/
│   ├── flow_analysis.py       # Order flow imbalance detection
│   ├── price_prediction.py    # 30-minute price prediction
│   ├── support_resistance.py  # Support/resistance detection
│   └── trading_signals.py     # Trading signal generation
├── gui/
│   ├── main_window.py         # Main GUI window
│   ├── order_book_display.py  # Order book visualization
│   └── components.py          # GUI components
├── storage/
│   ├── historical_data.py     # Historical data storage
│   └── reconstruction.py      # Data reconstruction
└── utils/
    ├── performance.py         # Performance monitoring
    └── helpers.py             # Utility functions
```

## 🔧 Installation

1. **Clone or download** the project to your local machine

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure Smart API credentials**:
   
   **Option A: Environment Variables**
   ```bash
   set SMART_API_KEY=your_api_key
   set SMART_CLIENT_CODE=your_client_code
   set SMART_PASSWORD=your_password
   set SMART_TOTP_TOKEN=your_totp_token
   ```
   
   **Option B: Credentials File**
   Create `credentials.txt` in the project root:
   ```
   API_KEY=your_api_key
   CLIENT_CODE=your_client_code
   PASSWORD=your_password
   TOTP_TOKEN=your_totp_token
   ```

## 🚀 Usage

### Basic Usage
```bash
python main.py
```

### Advanced Configuration
Edit `config/settings.py` to customize:
- Update intervals
- Analytics parameters
- GUI settings
- Performance thresholds

## 📊 System Components

### WebSocket Manager
- Real-time Smart API connection
- Automatic reconnection
- Performance monitoring
- Binary data parsing

### Order Book Engine
- 20-level market depth
- Change detection
- Event emission
- Historical retention

### Analytics Engine
- Order flow imbalance detection
- Price prediction models
- Support/resistance levels
- Trading signal generation

### GUI System
- Professional interface
- Real-time updates
- Performance metrics
- Order book visualization

## 🎯 Trading Features

### Order Flow Analysis
- Real-time imbalance detection
- Institutional vs retail flow
- Momentum analysis
- Prediction confidence

### Price Prediction
- 30-minute ensemble models
- Multiple prediction algorithms
- Confidence scoring
- Risk assessment

### Trading Signals
- 5-minute timeframe focus
- Multiple signal types
- Confidence-based filtering
- Risk-reward analysis

## ⚡ Performance

### Optimizations
- Sub-second latency
- Efficient memory usage
- Compressed historical storage
- Event-driven updates

### Monitoring
- Real-time performance metrics
- Connection health tracking
- Data quality scoring
- Error rate monitoring

## 🔒 Security

- Secure credential management
- Environment variable support
- No hardcoded secrets
- Safe error handling

## 🛠️ Development

### Code Structure
- Modular architecture
- Clean separation of concerns
- Type hints throughout
- Comprehensive error handling

### Testing
```bash
# Run tests (when implemented)
python -m pytest tests/
```

### Code Quality
```bash
# Format code (optional)
black .

# Lint code (optional)
flake8 .
```

## 📈 System Requirements

- **Python**: 3.8 or higher
- **Memory**: 512MB RAM minimum
- **Network**: Stable internet connection
- **Display**: 1400x900 minimum resolution

## 🔧 Configuration

### System Settings
- Update intervals: 100ms GUI, 1000ms performance
- Order book levels: 20 bids/asks
- Historical retention: 10 minutes
- Analytics window: 5 minutes

### Performance Thresholds
- Max latency: 100ms
- Min update rate: 1Hz
- Max memory usage: 512MB
- Min data quality: 60%

## 📞 Support

For issues or questions:
1. Check the console output for error messages
2. Verify Smart API credentials
3. Ensure stable internet connection
4. Check system requirements

## ⚠️ Disclaimer

This software is for educational and research purposes. Trading involves risk. Always verify signals and use proper risk management.

## 📄 License

This project is provided as-is for educational purposes.
