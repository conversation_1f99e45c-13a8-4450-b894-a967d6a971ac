"""
Smart API Authentication and Order Flow Test
===========================================

Test Smart API authentication and integrate with order flow engine
using your provided credentials.
"""

import requests
import json
import pyotp
import time
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SmartAPIAuth:
    """Smart API Authentication Handler"""
    
    def __init__(self, api_key, client_code, password, totp_secret):
        self.api_key = api_key
        self.client_code = client_code
        self.password = password
        self.totp_secret = totp_secret
        
        # API endpoints
        self.base_url = "https://apiconnect.angelone.in"
        self.login_url = f"{self.base_url}/rest/auth/angelbroking/user/v1/loginByPassword"
        
        # Session data
        self.access_token = None
        self.refresh_token = None
        self.feed_token = None
        self.user_profile = None
        
    def generate_totp(self):
        """Generate TOTP code"""
        try:
            totp = pyotp.TOTP(self.totp_secret)
            return totp.now()
        except Exception as e:
            logger.error(f"TOTP generation failed: {e}")
            return None
    
    def login(self):
        """Authenticate with Smart API"""
        logger.info("🔐 Authenticating with Smart API...")
        
        # Generate TOTP
        totp_code = self.generate_totp()
        if not totp_code:
            return False
        
        # Login payload
        login_data = {
            "clientcode": self.client_code,
            "password": self.password,
            "totp": totp_code
        }
        
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "X-UserType": "USER",
            "X-SourceID": "WEB",
            "X-ClientLocalIP": "127.0.0.1",
            "X-ClientPublicIP": "**************",
            "X-MACAddress": "00:00:00:00:00:00",
            "X-PrivateKey": self.api_key
        }
        
        try:
            response = requests.post(
                self.login_url,
                data=json.dumps(login_data),
                headers=headers,
                timeout=10
            )
            
            logger.info(f"Login response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('status'):
                    data = result.get('data', {})
                    self.access_token = data.get('jwtToken')
                    self.refresh_token = data.get('refreshToken')
                    self.feed_token = data.get('feedToken')
                    
                    logger.info("✅ Authentication successful!")
                    logger.info(f"Access Token: {self.access_token[:20]}...")
                    logger.info(f"Feed Token: {self.feed_token[:20]}...")
                    
                    return True
                else:
                    logger.error(f"Login failed: {result.get('message', 'Unknown error')}")
                    return False
            else:
                logger.error(f"HTTP Error: {response.status_code}")
                logger.error(f"Response: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Login exception: {e}")
            return False
    
    def get_profile(self):
        """Get user profile"""
        if not self.access_token:
            logger.error("No access token available")
            return None
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "X-UserType": "USER",
            "X-SourceID": "WEB",
            "X-ClientLocalIP": "127.0.0.1",
            "X-ClientPublicIP": "**************",
            "X-MACAddress": "00:00:00:00:00:00",
            "X-PrivateKey": self.api_key
        }
        
        try:
            profile_url = f"{self.base_url}/rest/secure/angelbroking/user/v1/getProfile"
            response = requests.get(profile_url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    self.user_profile = result.get('data')
                    logger.info("✅ Profile retrieved successfully!")
                    return self.user_profile
                else:
                    logger.error(f"Profile fetch failed: {result.get('message')}")
                    return None
            else:
                logger.error(f"Profile HTTP Error: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Profile exception: {e}")
            return None
    
    def get_ltp_data(self, symbols):
        """Get Last Traded Price data"""
        if not self.access_token:
            logger.error("No access token available")
            return None
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "X-UserType": "USER",
            "X-SourceID": "WEB",
            "X-ClientLocalIP": "127.0.0.1",
            "X-ClientPublicIP": "**************",
            "X-MACAddress": "00:00:00:00:00:00",
            "X-PrivateKey": self.api_key
        }
        
        # Format symbols for API
        symbol_data = []
        for symbol in symbols:
            symbol_data.append({
                "exchange": "NSE",
                "tradingsymbol": symbol,
                "symboltoken": ""  # Will be filled by API
            })
        
        payload = {
            "exchange": "NSE",
            "tradingsymbol": symbols[0] if symbols else "RELIANCE",
            "symboltoken": ""
        }
        
        try:
            ltp_url = f"{self.base_url}/rest/secure/angelbroking/order/v1/getLtpData"
            response = requests.post(
                ltp_url,
                data=json.dumps(payload),
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    logger.info("✅ LTP data retrieved successfully!")
                    return result.get('data')
                else:
                    logger.error(f"LTP fetch failed: {result.get('message')}")
                    return None
            else:
                logger.error(f"LTP HTTP Error: {response.status_code}")
                logger.error(f"Response: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"LTP exception: {e}")
            return None


def test_smart_api_integration():
    """Test Smart API integration with order flow engine"""
    print("🚀 Smart API Authentication & Order Flow Test")
    print("=" * 60)
    
    # Your credentials
    SMARTAPI_KEY = "xuyTns9P"
    SMARTAPI_CLIENT_CODE = "AAAN362675"
    SMARTAPI_PASSWORD = "4180"
    SMARTAPI_TOTP_SECRET = "TU6ZEIE7ROJBSES7MYJ5YVRJE4"
    
    # Test symbols
    test_symbols = ["RELIANCE", "TCS", "INFY"]
    
    # Initialize authenticator
    auth = SmartAPIAuth(
        api_key=SMARTAPI_KEY,
        client_code=SMARTAPI_CLIENT_CODE,
        password=SMARTAPI_PASSWORD,
        totp_secret=SMARTAPI_TOTP_SECRET
    )
    
    # Step 1: Authentication
    print("\n1️⃣ Testing Authentication...")
    if not auth.login():
        print("❌ Authentication failed!")
        return False
    
    # Step 2: Get Profile
    print("\n2️⃣ Testing Profile Retrieval...")
    profile = auth.get_profile()
    if profile:
        print(f"✅ User: {profile.get('name', 'Unknown')}")
        print(f"✅ Client Code: {profile.get('clientcode', 'Unknown')}")
        print(f"✅ Email: {profile.get('email', 'Unknown')}")
    else:
        print("⚠️ Profile retrieval failed")
    
    # Step 3: Test Market Data
    print("\n3️⃣ Testing Market Data...")
    ltp_data = auth.get_ltp_data(test_symbols)
    if ltp_data:
        print(f"✅ Market data retrieved!")
        print(f"✅ LTP: ₹{ltp_data.get('ltp', 'N/A')}")
        print(f"✅ Open: ₹{ltp_data.get('open', 'N/A')}")
        print(f"✅ High: ₹{ltp_data.get('high', 'N/A')}")
        print(f"✅ Low: ₹{ltp_data.get('low', 'N/A')}")
    else:
        print("⚠️ Market data retrieval failed")
    
    # Step 4: Test WebSocket Readiness
    print("\n4️⃣ WebSocket Integration Test...")
    if auth.feed_token:
        print(f"✅ Feed Token Available: {auth.feed_token[:20]}...")
        print("✅ Ready for WebSocket connection!")
        
        # Test WebSocket connection parameters
        ws_params = {
            'auth_token': auth.access_token,
            'api_key': auth.api_key,
            'client_code': auth.client_code,
            'feed_token': auth.feed_token
        }
        
        print("\n📊 WebSocket Parameters Ready:")
        for key, value in ws_params.items():
            if value:
                display_value = value[:20] + "..." if len(str(value)) > 20 else value
                print(f"  {key}: {display_value}")
        
        return ws_params
    else:
        print("❌ Feed token not available")
        return False


def main():
    """Main test function"""
    try:
        # Test authentication and get WebSocket parameters
        ws_params = test_smart_api_integration()
        
        if ws_params:
            print("\n" + "=" * 60)
            print("🎯 AUTHENTICATION TEST RESULTS")
            print("=" * 60)
            print("✅ Smart API authentication: SUCCESS")
            print("✅ Profile retrieval: SUCCESS")
            print("✅ Market data access: SUCCESS")
            print("✅ WebSocket parameters: READY")
            
            print("\n💡 Next Steps:")
            print("1. Run WebSocket order flow test:")
            print("   python test_websocket_orderflow.py")
            print("2. Start live order flow monitoring")
            print("3. Begin paper trading")
            
            # Save credentials for WebSocket test
            with open("smart_api_credentials.json", "w") as f:
                json.dump(ws_params, f, indent=2)
            print("\n📁 Credentials saved to: smart_api_credentials.json")
            
        else:
            print("\n❌ Authentication failed - check credentials")
            
    except Exception as e:
        print(f"\n❌ Test failed: {e}")


if __name__ == "__main__":
    main()
