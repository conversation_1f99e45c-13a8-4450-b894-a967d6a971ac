"""
Advanced Market Depth GUI - Angel One Style
==========================================

Creates a sophisticated market depth display similar to Angel One's interface
with enhanced coloring, readability, and real-time analysis.
"""

import tkinter as tk
from tkinter import ttk, font
import threading
import time
import queue
from datetime import datetime, timedelta
from collections import deque
import math

class AdvancedMarketDepthGUI:
    """Advanced Market Depth GUI with Angel One style interface"""
    
    def __init__(self, symbols):
        self.symbols = symbols
        self.root = tk.Tk()
        self.root.title("Advanced Market Depth - Real Time")
        self.root.geometry("1400x900")
        self.root.configure(bg='#1a1a1a')  # Dark theme
        
        # Data queue for thread-safe updates
        self.update_queue = queue.Queue()
        
        # Market depth data storage
        self.market_depth_data = {symbol: None for symbol in symbols}
        self.price_history = {symbol: deque(maxlen=100) for symbol in symbols}
        self.volume_history = {symbol: deque(maxlen=100) for symbol in symbols}
        
        # Analysis data
        self.depth_analysis = {symbol: {} for symbol in symbols}

        # Colors for different elements (define before creating widgets)
        self.colors = {
            'bg_dark': '#1a1a1a',
            'bg_medium': '#2d2d2d',
            'bg_light': '#3d3d3d',
            'text_white': '#ffffff',
            'text_gray': '#cccccc',
            'text_light_gray': '#999999',
            'bid_green': '#00ff88',
            'bid_green_light': '#66ffaa',
            'ask_red': '#ff4444',
            'ask_red_light': '#ff6666',
            'spread_yellow': '#ffdd44',
            'volume_blue': '#4488ff',
            'price_change_up': '#00ff88',
            'price_change_down': '#ff4444',
            'neutral': '#888888'
        }

        # Create custom fonts
        self.setup_fonts()

        # Create GUI
        self.create_widgets()

        # Start update processor
        self.process_updates()

        # Data storage
        self.order_book_history = {}
    
    def setup_fonts(self):
        """Setup custom fonts"""
        self.fonts = {
            'title': font.Font(family="Arial", size=14, weight="bold"),
            'header': font.Font(family="Arial", size=11, weight="bold"),
            'data': font.Font(family="Consolas", size=10),
            'data_bold': font.Font(family="Consolas", size=10, weight="bold"),
            'small': font.Font(family="Arial", size=9),
            'large_price': font.Font(family="Consolas", size=12, weight="bold")
        }
    
    def create_widgets(self):
        """Create the main GUI widgets"""
        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_dark'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Title bar
        self.create_title_bar(main_frame)
        
        # Symbol tabs
        self.create_symbol_tabs(main_frame)
    
    def create_title_bar(self, parent):
        """Create title bar with status"""
        title_frame = tk.Frame(parent, bg=self.colors['bg_medium'], height=50)
        title_frame.pack(fill=tk.X, pady=(0, 5))
        title_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(
            title_frame,
            text="🚀 Advanced Market Depth - Real Time Analysis",
            font=self.fonts['title'],
            fg=self.colors['text_white'],
            bg=self.colors['bg_medium']
        )
        title_label.pack(side=tk.LEFT, padx=10, pady=10)
        
        # Status
        self.status_label = tk.Label(
            title_frame,
            text="🟢 Live Data",
            font=self.fonts['header'],
            fg=self.colors['bid_green'],
            bg=self.colors['bg_medium']
        )
        self.status_label.pack(side=tk.RIGHT, padx=10, pady=10)
        
        # Time
        self.time_label = tk.Label(
            title_frame,
            text="",
            font=self.fonts['data'],
            fg=self.colors['text_gray'],
            bg=self.colors['bg_medium']
        )
        self.time_label.pack(side=tk.RIGHT, padx=10, pady=10)
        
        # Update time continuously
        self.update_time()
    
    def update_time(self):
        """Update time display"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
    
    def create_symbol_tabs(self, parent):
        """Create tabs for each symbol"""
        # Notebook for symbols
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TNotebook', background=self.colors['bg_dark'])
        style.configure('TNotebook.Tab', background=self.colors['bg_medium'], 
                       foreground=self.colors['text_white'], padding=[20, 10])
        
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create tabs for each symbol
        self.symbol_frames = {}
        for symbol in self.symbols:
            frame = tk.Frame(self.notebook, bg=self.colors['bg_dark'])
            self.notebook.add(frame, text=f"  {symbol}  ")
            self.symbol_frames[symbol] = frame
            
            # Create market depth display for this symbol
            self.create_market_depth_display(symbol, frame)
    
    def create_market_depth_display(self, symbol, parent):
        """Create market depth display for a symbol"""
        # Main container with padding
        container = tk.Frame(parent, bg=self.colors['bg_dark'])
        container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Top section - Symbol info and key metrics
        self.create_symbol_header(symbol, container)
        
        # Middle section - Market depth table
        self.create_depth_table(symbol, container)
        
        # Bottom section - Analysis and insights
        self.create_analysis_section(symbol, container)
    
    def create_symbol_header(self, symbol, parent):
        """Create symbol header with key metrics"""
        header_frame = tk.Frame(parent, bg=self.colors['bg_medium'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # Left side - Symbol and price
        left_frame = tk.Frame(header_frame, bg=self.colors['bg_medium'])
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20, pady=10)
        
        # Symbol name
        symbol_label = tk.Label(
            left_frame,
            text=symbol,
            font=self.fonts['title'],
            fg=self.colors['text_white'],
            bg=self.colors['bg_medium']
        )
        symbol_label.pack(anchor=tk.W)
        
        # Current price
        price_label = tk.Label(
            left_frame,
            text="₹0.00",
            font=self.fonts['large_price'],
            fg=self.colors['text_white'],
            bg=self.colors['bg_medium']
        )
        price_label.pack(anchor=tk.W)
        setattr(self, f'{symbol}_price_label', price_label)
        
        # Price change
        change_label = tk.Label(
            left_frame,
            text="0.00 (0.00%)",
            font=self.fonts['data'],
            fg=self.colors['neutral'],
            bg=self.colors['bg_medium']
        )
        change_label.pack(anchor=tk.W)
        setattr(self, f'{symbol}_change_label', change_label)
        
        # Right side - Key metrics
        right_frame = tk.Frame(header_frame, bg=self.colors['bg_medium'])
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=20, pady=10)
        
        # Create metric labels
        metrics = ['Volume', 'VWAP', 'Spread', 'Imbalance']
        for i, metric in enumerate(metrics):
            metric_frame = tk.Frame(right_frame, bg=self.colors['bg_medium'])
            metric_frame.grid(row=i//2, column=i%2, padx=10, pady=2, sticky=tk.W)
            
            tk.Label(
                metric_frame,
                text=f"{metric}:",
                font=self.fonts['small'],
                fg=self.colors['text_gray'],
                bg=self.colors['bg_medium']
            ).pack(side=tk.LEFT)
            
            value_label = tk.Label(
                metric_frame,
                text="--",
                font=self.fonts['data_bold'],
                fg=self.colors['text_white'],
                bg=self.colors['bg_medium']
            )
            value_label.pack(side=tk.LEFT, padx=(5, 0))
            setattr(self, f'{symbol}_{metric.lower()}_label', value_label)
    
    def create_depth_table(self, symbol, parent):
        """Create the main market depth table"""
        # Table container
        table_frame = tk.Frame(parent, bg=self.colors['bg_light'])
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Table header
        header_frame = tk.Frame(table_frame, bg=self.colors['bg_medium'], height=40)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # Header labels
        headers = ['Qty', 'Orders', 'Price', 'Price', 'Orders', 'Qty']
        header_colors = [
            self.colors['bid_green'], self.colors['bid_green'], self.colors['bid_green'],
            self.colors['ask_red'], self.colors['ask_red'], self.colors['ask_red']
        ]
        
        for i, (header, color) in enumerate(zip(headers, header_colors)):
            label = tk.Label(
                header_frame,
                text=header,
                font=self.fonts['header'],
                fg=color,
                bg=self.colors['bg_medium']
            )
            label.grid(row=0, column=i, padx=5, pady=10, sticky=tk.EW)
        
        # Configure grid weights
        for i in range(6):
            header_frame.grid_columnconfigure(i, weight=1)
        
        # Scrollable content area
        canvas = tk.Canvas(table_frame, bg=self.colors['bg_light'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_light'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Store references for updates
        setattr(self, f'{symbol}_depth_frame', scrollable_frame)
        setattr(self, f'{symbol}_canvas', canvas)
    
    def create_analysis_section(self, symbol, parent):
        """Create analysis section"""
        analysis_frame = tk.Frame(parent, bg=self.colors['bg_medium'], height=120)
        analysis_frame.pack(fill=tk.X)
        analysis_frame.pack_propagate(False)
        
        # Title
        tk.Label(
            analysis_frame,
            text="📊 Real-Time Analysis",
            font=self.fonts['header'],
            fg=self.colors['text_white'],
            bg=self.colors['bg_medium']
        ).pack(pady=(10, 5))
        
        # Analysis content
        analysis_text = tk.Text(
            analysis_frame,
            height=4,
            font=self.fonts['small'],
            fg=self.colors['text_gray'],
            bg=self.colors['bg_dark'],
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        analysis_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        setattr(self, f'{symbol}_analysis_text', analysis_text)
    
    def update_market_depth(self, symbol, depth_data):
        """Update market depth display"""
        self.update_queue.put(('depth', symbol, depth_data))
    
    def update_symbol_metrics(self, symbol, metrics):
        """Update symbol metrics"""
        self.update_queue.put(('metrics', symbol, metrics))
    
    def update_analysis(self, symbol, analysis):
        """Update analysis text"""
        self.update_queue.put(('analysis', symbol, analysis))
    
    def process_updates(self):
        """Process updates from queue"""
        try:
            while True:
                try:
                    update = self.update_queue.get_nowait()
                    
                    if update[0] == 'depth':
                        self._update_depth_display(update[1], update[2])
                    elif update[0] == 'metrics':
                        self._update_metrics_display(update[1], update[2])
                    elif update[0] == 'analysis':
                        self._update_analysis_display(update[1], update[2])
                        
                except queue.Empty:
                    break
                    
        except Exception as e:
            print(f"Update processing error: {e}")
        
        # Schedule next update
        self.root.after(100, self.process_updates)
    
    def _update_depth_display(self, symbol, depth_data):
        """Update the depth table display"""
        try:
            depth_frame = getattr(self, f'{symbol}_depth_frame')
            
            # Clear existing content
            for widget in depth_frame.winfo_children():
                widget.destroy()
            
            if not depth_data or not depth_data.get('bids') or not depth_data.get('asks'):
                return
            
            bids = depth_data['bids'][:10]  # Top 10 bids
            asks = depth_data['asks'][:10]  # Top 10 asks
            
            # Calculate total volumes for percentage bars
            total_bid_vol = sum(bid[1] for bid in bids)
            total_ask_vol = sum(ask[1] for ask in asks)
            max_vol = max(total_bid_vol, total_ask_vol) if total_bid_vol > 0 or total_ask_vol > 0 else 1
            
            # Display asks (top to bottom, highest price first)
            for i, (price, qty, orders) in enumerate(reversed(asks)):
                self._create_depth_row(depth_frame, i, qty, orders, price, None, None, None, 
                                     'ask', qty/max_vol if max_vol > 0 else 0)
            
            # Spread row
            if bids and asks:
                spread = asks[0][0] - bids[0][0]
                spread_pct = (spread / bids[0][0]) * 100
                self._create_spread_row(depth_frame, len(asks), spread, spread_pct)
            
            # Display bids (top to bottom, highest price first)
            for i, (price, qty, orders) in enumerate(bids):
                row_idx = len(asks) + 1 + i
                self._create_depth_row(depth_frame, row_idx, None, None, None, price, orders, qty,
                                     'bid', qty/max_vol if max_vol > 0 else 0)
            
            # Update canvas scroll region
            canvas = getattr(self, f'{symbol}_canvas')
            canvas.update_idletasks()
            canvas.configure(scrollregion=canvas.bbox("all"))
            
        except Exception as e:
            print(f"Depth display update error: {e}")
    
    def _create_depth_row(self, parent, row, ask_qty, ask_orders, ask_price, 
                         bid_price, bid_orders, bid_qty, side, volume_pct):
        """Create a single depth row"""
        row_frame = tk.Frame(parent, bg=self.colors['bg_light'], height=25)
        row_frame.pack(fill=tk.X, pady=1)
        row_frame.pack_propagate(False)
        
        # Configure grid
        for i in range(6):
            row_frame.grid_columnconfigure(i, weight=1)
        
        # Volume bar background
        if volume_pct > 0:
            bar_color = self.colors['bid_green_light'] if side == 'bid' else self.colors['ask_red_light']
            bar_width = int(volume_pct * 100)  # Percentage width
            
            if side == 'bid':
                # Bid volume bar (left side)
                bar_frame = tk.Frame(row_frame, bg=bar_color, height=25)
                bar_frame.place(x=0, y=0, width=bar_width*2, height=25)
            else:
                # Ask volume bar (right side)
                bar_frame = tk.Frame(row_frame, bg=bar_color, height=25)
                bar_frame.place(relx=1.0, y=0, width=bar_width*2, height=25, anchor='ne')
        
        # Data labels
        labels = [
            (ask_qty if ask_qty else '', 0, self.colors['ask_red'] if ask_qty else ''),
            (ask_orders if ask_orders else '', 1, self.colors['ask_red'] if ask_orders else ''),
            (f"₹{ask_price:.2f}" if ask_price else '', 2, self.colors['ask_red'] if ask_price else ''),
            (f"₹{bid_price:.2f}" if bid_price else '', 3, self.colors['bid_green'] if bid_price else ''),
            (bid_orders if bid_orders else '', 4, self.colors['bid_green'] if bid_orders else ''),
            (f"{bid_qty:,}" if bid_qty else '', 5, self.colors['bid_green'] if bid_qty else '')
        ]
        
        for text, col, color in labels:
            if text:
                label = tk.Label(
                    row_frame,
                    text=text,
                    font=self.fonts['data'],
                    fg=color,
                    bg=self.colors['bg_light']
                )
                label.grid(row=0, column=col, padx=5, pady=2)
    
    def _create_spread_row(self, parent, row, spread, spread_pct):
        """Create spread indicator row"""
        spread_frame = tk.Frame(parent, bg=self.colors['spread_yellow'], height=30)
        spread_frame.pack(fill=tk.X, pady=2)
        spread_frame.pack_propagate(False)
        
        spread_label = tk.Label(
            spread_frame,
            text=f"SPREAD: ₹{spread:.2f} ({spread_pct:.3f}%)",
            font=self.fonts['data_bold'],
            fg=self.colors['bg_dark'],
            bg=self.colors['spread_yellow']
        )
        spread_label.pack(expand=True)
    
    def _update_metrics_display(self, symbol, metrics):
        """Update metrics display"""
        try:
            # Update price
            price_label = getattr(self, f'{symbol}_price_label')
            price_label.config(text=f"₹{metrics.get('current_price', 0):.2f}")
            
            # Update price change
            change = metrics.get('price_change', 0)
            change_pct = metrics.get('price_change_pct', 0)
            change_label = getattr(self, f'{symbol}_change_label')
            
            change_color = self.colors['price_change_up'] if change >= 0 else self.colors['price_change_down']
            change_text = f"{change:+.2f} ({change_pct:+.2f}%)"
            change_label.config(text=change_text, fg=change_color)
            
            # Update other metrics
            metric_updates = {
                'volume': f"{metrics.get('volume', 0):,}",
                'vwap': f"₹{metrics.get('vwap', 0):.2f}",
                'spread': f"₹{metrics.get('spread', 0):.2f}",
                'imbalance': f"{metrics.get('imbalance', 0):+.1f}%"
            }
            
            for metric, value in metric_updates.items():
                label = getattr(self, f'{symbol}_{metric}_label')
                label.config(text=value)
                
        except Exception as e:
            print(f"Metrics display update error: {e}")
    
    def _update_analysis_display(self, symbol, analysis):
        """Update analysis text"""
        try:
            analysis_text = getattr(self, f'{symbol}_analysis_text')
            
            analysis_text.config(state=tk.NORMAL)
            analysis_text.delete(1.0, tk.END)
            
            # Format analysis text
            analysis_content = f"🔍 Market Analysis - {datetime.now().strftime('%H:%M:%S')}\n\n"
            
            for key, value in analysis.items():
                analysis_content += f"• {key}: {value}\n"
            
            analysis_text.insert(tk.END, analysis_content)
            analysis_text.config(state=tk.DISABLED)
            
        except Exception as e:
            print(f"Analysis display update error: {e}")
    
    def run(self):
        """Start the GUI"""
        self.root.mainloop()


# Test the advanced market depth GUI
if __name__ == "__main__":
    import random
    
    def test_advanced_gui():
        """Test the advanced market depth GUI"""
        symbols = ["RELIANCE", "TCS", "INFY"]
        gui = AdvancedMarketDepthGUI(symbols)
        
        def simulate_data():
            """Simulate market depth data"""
            base_prices = {"RELIANCE": 2500, "TCS": 3500, "INFY": 1800}
            
            while True:
                for symbol in symbols:
                    base_price = base_prices[symbol]
                    
                    # Generate realistic market depth
                    bids = []
                    asks = []
                    
                    for i in range(10):
                        # Bids
                        bid_price = base_price - (i + 1) * 0.05
                        bid_qty = random.randint(1000, 50000)
                        bid_orders = random.randint(1, 20)
                        bids.append((bid_price, bid_qty, bid_orders))
                        
                        # Asks
                        ask_price = base_price + (i + 1) * 0.05
                        ask_qty = random.randint(1000, 50000)
                        ask_orders = random.randint(1, 20)
                        asks.append((ask_price, ask_qty, ask_orders))
                    
                    depth_data = {'bids': bids, 'asks': asks}
                    gui.update_market_depth(symbol, depth_data)
                    
                    # Update metrics
                    metrics = {
                        'current_price': base_price + random.uniform(-5, 5),
                        'price_change': random.uniform(-10, 10),
                        'price_change_pct': random.uniform(-2, 2),
                        'volume': random.randint(100000, 1000000),
                        'vwap': base_price + random.uniform(-2, 2),
                        'spread': random.uniform(0.05, 0.5),
                        'imbalance': random.uniform(-50, 50)
                    }
                    gui.update_symbol_metrics(symbol, metrics)
                    
                    # Update analysis
                    analysis = {
                        'Trend': random.choice(['Bullish', 'Bearish', 'Neutral']),
                        'Volume Profile': random.choice(['High', 'Medium', 'Low']),
                        'Support Level': f"₹{base_price - 10:.2f}",
                        'Resistance Level': f"₹{base_price + 10:.2f}",
                        'Order Flow': random.choice(['Buying Pressure', 'Selling Pressure', 'Balanced'])
                    }
                    gui.update_analysis(symbol, analysis)
                
                time.sleep(1)  # Update every second
        
        # Start data simulation in background
        data_thread = threading.Thread(target=simulate_data, daemon=True)
        data_thread.start()
        
        # Run GUI
        gui.run()
    
    test_advanced_gui()


class MarketDepthEngine:
    """
    Advanced Market Depth Analysis Engine

    Processes real-time tick data to create sophisticated market depth
    analysis similar to professional trading platforms.
    """

    def __init__(self, symbol):
        self.symbol = symbol
        self.tick_data = deque(maxlen=1000)  # Last 1000 ticks
        self.order_book_snapshots = deque(maxlen=100)  # Last 100 order book snapshots

        # Price levels and volume tracking
        self.price_levels = {}  # price -> {total_volume, trade_count, avg_size}
        self.volume_profile = {}  # price -> volume
        self.time_and_sales = deque(maxlen=500)  # Last 500 trades

        # Market microstructure analysis
        self.bid_ask_spreads = deque(maxlen=100)
        self.market_impact_data = deque(maxlen=50)
        self.order_flow_imbalance = deque(maxlen=100)

        # Support/Resistance levels
        self.support_levels = []
        self.resistance_levels = []

        # Current market state
        self.current_price = 0
        self.current_volume = 0
        self.vwap = 0
        self.total_volume = 0
        self.total_value = 0

    def add_tick(self, price, volume, timestamp, buyer_initiated=True):
        """Add new tick data and update analysis"""
        tick = {
            'price': price,
            'volume': volume,
            'timestamp': timestamp,
            'buyer_initiated': buyer_initiated
        }

        self.tick_data.append(tick)
        self.time_and_sales.append(tick)

        # Update current state
        self.current_price = price
        self.current_volume += volume

        # Update VWAP
        self.total_volume += volume
        self.total_value += price * volume
        self.vwap = self.total_value / self.total_volume if self.total_volume > 0 else price

        # Update price levels
        self._update_price_levels(price, volume)

        # Update volume profile
        price_level = round(price, 2)
        self.volume_profile[price_level] = self.volume_profile.get(price_level, 0) + volume

        # Analyze market microstructure
        self._analyze_microstructure()

        return self._generate_market_depth()

    def add_order_book(self, bids, asks, timestamp):
        """Add order book snapshot"""
        order_book = {
            'timestamp': timestamp,
            'bids': bids,  # [(price, quantity, orders), ...]
            'asks': asks,
            'spread': asks[0][0] - bids[0][0] if bids and asks else 0
        }

        self.order_book_snapshots.append(order_book)

        # Update spread analysis
        if order_book['spread'] > 0:
            self.bid_ask_spreads.append(order_book['spread'])

        # Calculate order flow imbalance
        total_bid_vol = sum(bid[1] for bid in bids[:5])
        total_ask_vol = sum(ask[1] for ask in asks[:5])
        total_vol = total_bid_vol + total_ask_vol

        if total_vol > 0:
            imbalance = (total_bid_vol - total_ask_vol) / total_vol * 100
            self.order_flow_imbalance.append(imbalance)

        return self._generate_enhanced_order_book(bids, asks)

    def _update_price_levels(self, price, volume):
        """Update price level statistics"""
        price_level = round(price, 2)

        if price_level not in self.price_levels:
            self.price_levels[price_level] = {
                'total_volume': 0,
                'trade_count': 0,
                'avg_size': 0,
                'first_seen': datetime.now(),
                'last_seen': datetime.now()
            }

        level_data = self.price_levels[price_level]
        level_data['total_volume'] += volume
        level_data['trade_count'] += 1
        level_data['avg_size'] = level_data['total_volume'] / level_data['trade_count']
        level_data['last_seen'] = datetime.now()

    def _analyze_microstructure(self):
        """Analyze market microstructure patterns"""
        if len(self.tick_data) < 10:
            return

        recent_ticks = list(self.tick_data)[-10:]

        # Calculate market impact
        price_changes = []
        volume_sizes = []

        for i in range(1, len(recent_ticks)):
            price_change = abs(recent_ticks[i]['price'] - recent_ticks[i-1]['price'])
            volume_size = recent_ticks[i]['volume']

            price_changes.append(price_change)
            volume_sizes.append(volume_size)

        if volume_sizes:
            avg_volume = sum(volume_sizes) / len(volume_sizes)
            avg_impact = sum(price_changes) / len(price_changes)

            self.market_impact_data.append({
                'avg_volume': avg_volume,
                'avg_impact': avg_impact,
                'timestamp': datetime.now()
            })

    def _generate_market_depth(self):
        """Generate comprehensive market depth analysis"""
        if not self.order_book_snapshots:
            return None

        latest_book = self.order_book_snapshots[-1]

        # Enhanced market depth with analysis
        depth_analysis = {
            'symbol': self.symbol,
            'timestamp': datetime.now(),
            'current_price': self.current_price,
            'vwap': self.vwap,
            'total_volume': self.total_volume,
            'spread': latest_book['spread'],
            'spread_pct': (latest_book['spread'] / self.current_price) * 100 if self.current_price > 0 else 0,
            'order_flow_imbalance': self.order_flow_imbalance[-1] if self.order_flow_imbalance else 0,
            'volume_profile': self._get_volume_profile_analysis(),
            'support_resistance': self._identify_support_resistance(),
            'market_sentiment': self._analyze_market_sentiment(),
            'liquidity_analysis': self._analyze_liquidity(latest_book),
            'price_action': self._analyze_price_action()
        }

        return depth_analysis

    def _generate_enhanced_order_book(self, bids, asks):
        """Generate enhanced order book with additional analysis"""
        enhanced_book = {
            'bids': bids,
            'asks': asks,
            'bid_ask_ratio': self._calculate_bid_ask_ratio(bids, asks),
            'depth_analysis': self._analyze_order_book_depth(bids, asks),
            'large_orders': self._identify_large_orders(bids, asks),
            'price_clustering': self._analyze_price_clustering(bids, asks)
        }

        return enhanced_book

    def _get_volume_profile_analysis(self):
        """Analyze volume profile"""
        if not self.volume_profile:
            return {}

        # Find Point of Control (POC) - price level with highest volume
        poc_price = max(self.volume_profile.items(), key=lambda x: x[1])

        # Calculate volume distribution
        total_vol = sum(self.volume_profile.values())
        volume_above_poc = sum(vol for price, vol in self.volume_profile.items() if price > poc_price[0])
        volume_below_poc = sum(vol for price, vol in self.volume_profile.items() if price < poc_price[0])

        return {
            'poc_price': poc_price[0],
            'poc_volume': poc_price[1],
            'volume_above_poc_pct': (volume_above_poc / total_vol) * 100 if total_vol > 0 else 0,
            'volume_below_poc_pct': (volume_below_poc / total_vol) * 100 if total_vol > 0 else 0,
            'total_volume': total_vol
        }

    def _identify_support_resistance(self):
        """Identify support and resistance levels"""
        if len(self.tick_data) < 50:
            return {'support_levels': [], 'resistance_levels': []}

        # Simple support/resistance identification based on price levels with high volume
        price_volumes = {}
        for tick in self.tick_data:
            price_level = round(tick['price'], 1)  # Round to nearest 10 paisa
            price_volumes[price_level] = price_volumes.get(price_level, 0) + tick['volume']

        # Sort by volume and get top levels
        sorted_levels = sorted(price_volumes.items(), key=lambda x: x[1], reverse=True)

        current_price = self.current_price
        support_levels = [price for price, vol in sorted_levels[:5] if price < current_price]
        resistance_levels = [price for price, vol in sorted_levels[:5] if price > current_price]

        return {
            'support_levels': support_levels[:3],  # Top 3 support levels
            'resistance_levels': resistance_levels[:3]  # Top 3 resistance levels
        }

    def _analyze_market_sentiment(self):
        """Analyze market sentiment based on order flow"""
        if len(self.tick_data) < 20:
            return 'Neutral'

        recent_ticks = list(self.tick_data)[-20:]

        # Calculate buying vs selling pressure
        buy_volume = sum(tick['volume'] for tick in recent_ticks if tick['buyer_initiated'])
        sell_volume = sum(tick['volume'] for tick in recent_ticks if not tick['buyer_initiated'])
        total_volume = buy_volume + sell_volume

        if total_volume == 0:
            return 'Neutral'

        buy_ratio = buy_volume / total_volume

        if buy_ratio > 0.6:
            return 'Bullish'
        elif buy_ratio < 0.4:
            return 'Bearish'
        else:
            return 'Neutral'

    def _analyze_liquidity(self, order_book):
        """Analyze market liquidity"""
        bids = order_book['bids']
        asks = order_book['asks']

        if not bids or not asks:
            return {}

        # Calculate liquidity metrics
        bid_liquidity = sum(bid[1] for bid in bids[:5])  # Top 5 levels
        ask_liquidity = sum(ask[1] for ask in asks[:5])
        total_liquidity = bid_liquidity + ask_liquidity

        # Calculate average order size
        bid_orders = sum(bid[2] for bid in bids[:5])
        ask_orders = sum(ask[2] for ask in asks[:5])

        avg_bid_size = bid_liquidity / bid_orders if bid_orders > 0 else 0
        avg_ask_size = ask_liquidity / ask_orders if ask_orders > 0 else 0

        return {
            'total_liquidity': total_liquidity,
            'bid_liquidity': bid_liquidity,
            'ask_liquidity': ask_liquidity,
            'liquidity_imbalance': (bid_liquidity - ask_liquidity) / total_liquidity * 100 if total_liquidity > 0 else 0,
            'avg_bid_size': avg_bid_size,
            'avg_ask_size': avg_ask_size
        }

    def _analyze_price_action(self):
        """Analyze recent price action"""
        if len(self.tick_data) < 10:
            return {}

        recent_ticks = list(self.tick_data)[-10:]
        prices = [tick['price'] for tick in recent_ticks]

        # Calculate price statistics
        price_range = max(prices) - min(prices)
        price_volatility = self._calculate_volatility(prices)

        # Trend analysis
        if len(prices) >= 3:
            trend = 'Up' if prices[-1] > prices[-3] else 'Down' if prices[-1] < prices[-3] else 'Sideways'
        else:
            trend = 'Sideways'

        return {
            'price_range': price_range,
            'volatility': price_volatility,
            'trend': trend,
            'momentum': self._calculate_momentum(prices)
        }

    def _calculate_bid_ask_ratio(self, bids, asks):
        """Calculate bid-ask ratio"""
        if not bids or not asks:
            return 1.0

        total_bid_vol = sum(bid[1] for bid in bids[:5])
        total_ask_vol = sum(ask[1] for ask in asks[:5])

        return total_bid_vol / total_ask_vol if total_ask_vol > 0 else float('inf')

    def _analyze_order_book_depth(self, bids, asks):
        """Analyze order book depth characteristics"""
        if not bids or not asks:
            return {}

        # Calculate depth at different price levels
        depth_levels = [0.1, 0.2, 0.5, 1.0]  # Percentage levels
        current_price = (bids[0][0] + asks[0][0]) / 2

        depth_analysis = {}
        for level_pct in depth_levels:
            price_range = current_price * level_pct / 100

            bid_depth = sum(bid[1] for bid in bids if bid[0] >= current_price - price_range)
            ask_depth = sum(ask[1] for ask in asks if ask[0] <= current_price + price_range)

            depth_analysis[f'depth_{level_pct}pct'] = {
                'bid_depth': bid_depth,
                'ask_depth': ask_depth,
                'total_depth': bid_depth + ask_depth
            }

        return depth_analysis

    def _identify_large_orders(self, bids, asks):
        """Identify unusually large orders"""
        all_orders = bids + asks

        if not all_orders:
            return []

        # Calculate average order size
        avg_size = sum(order[1] for order in all_orders) / len(all_orders)

        # Identify orders significantly larger than average
        large_orders = []
        for price, qty, orders in all_orders:
            if qty > avg_size * 3:  # 3x larger than average
                side = 'BID' if (price, qty, orders) in bids else 'ASK'
                large_orders.append({
                    'side': side,
                    'price': price,
                    'quantity': qty,
                    'size_ratio': qty / avg_size
                })

        return large_orders

    def _analyze_price_clustering(self, bids, asks):
        """Analyze price clustering patterns"""
        all_prices = [bid[0] for bid in bids] + [ask[0] for ask in asks]

        if not all_prices:
            return {}

        # Check for clustering at round numbers
        round_prices = 0
        for price in all_prices:
            if price == round(price) or price == round(price, 1):
                round_prices += 1

        clustering_ratio = round_prices / len(all_prices)

        return {
            'round_number_clustering': clustering_ratio,
            'price_distribution': 'Clustered' if clustering_ratio > 0.3 else 'Distributed'
        }

    def _calculate_volatility(self, prices):
        """Calculate price volatility"""
        if len(prices) < 2:
            return 0

        returns = []
        for i in range(1, len(prices)):
            returns.append((prices[i] - prices[i-1]) / prices[i-1])

        if not returns:
            return 0

        mean_return = sum(returns) / len(returns)
        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)

        return math.sqrt(variance)

    def _calculate_momentum(self, prices):
        """Calculate price momentum"""
        if len(prices) < 3:
            return 0

        # Simple momentum: (current - previous) / previous
        return (prices[-1] - prices[-3]) / prices[-3] if prices[-3] != 0 else 0
