"""
Offline Order Flow Engine Test
=============================

This script tests the order flow engine with simulated data
to validate functionality without requiring API credentials.
"""

import asyncio
import random
import time
from datetime import datetime, timedelta
import numpy as np

from order_flow_engine import OrderFlowEngine, AdvancedOrderFlowAnalyzer, Tick, OrderBook, OrderBookLevel
from order_flow_monitor import OrderFlowMonitor

class OrderFlowTester:
    """
    Comprehensive order flow engine tester with simulated market data
    """
    
    def __init__(self):
        self.symbols = ["RELIANCE", "TCS", "INFY"]
        self.test_results = {}
        
    async def test_basic_engine(self):
        """Test basic order flow engine functionality"""
        print("🔧 Testing Basic Order Flow Engine...")
        print("-" * 40)
        
        engine = OrderFlowEngine("RELIANCE")
        signals_generated = 0
        
        # Generate test data
        base_price = 2500.0
        current_price = base_price
        
        for i in range(100):
            # Simulate price movement
            price_change = random.gauss(0, 0.5)
            current_price += price_change
            current_price = max(current_price, base_price * 0.95)  # Floor
            current_price = min(current_price, base_price * 1.05)  # Ceiling
            
            # Create tick
            tick = Tick(
                timestamp=datetime.now() + timedelta(seconds=i),
                price=round(current_price, 2),
                volume=random.randint(100, 10000),
                buyer_initiated=random.choice([True, False])
            )
            
            # Process tick
            signal = engine.add_tick(tick)
            
            if signal:
                signals_generated += 1
                print(f"  🚨 Signal {signals_generated}: {signal.signal_type} "
                      f"(Strength: {signal.strength:.2f}, Confidence: {signal.confidence:.2f})")
                print(f"     Reasons: {', '.join(signal.reasons)}")
        
        # Get metrics
        metrics = engine.get_current_metrics()
        
        print(f"\n📊 Basic Engine Results:")
        print(f"  Ticks Processed: {len(engine.ticks)}")
        print(f"  Signals Generated: {signals_generated}")
        print(f"  Current VWAP: ₹{metrics.get('current_vwap', 0):.2f}")
        print(f"  Cumulative Delta: {metrics.get('cumulative_delta', 0):,}")
        
        return {
            'ticks_processed': len(engine.ticks),
            'signals_generated': signals_generated,
            'engine_working': signals_generated > 0
        }
    
    async def test_advanced_patterns(self):
        """Test advanced pattern detection"""
        print("\n🔍 Testing Advanced Pattern Detection...")
        print("-" * 40)
        
        analyzer = AdvancedOrderFlowAnalyzer("TCS")
        patterns_detected = []
        
        base_price = 3500.0
        current_price = base_price
        
        # Scenario 1: Volume Spike
        print("  📈 Scenario 1: Volume Spike")
        for i in range(20):
            volume = 50000 if i > 15 else random.randint(1000, 5000)  # Spike at end
            
            tick = Tick(
                timestamp=datetime.now() + timedelta(seconds=i),
                price=current_price + random.uniform(-1, 1),
                volume=volume,
                buyer_initiated=True
            )
            
            analyzer.flow_engine.add_tick(tick)
        
        patterns = analyzer.detect_order_flow_patterns()
        patterns_detected.extend(patterns)
        
        # Scenario 2: Institutional Activity
        print("  🏦 Scenario 2: Institutional Activity")
        for i in range(20):
            # Large institutional orders
            volume = random.randint(100000, 500000) if i % 5 == 0 else random.randint(1000, 5000)
            
            tick = Tick(
                timestamp=datetime.now() + timedelta(seconds=20 + i),
                price=current_price + random.uniform(-0.5, 0.5),
                volume=volume,
                buyer_initiated=True
            )
            
            analyzer.flow_engine.add_tick(tick)
        
        patterns = analyzer.detect_order_flow_patterns()
        patterns_detected.extend(patterns)
        
        # Scenario 3: Order Book Imbalance
        print("  ⚖️  Scenario 3: Order Book Imbalance")
        for i in range(10):
            # Create imbalanced order book
            bids = [OrderBookLevel(price=current_price - j*0.05, quantity=10000, orders=5) for j in range(1, 6)]
            asks = [OrderBookLevel(price=current_price + j*0.05, quantity=1000, orders=1) for j in range(1, 6)]  # Much smaller ask quantities
            
            order_book = OrderBook(
                timestamp=datetime.now() + timedelta(seconds=40 + i),
                bids=bids,
                asks=asks
            )
            
            analyzer.flow_engine.add_order_book(order_book)
        
        patterns = analyzer.detect_order_flow_patterns()
        patterns_detected.extend(patterns)
        
        # Print detected patterns
        print(f"\n🔍 Patterns Detected:")
        unique_patterns = {}
        for pattern in patterns_detected:
            pattern_key = f"{pattern['pattern']}_{pattern['direction']}"
            if pattern_key not in unique_patterns:
                unique_patterns[pattern_key] = pattern
                print(f"  • {pattern['pattern']}: {pattern['direction']} "
                      f"(strength: {pattern['strength']:.2f})")
        
        return {
            'patterns_detected': len(unique_patterns),
            'pattern_types': list(unique_patterns.keys()),
            'advanced_working': len(unique_patterns) > 0
        }
    
    async def test_performance(self):
        """Test engine performance with high-frequency data"""
        print("\n⚡ Testing Performance...")
        print("-" * 40)
        
        engine = OrderFlowEngine("PERFORMANCE_TEST")
        
        tick_count = 10000
        start_time = time.time()
        
        print(f"  Processing {tick_count:,} ticks...")
        
        base_price = 2000.0
        for i in range(tick_count):
            tick = Tick(
                timestamp=datetime.now() + timedelta(milliseconds=i),
                price=base_price + random.uniform(-10, 10),
                volume=random.randint(100, 10000),
                buyer_initiated=random.choice([True, False])
            )
            
            engine.add_tick(tick)
            
            # Add order book every 100 ticks
            if i % 100 == 0:
                bids = [OrderBookLevel(price=base_price - j*0.05, quantity=random.randint(1000, 10000), orders=1) for j in range(1, 6)]
                asks = [OrderBookLevel(price=base_price + j*0.05, quantity=random.randint(1000, 10000), orders=1) for j in range(1, 6)]
                
                order_book = OrderBook(
                    timestamp=datetime.now() + timedelta(milliseconds=i),
                    bids=bids,
                    asks=asks
                )
                engine.add_order_book(order_book)
        
        end_time = time.time()
        duration = end_time - start_time
        ticks_per_second = tick_count / duration
        
        print(f"  ✅ Performance Results:")
        print(f"    Duration: {duration:.2f} seconds")
        print(f"    Rate: {ticks_per_second:,.0f} ticks/second")
        print(f"    Memory: {len(engine.ticks)} ticks stored")
        
        return {
            'ticks_per_second': ticks_per_second,
            'duration_seconds': duration,
            'performance_good': ticks_per_second > 1000
        }
    
    async def test_real_time_monitoring(self):
        """Test real-time monitoring system"""
        print("\n📡 Testing Real-Time Monitoring...")
        print("-" * 40)
        
        monitor = OrderFlowMonitor(["TEST_SYMBOL"])
        
        # Start monitoring in background
        monitor_task = asyncio.create_task(monitor.start_monitoring())
        
        # Generate data
        print("  📊 Generating market data...")
        
        base_price = 1500.0
        signals_generated = 0
        
        for i in range(50):
            # Create varying volume to trigger signals
            if i % 10 == 0:
                volume = random.randint(50000, 100000)  # Large volume
            else:
                volume = random.randint(1000, 5000)
            
            price = base_price + random.uniform(-5, 5)
            
            signal = monitor.add_tick_data(
                symbol="TEST_SYMBOL",
                price=price,
                volume=volume,
                buyer_initiated=random.choice([True, False])
            )
            
            if signal:
                signals_generated += 1
            
            await asyncio.sleep(0.1)  # 100ms between ticks
        
        # Stop monitoring
        monitor.stop_monitoring()
        
        try:
            await asyncio.wait_for(monitor_task, timeout=1.0)
        except asyncio.TimeoutError:
            pass
        
        print(f"  📊 Monitoring Results:")
        print(f"    Signals Generated: {monitor.total_signals_generated}")
        print(f"    System Working: {monitor.total_signals_generated > 0}")
        
        return {
            'signals_generated': monitor.total_signals_generated,
            'monitoring_working': monitor.total_signals_generated > 0
        }
    
    async def run_comprehensive_test(self):
        """Run all tests and generate report"""
        print("🚀 COMPREHENSIVE ORDER FLOW ENGINE TEST")
        print("=" * 60)
        
        # Run all tests
        basic_results = await self.test_basic_engine()
        pattern_results = await self.test_advanced_patterns()
        performance_results = await self.test_performance()
        monitoring_results = await self.test_real_time_monitoring()
        
        # Generate comprehensive report
        self._generate_test_report(basic_results, pattern_results, performance_results, monitoring_results)
    
    def _generate_test_report(self, basic, patterns, performance, monitoring):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("=" * 60)
        
        # Basic Engine Test
        print("🔧 Basic Engine Test:")
        print(f"  ✅ Working: {basic['engine_working']}")
        print(f"  📊 Ticks Processed: {basic['ticks_processed']}")
        print(f"  🚨 Signals Generated: {basic['signals_generated']}")
        
        # Pattern Detection Test
        print(f"\n🔍 Pattern Detection Test:")
        print(f"  ✅ Working: {patterns['advanced_working']}")
        print(f"  📈 Patterns Detected: {patterns['patterns_detected']}")
        print(f"  🎯 Pattern Types: {', '.join(patterns['pattern_types'])}")
        
        # Performance Test
        print(f"\n⚡ Performance Test:")
        print(f"  ✅ Good Performance: {performance['performance_good']}")
        print(f"  🚀 Speed: {performance['ticks_per_second']:,.0f} ticks/second")
        print(f"  ⏱️  Duration: {performance['duration_seconds']:.2f} seconds")
        
        # Monitoring Test
        print(f"\n📡 Monitoring Test:")
        print(f"  ✅ Working: {monitoring['monitoring_working']}")
        print(f"  🚨 Signals: {monitoring['signals_generated']}")
        
        # Overall Assessment
        all_working = (
            basic['engine_working'] and
            patterns['advanced_working'] and
            performance['performance_good'] and
            monitoring['monitoring_working']
        )
        
        print(f"\n🎯 OVERALL ASSESSMENT:")
        if all_working:
            print("  🟢 EXCELLENT - All systems working perfectly!")
            print("  ✅ Ready for live trading with real data")
        else:
            print("  🟡 PARTIAL - Some components need attention")
            if not basic['engine_working']:
                print("    ❌ Basic engine issues")
            if not patterns['advanced_working']:
                print("    ❌ Pattern detection issues")
            if not performance['performance_good']:
                print("    ❌ Performance issues")
            if not monitoring['monitoring_working']:
                print("    ❌ Monitoring issues")
        
        print("\n💡 Next Steps:")
        if all_working:
            print("  1. Test with real Smart API connection")
            print("  2. Configure with your API credentials")
            print("  3. Start with paper trading")
        else:
            print("  1. Review and fix failing components")
            print("  2. Re-run tests until all pass")
            print("  3. Then proceed to live API testing")
        
        print("=" * 60)


async def main():
    """Main test execution"""
    tester = OrderFlowTester()
    await tester.run_comprehensive_test()


if __name__ == "__main__":
    asyncio.run(main())
