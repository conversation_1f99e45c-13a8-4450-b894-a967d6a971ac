#!/usr/bin/env python3
"""
Test script for trading charts
"""

import tkinter as tk
import time
import threading
from trading_charts import TradingChartsWidget
from config import EXECUTIVE_COLORS
import numpy as np

def main():
    # Create main window
    root = tk.Tk()
    root.title("Trading Charts Test")
    root.geometry("1400x900")
    root.configure(bg=EXECUTIVE_COLORS['bg_primary'])
    
    # Create charts widget
    charts = TradingChartsWidget(root)
    charts.pack(fill='both', expand=True, padx=20, pady=20)
    
    # Simulate real-time data updates
    def simulate_data():
        base_price = 872.0
        while True:
            try:
                # Generate new price data
                price_change = np.random.normal(0, 0.5)
                new_price = base_price + price_change
                
                price_data = {
                    'open': base_price,
                    'high': max(base_price, new_price) + abs(np.random.normal(0, 0.2)),
                    'low': min(base_price, new_price) - abs(np.random.normal(0, 0.2)),
                    'close': new_price
                }
                
                volume = np.random.randint(1000, 5000)
                
                # Generate sample order book data
                bids = [(np.random.randint(100, 1000), np.random.randint(1, 5), new_price - i*0.1) 
                       for i in range(1, 11)]
                asks = [(new_price + i*0.1, np.random.randint(1, 5), np.random.randint(100, 1000)) 
                       for i in range(1, 11)]
                
                # Update charts
                def update_ui():
                    charts.update_order_book_data(bids, asks)
                    charts.add_new_data(price_data, volume)
                
                root.after(0, update_ui)
                base_price = new_price
                
                time.sleep(2)  # Update every 2 seconds
                
            except Exception as e:
                print(f"Error in simulation: {e}")
                break
    
    # Start simulation in background
    sim_thread = threading.Thread(target=simulate_data, daemon=True)
    sim_thread.start()
    
    # Start the application
    root.mainloop()

if __name__ == "__main__":
    main()
