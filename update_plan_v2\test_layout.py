#!/usr/bin/env python3
"""
Test the new layout with separate order book table and depth chart
"""

import tkinter as tk
import time
import threading
from main_app import TradingApp
from config import EXECUTIVE_COLORS

def main():
    print("🚀 Testing new layout...")
    print("📊 Layout structure:")
    print("   Left Column:")
    print("   ├── Order Book Table (top)")
    print("   └── Market Depth Chart (bottom)")
    print("   Middle Column:")
    print("   └── Trading Charts (Price, Volume, RSI)")
    print("   Right Column:")
    print("   ├── Trading Signals (top)")
    print("   └── Market Analytics (bottom)")
    print()
    
    # Create and run the application
    app = TradingApp()
    app.run()

if __name__ == "__main__":
    main()
