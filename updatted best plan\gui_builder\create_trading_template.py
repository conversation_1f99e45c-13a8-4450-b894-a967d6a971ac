"""
Create Trading System Template for PAGE GUI Builder
"""

import tkinter as tk
from tkinter import ttk
import os
from pathlib import Path


class TradingSystemTemplate:
    """Create a template trading system UI for PAGE"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("CGCL Trading System - Template")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1a1a1a')
        
        # Colors matching your system
        self.colors = {
            'bg_dark': '#1a1a1a',
            'bg_medium': '#2d2d2d',
            'bg_light': '#3d3d3d',
            'text_white': '#ffffff',
            'text_gray': '#cccccc',
            'text_green': '#00ff88',
            'ask_red': '#ff4444',
            'bid_green': '#44ff44'
        }
        
        self.create_template_ui()
    
    def create_template_ui(self):
        """Create the template UI structure"""
        
        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_dark'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Top panel
        self.create_top_panel(main_frame)
        
        # Middle panel (Order Book + Analytics)
        self.create_middle_panel(main_frame)
        
        # Bottom panel (Controls)
        self.create_bottom_panel(main_frame)
        
        # Add instructions
        self.add_instructions()
    
    def create_top_panel(self, parent):
        """Create top panel with performance, clock, and status"""
        top_frame = tk.Frame(parent, bg=self.colors['bg_medium'], relief=tk.RAISED, bd=2)
        top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Performance Panel
        perf_frame = tk.LabelFrame(top_frame, text="📊 Performance", 
                                  bg=self.colors['bg_medium'], fg=self.colors['text_white'])
        perf_frame.pack(side=tk.LEFT, padx=10, pady=10, fill=tk.Y)
        
        tk.Label(perf_frame, text="Latency: 25ms", bg=self.colors['bg_medium'], 
                fg=self.colors['text_green']).pack(anchor=tk.W)
        tk.Label(perf_frame, text="Throughput: 150/s", bg=self.colors['bg_medium'], 
                fg=self.colors['text_green']).pack(anchor=tk.W)
        tk.Label(perf_frame, text="Updates: 1,234", bg=self.colors['bg_medium'], 
                fg=self.colors['text_gray']).pack(anchor=tk.W)
        
        # Clock Panel
        clock_frame = tk.LabelFrame(top_frame, text="🕐 Market Time", 
                                   bg=self.colors['bg_medium'], fg=self.colors['text_white'])
        clock_frame.pack(side=tk.LEFT, padx=10, pady=10, fill=tk.Y)
        
        tk.Label(clock_frame, text="16:31:45", bg=self.colors['bg_medium'], 
                fg=self.colors['text_white'], font=('Arial', 14, 'bold')).pack()
        tk.Label(clock_frame, text="25 Jul 2025", bg=self.colors['bg_medium'], 
                fg=self.colors['text_gray']).pack()
        tk.Label(clock_frame, text="🔴 Market Closed", bg=self.colors['bg_medium'], 
                fg=self.colors['ask_red']).pack()
        
        # Status Panel
        status_frame = tk.LabelFrame(top_frame, text="📈 Market Status", 
                                    bg=self.colors['bg_medium'], fg=self.colors['text_white'])
        status_frame.pack(side=tk.RIGHT, padx=10, pady=10, fill=tk.Y)
        
        tk.Label(status_frame, text="CGCL", bg=self.colors['bg_medium'], 
                fg=self.colors['text_white'], font=('Arial', 12, 'bold')).pack()
        tk.Label(status_frame, text="₹180.75", bg=self.colors['bg_medium'], 
                fg=self.colors['text_white'], font=('Arial', 14, 'bold')).pack()
        tk.Label(status_frame, text="Market Closed", bg=self.colors['bg_medium'], 
                fg=self.colors['ask_red']).pack()
        tk.Label(status_frame, text="Vol: --", bg=self.colors['bg_medium'], 
                fg=self.colors['text_gray']).pack()
    
    def create_middle_panel(self, parent):
        """Create middle panel with order book and analytics"""
        middle_frame = tk.Frame(parent, bg=self.colors['bg_dark'])
        middle_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Order Book Panel
        ob_frame = tk.LabelFrame(middle_frame, text="📊 Order Book - CGCL", 
                                bg=self.colors['bg_medium'], fg=self.colors['text_white'])
        ob_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Order book headers
        headers_frame = tk.Frame(ob_frame, bg=self.colors['bg_light'])
        headers_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(headers_frame, text="Qty", bg=self.colors['bg_light'], 
                fg=self.colors['text_white'], width=8).pack(side=tk.LEFT)
        tk.Label(headers_frame, text="Orders", bg=self.colors['bg_light'], 
                fg=self.colors['text_white'], width=8).pack(side=tk.LEFT)
        tk.Label(headers_frame, text="Price", bg=self.colors['bg_light'], 
                fg=self.colors['text_white'], width=10).pack(side=tk.LEFT)
        tk.Label(headers_frame, text="Price", bg=self.colors['bg_light'], 
                fg=self.colors['text_white'], width=10).pack(side=tk.LEFT)
        tk.Label(headers_frame, text="Orders", bg=self.colors['bg_light'], 
                fg=self.colors['text_white'], width=8).pack(side=tk.LEFT)
        tk.Label(headers_frame, text="Qty", bg=self.colors['bg_light'], 
                fg=self.colors['text_white'], width=8).pack(side=tk.LEFT)
        
        # Sample order book data
        for i in range(10):
            row_frame = tk.Frame(ob_frame, bg=self.colors['bg_medium'])
            row_frame.pack(fill=tk.X, padx=5, pady=1)
            
            # Ask side
            tk.Label(row_frame, text=f"{100+i*10}", bg=self.colors['bg_medium'], 
                    fg=self.colors['ask_red'], width=8).pack(side=tk.LEFT)
            tk.Label(row_frame, text=f"{i+1}", bg=self.colors['bg_medium'], 
                    fg=self.colors['text_gray'], width=8).pack(side=tk.LEFT)
            tk.Label(row_frame, text=f"₹{180.75+i*0.05:.2f}", bg=self.colors['bg_medium'], 
                    fg=self.colors['ask_red'], width=10).pack(side=tk.LEFT)
            
            # Bid side
            tk.Label(row_frame, text=f"₹{180.70-i*0.05:.2f}", bg=self.colors['bg_medium'], 
                    fg=self.colors['bid_green'], width=10).pack(side=tk.LEFT)
            tk.Label(row_frame, text=f"{i+1}", bg=self.colors['bg_medium'], 
                    fg=self.colors['text_gray'], width=8).pack(side=tk.LEFT)
            tk.Label(row_frame, text=f"{120+i*15}", bg=self.colors['bg_medium'], 
                    fg=self.colors['bid_green'], width=8).pack(side=tk.LEFT)
        
        # Analytics Panel
        analytics_frame = tk.LabelFrame(middle_frame, text="🔍 Analytics", 
                                       bg=self.colors['bg_medium'], fg=self.colors['text_white'])
        analytics_frame.pack(side=tk.RIGHT, fill=tk.Y, width=350)
        
        # Flow Analysis
        flow_frame = tk.LabelFrame(analytics_frame, text="Order Flow", 
                                  bg=self.colors['bg_light'], fg=self.colors['text_white'])
        flow_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(flow_frame, text="Imbalance: +15.2%", bg=self.colors['bg_light'], 
                fg=self.colors['text_green']).pack(anchor=tk.W)
        tk.Label(flow_frame, text="Momentum: Bullish", bg=self.colors['bg_light'], 
                fg=self.colors['text_green']).pack(anchor=tk.W)
        tk.Label(flow_frame, text="Institutional: 65%", bg=self.colors['bg_light'], 
                fg=self.colors['text_white']).pack(anchor=tk.W)
        
        # Price Prediction
        pred_frame = tk.LabelFrame(analytics_frame, text="30-Min Prediction", 
                                  bg=self.colors['bg_light'], fg=self.colors['text_white'])
        pred_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(pred_frame, text="Target: ₹182.50", bg=self.colors['bg_light'], 
                fg=self.colors['text_green']).pack(anchor=tk.W)
        tk.Label(pred_frame, text="Confidence: 78%", bg=self.colors['bg_light'], 
                fg=self.colors['text_white']).pack(anchor=tk.W)
        tk.Label(pred_frame, text="Direction: UP", bg=self.colors['bg_light'], 
                fg=self.colors['text_green']).pack(anchor=tk.W)
        
        # Trading Signals
        signals_frame = tk.LabelFrame(analytics_frame, text="Trading Signals", 
                                     bg=self.colors['bg_light'], fg=self.colors['text_white'])
        signals_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(signals_frame, text="Signal: BUY", bg=self.colors['bg_light'], 
                fg=self.colors['text_green'], font=('Arial', 12, 'bold')).pack(anchor=tk.W)
        tk.Label(signals_frame, text="Strength: 85%", bg=self.colors['bg_light'], 
                fg=self.colors['text_white']).pack(anchor=tk.W)
        tk.Label(signals_frame, text="Type: Flow Imbalance", bg=self.colors['bg_light'], 
                fg=self.colors['text_gray']).pack(anchor=tk.W)
    
    def create_bottom_panel(self, parent):
        """Create bottom control panel"""
        bottom_frame = tk.Frame(parent, bg=self.colors['bg_medium'], relief=tk.RAISED, bd=2)
        bottom_frame.pack(fill=tk.X)
        
        # Control buttons
        tk.Button(bottom_frame, text="🔌 Connect", bg=self.colors['text_green'], 
                 fg='black', font=('Arial', 10, 'bold'), padx=20, pady=5).pack(side=tk.LEFT, padx=10, pady=10)
        
        tk.Button(bottom_frame, text="📊 Analytics", bg=self.colors['bg_light'], 
                 fg=self.colors['text_white'], font=('Arial', 10), padx=20, pady=5).pack(side=tk.LEFT, padx=10, pady=10)
        
        tk.Button(bottom_frame, text="⚙️ Settings", bg=self.colors['bg_light'], 
                 fg=self.colors['text_white'], font=('Arial', 10), padx=20, pady=5).pack(side=tk.LEFT, padx=10, pady=10)
        
        tk.Button(bottom_frame, text="❌ Exit", bg=self.colors['ask_red'], 
                 fg='white', font=('Arial', 10, 'bold'), padx=20, pady=5).pack(side=tk.RIGHT, padx=10, pady=10)
    
    def add_instructions(self):
        """Add instructions for using PAGE"""
        instructions = tk.Toplevel(self.root)
        instructions.title("PAGE GUI Builder Instructions")
        instructions.geometry("600x400")
        instructions.configure(bg='white')
        
        text_widget = tk.Text(instructions, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill=tk.BOTH, expand=True)
        
        instructions_text = """
🎨 PAGE GUI BUILDER INSTRUCTIONS FOR CGCL TRADING SYSTEM

1. DOWNLOAD PAGE:
   • Go to: https://sourceforge.net/projects/page/
   • Download and extract PAGE
   • Run page.exe

2. CREATE NEW PROJECT:
   • File → New Project
   • Choose "Tkinter" as framework
   • Set window size to 1200x800

3. DESIGN YOUR UI:
   • Drag widgets from the palette
   • Use Frame widgets for panels
   • Add Labels for text display
   • Add Buttons for controls
   • Use LabelFrame for grouped sections

4. GENERATE CODE:
   • File → Generate Python Code
   • This creates two files:
     - main_ui.py (UI structure)
     - main_ui_support.py (event handlers)

5. INTEGRATE WITH TRADING SYSTEM:
   • Copy generated code to your project
   • Replace widget update calls in main_window.py
   • Connect event handlers to your trading logic

6. RECOMMENDED WIDGETS FOR TRADING:
   • Frame: For panels and sections
   • Label: For displaying prices, data
   • Button: For controls
   • Listbox: For order book display
   • Canvas: For charts and graphs
   • Entry: For user input

7. COLOR SCHEME:
   • Background: #1a1a1a (dark)
   • Panels: #2d2d2d (medium)
   • Text: #ffffff (white)
   • Green: #00ff88 (bids/positive)
   • Red: #ff4444 (asks/negative)

8. LAYOUT TIPS:
   • Use grid layout for precise positioning
   • Group related widgets in frames
   • Leave space for real-time updates
   • Consider responsive design

This template shows the structure of your trading system.
Use it as a reference when designing in PAGE!
        """
        
        text_widget.insert(tk.END, instructions_text)
        text_widget.config(state=tk.DISABLED)
    
    def run(self):
        """Run the template"""
        self.root.mainloop()


def create_code_integration_helper():
    """Create helper script for integrating PAGE-generated code"""
    
    helper_content = '''"""
PAGE Code Integration Helper for CGCL Trading System
"""

import os
import re
from pathlib import Path


class PageCodeIntegrator:
    """Integrate PAGE-generated code with trading system"""
    
    def __init__(self, page_generated_file: str):
        self.page_file = Path(page_generated_file)
        self.trading_system_dir = Path(__file__).parent.parent
        
    def integrate_ui_code(self):
        """Integrate PAGE UI code with trading system"""
        
        if not self.page_file.exists():
            print(f"❌ PAGE file not found: {self.page_file}")
            return False
        
        # Read PAGE-generated code
        with open(self.page_file, 'r') as f:
            page_code = f.read()
        
        # Extract widget creation code
        widget_code = self.extract_widget_creation(page_code)
        
        # Extract layout code
        layout_code = self.extract_layout_code(page_code)
        
        # Generate integration template
        integration_template = self.create_integration_template(widget_code, layout_code)
        
        # Save integration file
        output_file = self.trading_system_dir / "gui" / "page_generated_ui.py"
        with open(output_file, 'w') as f:
            f.write(integration_template)
        
        print(f"✅ Integration template created: {output_file}")
        print("📝 Next steps:")
        print("1. Review the generated template")
        print("2. Update main_window.py to use the new UI")
        print("3. Connect event handlers to trading logic")
        
        return True
    
    def extract_widget_creation(self, code: str) -> str:
        """Extract widget creation code from PAGE output"""
        # This would parse the PAGE-generated code
        # and extract widget creation statements
        return "# Widget creation code will be extracted here"
    
    def extract_layout_code(self, code: str) -> str:
        """Extract layout code from PAGE output"""
        # This would parse the PAGE-generated code
        # and extract layout management statements
        return "# Layout code will be extracted here"
    
    def create_integration_template(self, widget_code: str, layout_code: str) -> str:
        """Create integration template"""
        
        template = f'''"""
PAGE-Generated UI Integration for CGCL Trading System
Auto-generated from PAGE GUI Builder
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
from typing import Dict, Optional

from config.settings import COLORS, SYSTEM_CONFIG
from core.data_structures import MarketData, OrderBookSnapshot


class PageGeneratedUI:
    """PAGE-generated UI integrated with trading system"""
    
    def __init__(self, parent):
        self.parent = parent
        self.widgets = {{}}
        self.create_widgets()
        self.setup_layout()
    
    def create_widgets(self):
        """Create widgets (from PAGE)"""
        {widget_code}
    
    def setup_layout(self):
        """Setup layout (from PAGE)"""
        {layout_code}
    
    def update_market_data(self, market_data: MarketData):
        """Update UI with market data"""
        # Connect this to your trading system data
        pass
    
    def update_order_book(self, snapshot: OrderBookSnapshot):
        """Update order book display"""
        # Connect this to your order book data
        pass
    
    def on_connect_clicked(self):
        """Handle connect button click"""
        # Connect this to your WebSocket connection logic
        pass
    
    def on_analytics_clicked(self):
        """Handle analytics button click"""
        # Connect this to your analytics display
        pass
'''
        
        return template


# Usage example:
# integrator = PageCodeIntegrator("path/to/page_generated_file.py")
# integrator.integrate_ui_code()
'''

    helper_file = Path(__file__).parent / "page_code_integrator.py"
    with open(helper_file, 'w') as f:
        f.write(helper_content)

    print(f"✅ Created code integration helper: {helper_file}")


if __name__ == "__main__":
    print("🎨 Creating Trading System Template for PAGE...")
    
    # Create the template UI
    template = TradingSystemTemplate()
    
    # Create integration helper
    create_code_integration_helper()
    
    print("✅ Template created! Use this as reference when designing in PAGE.")
    print("📝 Instructions window will open with the template.")
    
    # Run the template
    template.run()
