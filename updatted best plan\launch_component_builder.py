"""
Direct launcher for component builder
Bypasses any initialization scripts
"""

import sys
import os

# Add the gui directory to path
gui_path = os.path.join(os.path.dirname(__file__), 'gui')
sys.path.insert(0, gui_path)

# Import and run the component builder
from component_builder import ComponentBuilder

def main():
    print("🚀 Launching Component Builder...")
    print("📊 Features:")
    print("   ✅ 100% height increase for all charts")
    print("   ✅ Scrollable UI with mouse wheel support")
    print("   ✅ Chart widths adjusted for scrollbar")
    print("   ✅ Full space utilization")
    print()
    
    builder = ComponentBuilder()
    builder.run_component_demo()

if __name__ == "__main__":
    main()
