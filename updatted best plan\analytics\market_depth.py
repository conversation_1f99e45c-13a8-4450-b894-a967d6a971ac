"""
Market Depth Analytics - Angel One Style
"""

import statistics
from collections import deque
from datetime import datetime
from typing import Dict, List, Optional

from core.data_structures import OrderBookSnapshot


class MarketDepthAnalytics:
    """Advanced market depth analytics like Angel One"""
    
    def __init__(self):
        # Analytics state
        self.analytics_history = deque(maxlen=300)  # 5 minutes of analytics
        self.price_efficiency_history = deque(maxlen=100)
        self.liquidity_history = deque(maxlen=100)
        
        # Market microstructure metrics
        self.tick_size = 0.05  # Standard tick size
        self.lot_size = 1
        
        # Performance tracking
        self.analytics_count = 0
        self.last_analytics_time = None
        
    def calculate_comprehensive_analytics(self, snapshot: OrderBookSnapshot, 
                                        flow_analysis: Dict = None) -> Dict:
        """Calculate comprehensive market depth analytics"""
        try:
            timestamp = datetime.now()
            
            analytics = {
                'timestamp': timestamp,
                'basic_metrics': {},
                'liquidity_metrics': {},
                'efficiency_metrics': {},
                'microstructure_metrics': {},
                'risk_metrics': {},
                'angel_one_style_metrics': {}
            }
            
            # Basic order book metrics
            analytics['basic_metrics'] = self._calculate_basic_metrics(snapshot)
            
            # Liquidity analysis
            analytics['liquidity_metrics'] = self._calculate_liquidity_metrics(snapshot)
            
            # Price efficiency metrics
            analytics['efficiency_metrics'] = self._calculate_efficiency_metrics(snapshot)
            
            # Market microstructure analysis
            analytics['microstructure_metrics'] = self._calculate_microstructure_metrics(snapshot)
            
            # Risk assessment metrics
            analytics['risk_metrics'] = self._calculate_risk_metrics(snapshot, flow_analysis)
            
            # Angel One style specific metrics
            analytics['angel_one_style_metrics'] = self._calculate_angel_one_metrics(snapshot)
            
            # Store analytics
            self.analytics_history.append(analytics)
            self.analytics_count += 1
            self.last_analytics_time = timestamp
            
            return analytics
            
        except Exception as e:
            print(f"Error calculating comprehensive analytics: {e}")
            return {}
    
    def _calculate_basic_metrics(self, snapshot: OrderBookSnapshot) -> Dict:
        """Calculate basic order book metrics"""
        try:
            metrics = {
                'bid_levels': len(snapshot.bids),
                'ask_levels': len(snapshot.asks),
                'total_bid_quantity': snapshot.total_bid_quantity,
                'total_ask_quantity': snapshot.total_ask_quantity,
                'spread_absolute': snapshot.spread,
                'spread_percentage': (snapshot.spread / snapshot.mid_price) * 100 if snapshot.mid_price > 0 else 0,
                'mid_price': snapshot.mid_price,
                'imbalance': snapshot.imbalance
            }
            
            # Best bid/ask metrics
            if snapshot.bids:
                metrics['best_bid'] = snapshot.bids[0].price
                metrics['best_bid_quantity'] = snapshot.bids[0].quantity
                metrics['best_bid_orders'] = snapshot.bids[0].orders
            
            if snapshot.asks:
                metrics['best_ask'] = snapshot.asks[0].price
                metrics['best_ask_quantity'] = snapshot.asks[0].quantity
                metrics['best_ask_orders'] = snapshot.asks[0].orders
            
            # Depth metrics
            if len(snapshot.bids) >= 5:
                metrics['bid_depth_5'] = sum(level.quantity for level in snapshot.bids[:5])
            if len(snapshot.asks) >= 5:
                metrics['ask_depth_5'] = sum(level.quantity for level in snapshot.asks[:5])
            
            return metrics
            
        except Exception as e:
            print(f"Error calculating basic metrics: {e}")
            return {}
    
    def _calculate_liquidity_metrics(self, snapshot: OrderBookSnapshot) -> Dict:
        """Calculate liquidity metrics"""
        try:
            metrics = {}
            
            # Liquidity score (0-100)
            total_quantity = snapshot.total_bid_quantity + snapshot.total_ask_quantity
            total_orders = sum(level.orders for level in snapshot.bids + snapshot.asks)
            
            # Base liquidity score
            quantity_score = min(100, (total_quantity / 10000) * 100)  # Normalize to 10k shares
            order_score = min(100, (total_orders / 100) * 100)  # Normalize to 100 orders
            
            metrics['liquidity_score'] = (quantity_score * 0.7) + (order_score * 0.3)
            
            # Depth concentration
            if len(snapshot.bids) >= 3 and len(snapshot.asks) >= 3:
                top3_bid_qty = sum(level.quantity for level in snapshot.bids[:3])
                top3_ask_qty = sum(level.quantity for level in snapshot.asks[:3])
                
                bid_concentration = (top3_bid_qty / snapshot.total_bid_quantity) * 100 if snapshot.total_bid_quantity > 0 else 0
                ask_concentration = (top3_ask_qty / snapshot.total_ask_quantity) * 100 if snapshot.total_ask_quantity > 0 else 0
                
                metrics['bid_concentration'] = bid_concentration
                metrics['ask_concentration'] = ask_concentration
                metrics['avg_concentration'] = (bid_concentration + ask_concentration) / 2
            
            # Liquidity distribution
            if len(snapshot.bids) > 1:
                bid_quantities = [level.quantity for level in snapshot.bids]
                metrics['bid_quantity_std'] = statistics.stdev(bid_quantities)
                metrics['bid_quantity_cv'] = metrics['bid_quantity_std'] / statistics.mean(bid_quantities) if statistics.mean(bid_quantities) > 0 else 0
            
            if len(snapshot.asks) > 1:
                ask_quantities = [level.quantity for level in snapshot.asks]
                metrics['ask_quantity_std'] = statistics.stdev(ask_quantities)
                metrics['ask_quantity_cv'] = metrics['ask_quantity_std'] / statistics.mean(ask_quantities) if statistics.mean(ask_quantities) > 0 else 0
            
            # Store for trend analysis
            self.liquidity_history.append(metrics.get('liquidity_score', 0))
            
            return metrics
            
        except Exception as e:
            print(f"Error calculating liquidity metrics: {e}")
            return {}
    
    def _calculate_efficiency_metrics(self, snapshot: OrderBookSnapshot) -> Dict:
        """Calculate price efficiency metrics"""
        try:
            metrics = {}
            
            # Price efficiency score
            if snapshot.spread > 0 and snapshot.mid_price > 0:
                spread_efficiency = max(0, 100 - (snapshot.spread / snapshot.mid_price) * 10000)  # Lower spread = higher efficiency
                metrics['price_efficiency'] = spread_efficiency
            else:
                metrics['price_efficiency'] = 50  # Neutral score
            
            # Tick efficiency
            if snapshot.spread > 0:
                ticks_in_spread = snapshot.spread / self.tick_size
                metrics['ticks_in_spread'] = ticks_in_spread
                metrics['tick_efficiency'] = max(0, 100 - (ticks_in_spread - 1) * 10)  # Prefer 1-tick spreads
            
            # Price level efficiency
            if len(snapshot.bids) > 1:
                bid_price_gaps = [snapshot.bids[i-1].price - snapshot.bids[i].price for i in range(1, len(snapshot.bids))]
                avg_bid_gap = statistics.mean(bid_price_gaps)
                metrics['avg_bid_price_gap'] = avg_bid_gap
                metrics['bid_gap_consistency'] = 100 - (statistics.stdev(bid_price_gaps) / avg_bid_gap * 100) if avg_bid_gap > 0 else 0
            
            if len(snapshot.asks) > 1:
                ask_price_gaps = [snapshot.asks[i].price - snapshot.asks[i-1].price for i in range(1, len(snapshot.asks))]
                avg_ask_gap = statistics.mean(ask_price_gaps)
                metrics['avg_ask_price_gap'] = avg_ask_gap
                metrics['ask_gap_consistency'] = 100 - (statistics.stdev(ask_price_gaps) / avg_ask_gap * 100) if avg_ask_gap > 0 else 0
            
            # Store for trend analysis
            self.price_efficiency_history.append(metrics.get('price_efficiency', 50))
            
            return metrics
            
        except Exception as e:
            print(f"Error calculating efficiency metrics: {e}")
            return {}
    
    def _calculate_microstructure_metrics(self, snapshot: OrderBookSnapshot) -> Dict:
        """Calculate market microstructure metrics"""
        try:
            metrics = {}
            
            # Order size analysis
            if snapshot.bids:
                bid_sizes = [level.quantity for level in snapshot.bids]
                metrics['avg_bid_size'] = statistics.mean(bid_sizes)
                metrics['median_bid_size'] = statistics.median(bid_sizes)
                metrics['max_bid_size'] = max(bid_sizes)
                
                # Order count analysis
                bid_orders = [level.orders for level in snapshot.bids]
                metrics['avg_bid_orders'] = statistics.mean(bid_orders)
                metrics['total_bid_orders'] = sum(bid_orders)
                
                # Average size per order
                metrics['avg_bid_size_per_order'] = metrics['avg_bid_size'] / max(1, metrics['avg_bid_orders'])
            
            if snapshot.asks:
                ask_sizes = [level.quantity for level in snapshot.asks]
                metrics['avg_ask_size'] = statistics.mean(ask_sizes)
                metrics['median_ask_size'] = statistics.median(ask_sizes)
                metrics['max_ask_size'] = max(ask_sizes)
                
                # Order count analysis
                ask_orders = [level.orders for level in snapshot.asks]
                metrics['avg_ask_orders'] = statistics.mean(ask_orders)
                metrics['total_ask_orders'] = sum(ask_orders)
                
                # Average size per order
                metrics['avg_ask_size_per_order'] = metrics['avg_ask_size'] / max(1, metrics['avg_ask_orders'])
            
            # Market impact estimation
            if snapshot.bids and snapshot.asks:
                # Estimate impact of 1000 share market order
                impact_size = 1000
                
                # Buy impact (market order hitting asks)
                cumulative_ask_qty = 0
                weighted_ask_price = 0
                for level in snapshot.asks:
                    if cumulative_ask_qty >= impact_size:
                        break
                    qty_to_take = min(level.quantity, impact_size - cumulative_ask_qty)
                    weighted_ask_price += level.price * qty_to_take
                    cumulative_ask_qty += qty_to_take
                
                if cumulative_ask_qty > 0:
                    avg_buy_price = weighted_ask_price / cumulative_ask_qty
                    buy_impact = ((avg_buy_price - snapshot.mid_price) / snapshot.mid_price) * 100
                    metrics['buy_impact_1000'] = buy_impact
                
                # Sell impact (market order hitting bids)
                cumulative_bid_qty = 0
                weighted_bid_price = 0
                for level in snapshot.bids:
                    if cumulative_bid_qty >= impact_size:
                        break
                    qty_to_take = min(level.quantity, impact_size - cumulative_bid_qty)
                    weighted_bid_price += level.price * qty_to_take
                    cumulative_bid_qty += qty_to_take
                
                if cumulative_bid_qty > 0:
                    avg_sell_price = weighted_bid_price / cumulative_bid_qty
                    sell_impact = ((snapshot.mid_price - avg_sell_price) / snapshot.mid_price) * 100
                    metrics['sell_impact_1000'] = sell_impact
            
            return metrics
            
        except Exception as e:
            print(f"Error calculating microstructure metrics: {e}")
            return {}
    
    def _calculate_risk_metrics(self, snapshot: OrderBookSnapshot, flow_analysis: Dict = None) -> Dict:
        """Calculate risk assessment metrics"""
        try:
            metrics = {}
            
            # Spread risk
            if snapshot.mid_price > 0:
                spread_risk = (snapshot.spread / snapshot.mid_price) * 100
                metrics['spread_risk'] = spread_risk
                
                if spread_risk < 0.1:
                    metrics['spread_risk_level'] = 'LOW'
                elif spread_risk < 0.5:
                    metrics['spread_risk_level'] = 'MEDIUM'
                else:
                    metrics['spread_risk_level'] = 'HIGH'
            
            # Liquidity risk
            total_near_touch = 0
            if snapshot.bids:
                total_near_touch += snapshot.bids[0].quantity
            if snapshot.asks:
                total_near_touch += snapshot.asks[0].quantity
            
            if total_near_touch < 500:
                metrics['liquidity_risk_level'] = 'HIGH'
            elif total_near_touch < 2000:
                metrics['liquidity_risk_level'] = 'MEDIUM'
            else:
                metrics['liquidity_risk_level'] = 'LOW'
            
            # Imbalance risk
            imbalance_risk = abs(snapshot.imbalance)
            if imbalance_risk > 50:
                metrics['imbalance_risk_level'] = 'HIGH'
            elif imbalance_risk > 20:
                metrics['imbalance_risk_level'] = 'MEDIUM'
            else:
                metrics['imbalance_risk_level'] = 'LOW'
            
            # Flow risk (if flow analysis available)
            if flow_analysis:
                flow_momentum = flow_analysis.get('flow_momentum', {})
                momentum = abs(flow_momentum.get('momentum', 0))
                
                if momentum > 5:
                    metrics['flow_risk_level'] = 'HIGH'
                elif momentum > 2:
                    metrics['flow_risk_level'] = 'MEDIUM'
                else:
                    metrics['flow_risk_level'] = 'LOW'
            
            # Overall risk score
            risk_factors = [
                metrics.get('spread_risk_level', 'MEDIUM'),
                metrics.get('liquidity_risk_level', 'MEDIUM'),
                metrics.get('imbalance_risk_level', 'MEDIUM'),
                metrics.get('flow_risk_level', 'MEDIUM')
            ]
            
            high_risk_count = risk_factors.count('HIGH')
            medium_risk_count = risk_factors.count('MEDIUM')
            
            if high_risk_count >= 2:
                metrics['overall_risk_level'] = 'HIGH'
            elif high_risk_count >= 1 or medium_risk_count >= 3:
                metrics['overall_risk_level'] = 'MEDIUM'
            else:
                metrics['overall_risk_level'] = 'LOW'
            
            return metrics
            
        except Exception as e:
            print(f"Error calculating risk metrics: {e}")
            return {}
    
    def _calculate_angel_one_metrics(self, snapshot: OrderBookSnapshot) -> Dict:
        """Calculate Angel One style specific metrics"""
        try:
            metrics = {}
            
            # Angel One style depth percentage
            if snapshot.bids and snapshot.asks:
                best_bid_qty = snapshot.bids[0].quantity
                best_ask_qty = snapshot.asks[0].quantity
                
                total_best = best_bid_qty + best_ask_qty
                if total_best > 0:
                    metrics['best_bid_percentage'] = (best_bid_qty / total_best) * 100
                    metrics['best_ask_percentage'] = (best_ask_qty / total_best) * 100
            
            # Depth strength indicator
            if len(snapshot.bids) >= 5 and len(snapshot.asks) >= 5:
                top5_bid = sum(level.quantity for level in snapshot.bids[:5])
                top5_ask = sum(level.quantity for level in snapshot.asks[:5])
                
                metrics['top5_bid_strength'] = top5_bid
                metrics['top5_ask_strength'] = top5_ask
                metrics['top5_total_strength'] = top5_bid + top5_ask
            
            # Angel One style market quality score
            liquidity_component = min(100, (snapshot.total_bid_quantity + snapshot.total_ask_quantity) / 100)
            spread_component = max(0, 100 - (snapshot.spread / snapshot.mid_price) * 1000) if snapshot.mid_price > 0 else 50
            balance_component = max(0, 100 - abs(snapshot.imbalance))
            
            metrics['market_quality_score'] = (liquidity_component * 0.4) + (spread_component * 0.3) + (balance_component * 0.3)
            
            # Trading opportunity indicator
            if abs(snapshot.imbalance) > 20:
                metrics['trading_opportunity'] = 'HIGH'
            elif abs(snapshot.imbalance) > 10:
                metrics['trading_opportunity'] = 'MEDIUM'
            else:
                metrics['trading_opportunity'] = 'LOW'
            
            return metrics
            
        except Exception as e:
            print(f"Error calculating Angel One metrics: {e}")
            return {}
    
    def get_analytics_summary(self) -> Dict:
        """Get summary of market depth analytics"""
        try:
            if not self.analytics_history:
                return {}
            
            latest = self.analytics_history[-1]
            
            # Calculate trends
            liquidity_trend = 'STABLE'
            efficiency_trend = 'STABLE'
            
            if len(self.liquidity_history) >= 10:
                recent_liquidity = statistics.mean(list(self.liquidity_history)[-5:])
                older_liquidity = statistics.mean(list(self.liquidity_history)[-10:-5])
                
                if recent_liquidity > older_liquidity * 1.1:
                    liquidity_trend = 'IMPROVING'
                elif recent_liquidity < older_liquidity * 0.9:
                    liquidity_trend = 'DECLINING'
            
            if len(self.price_efficiency_history) >= 10:
                recent_efficiency = statistics.mean(list(self.price_efficiency_history)[-5:])
                older_efficiency = statistics.mean(list(self.price_efficiency_history)[-10:-5])
                
                if recent_efficiency > older_efficiency * 1.1:
                    efficiency_trend = 'IMPROVING'
                elif recent_efficiency < older_efficiency * 0.9:
                    efficiency_trend = 'DECLINING'
            
            return {
                'latest_analytics': latest,
                'liquidity_trend': liquidity_trend,
                'efficiency_trend': efficiency_trend,
                'analytics_count': self.analytics_count,
                'last_update': self.last_analytics_time
            }
            
        except Exception as e:
            print(f"Error getting analytics summary: {e}")
            return {}
