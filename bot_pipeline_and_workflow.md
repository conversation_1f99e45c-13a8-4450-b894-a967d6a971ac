# Equity Trading Bot: Pipeline Explanation and Weekly Workflow

This document details the bot's core decision-making pipeline for equity market trading and illustrates its operational flow over a typical trading week.

## 1. The Core Trading Pipeline

The bot's logic is a sequential, multi-stage pipeline designed to move from a high-level market event down to a specific, high-conviction trade execution. Each stage filters and refines the data to make a deterministic decision.

1.  **Event Processor (The "Why"):**
    *   **Input:** A machine-readable calendar of market-moving events (e.g., RBI policy, corporate earnings).
    *   **Process:** At the start of each day, this component scans the calendar to identify if any pre-defined, high-impact events are scheduled for today.
    *   **Output:** A signal that a specific event is happening, along with the associated stock/instrument (e.g., "Reliance Earnings Today"). If no event is scheduled, the bot remains dormant for the day.

2.  **Equity Flow Analysis Engine (The "How"):**
    *   **Input:** The event signal from the previous stage (e.g., "Reliance Earnings").
    *   **Process:** This engine activates only when an event is flagged. It then pulls in real-time and historical equity market data specifically for the stock related to the event. It uses a hardcoded, deterministic rule set to analyze:
        *   **Block/Bulk Deals:** Large institutional transactions indicating smart money flow
        *   **Delivery vs Intraday Volume:** Genuine accumulation vs speculative trading
        *   **Volume Profile:** At which price levels is the most significant trading happening?
        *   **Order Book Imbalances:** Real-time supply/demand dynamics
    *   **Output:** A clear, qualitative assessment of the institutional positioning (e.g., "Strong Bullish Institutional Accumulation Detected").

3.  **Deterministic Rule Engine & Playbook Executor (The "Decision"):**
    *   **Input:** The event signal and the institutional flow analysis.
    *   **Process:** This component acts as the final decision-maker. It takes the two inputs and consults a pre-defined "Playbook" — a set of hardcoded rules for that specific event. For example, the rule might be: `IF event is 'Reliance Earnings' AND flow is 'Strong Bullish Accumulation' THEN execute 'Earnings Long Playbook'`.
    *   **Output:** A command to execute a specific, pre-defined trading strategy (the playbook).

4.  **Pre-Trade Risk Check & Execution:**
    *   **Input:** The command from the Playbook Executor.
    *   **Process:** Before execution, the `Risk Manager` performs pre-trade checks to ensure the trade does not violate any risk parameters (e.g., max position size, max daily trades). If the checks pass, the `Playbook Executor` commands the `SmartAPIConnector` to place the trade.
    *   **Output:** A live order on the exchange.

5.  **Live Position Management:**
    *   **Input:** A live trade.
    *   **Process:** The `Risk Manager` continuously monitors the live position's Profit & Loss (P&L) against the playbook's defined stop-loss and take-profit levels.
    *   **Output:** An exit signal to the `Playbook Executor` if a limit is hit.

---

## 2. Flowchart: A Week in the Life of the Bot

This flowchart illustrates the bot's daily and weekly rhythm, showing how it operates from pre-market preparation to post-market analysis.

```mermaid
graph TD
    subgraph Monday
        A[Pre-Market: 8:00 AM] --> B{Event Check};
        B -- Event Today --> C[Activate Flow Analyzer];
        B -- No Event --> D[Remain Dormant];
        C --> E{Analysis Complete?};
        E -- Yes --> F[Execute Playbook];
        F --> G{Pre-Trade Risk Check};
        G -- Pass --> H[Place Order via API];
        G -- Fail --> I[Log Risk Rejection];
        H --> J[Monitor Position with Risk Manager];
        J --> K[Exit Signal?];
        K -- Yes --> L[Close Position via API];
        K -- No --> J;
        E -- No --> M[No Action];
        L --> N[Post-Market: 4:00 PM];
        I --> N;
        M --> N;
        N --> O[Log Daily P&L and Analysis];
    end

    subgraph Tuesday
        P[Pre-Market: 8:00 AM] --> Q{Event Check};
        Q -- Event Today --> R[Activate Flow Analyzer];
        Q -- No Event --> S[Remain Dormant];
        R --> T{Analysis Complete?};
        T -- Yes --> U[Execute Playbook];
        U --> V{Pre-Trade Risk Check};
        V -- Pass --> W[Place Order via API];
        V -- Fail --> X[Log Risk Rejection];
        W --> Y[Monitor Position with Risk Manager];
        Y --> Z[Exit Signal?];
        Z -- Yes --> AA[Close Position via API];
        Z -- No --> Y;
        T -- No --> AB[No Action];
        AA --> AC[Post-Market: 4:00 PM];
        X --> AC;
        AB --> AC;
        AC --> AD[Log Daily P&L and Analysis];
    end

    subgraph Wednesday
        AE[Pre-Market: 8:00 AM] --> AF{Event Check};
        AF -- Event Today --> AG[Activate Flow Analyzer];
        AF -- No Event --> AH[Remain Dormant];
        AG --> AI{Analysis Complete?};
        AI -- Yes --> AJ[Execute Playbook];
        AJ --> AK{Pre-Trade Risk Check};
        AK -- Pass --> AL[Place Order via API];
        AK -- Fail --> AM[Log Risk Rejection];
        AL --> AN[Monitor Position with Risk Manager];
        AN --> AO[Exit Signal?];
        AO -- Yes --> AP[Close Position via API];
        AO -- No --> AN;
        AI -- No --> AQ[No Action];
        AP --> AR[Post-Market: 4:00 PM];
        AM --> AR;
        AQ --> AR;
        AR --> AS[Log Daily P&L and Analysis];
    end

    subgraph Thursday_Expiry_Day
        AT[Pre-Market: 8:00 AM] --> AU{Event Check};
        AU -- Event Today --> AV[Activate Flow Analyzer];
        AU -- No Event --> AW[Remain Dormant];
        AV --> AX{Analysis Complete?};
        AX -- Yes --> AY[Execute Playbook];
        AY --> AZ{Pre-Trade Risk Check};
        AZ -- Pass --> BA[Place Order via API];
        AZ -- Fail --> BB[Log Risk Rejection];
        BA --> BC[Monitor Position with Risk Manager];
        BC --> BD[Exit Signal?];
        BD -- Yes --> BE[Close Position via API];
        BD -- No --> BC;
        AX -- No --> BF[No Action];
        BE --> BG[Post-Market: 4:00 PM];
        BB --> BG;
        BF --> BG;
        BG --> BH[Log Daily P&L and Analysis];
    end

    subgraph Friday
        BI[Pre-Market: 8:00 AM] --> BJ{Event Check};
        BJ -- Event Today --> BK[Activate Flow Analyzer];
        BJ -- No Event --> BL[Remain Dormant];
        BK --> BM{Analysis Complete?};
        BM -- Yes --> BN[Execute Playbook];
        BN --> BO{Pre-Trade Risk Check};
        BO -- Pass --> BP[Place Order via API];
        BO -- Fail --> BQ[Log Risk Rejection];
        BP --> BR[Monitor Position with Risk Manager];
        BR --> BS[Exit Signal?];
        BS -- Yes --> BT[Close Position via API];
        BS -- No --> BR;
        BM -- No --> BU[No Action];
        BT --> BV[Post-Market: 4:00 PM];
        BQ --> BV;
        BU --> BV;
        BV --> BW[Log Daily P&L and Analysis];
    end

    subgraph Weekend
        AY[Saturday/Sunday] --> AZ[System Maintenance, Strategy Review, Backtesting];
    end

    O --> P;
    AD --> AE;
    AS --> AT;
    BH --> BI;
    BW --> AY;
