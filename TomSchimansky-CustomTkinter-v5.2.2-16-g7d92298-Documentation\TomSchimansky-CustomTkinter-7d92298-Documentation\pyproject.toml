[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[tool.tbump]
github_url = "https://github.com/TomSchimansky/CustomTkinter"

[tool.tbump.version]
current = "5.2.2"

# Example of a semver regexp.
# Make sure this matches current_version before
# using tbump
regex = '''
  (?P<major>\d+)
  \.
  (?P<minor>\d+)
  \.
  (?P<patch>\d+)
  '''

[tool.tbump.git]
message_template = "Bump to {new_version}"
tag_template = "v{new_version}"

# For each file to patch, add a [[tool.tbump.file]] config
# section containing the path of the file, relative to the
# tbump.toml location.
[[tool.tbump.file]]
src = "setup.cfg"

[[tool.tbump.file]]
src = "customtkinter/__init__.py"
search = "__version__ = \"{current_version}\""
