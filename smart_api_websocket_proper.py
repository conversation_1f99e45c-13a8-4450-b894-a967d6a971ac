"""
Proper Smart API WebSocket Implementation
========================================

Using the official Smart API WebSocket format for order flow analysis.
"""

import json
import time
import struct
import asyncio
from datetime import datetime
import logging

# Import the official Smart API WebSocket class
import sys
import os
sys.path.append('api_documentation')

from order_flow_engine import Order<PERSON>lowEngine, AdvancedOrderFlowAnalyzer, Tick, OrderBook, OrderBookLevel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SmartAPIOrderFlowWebSocket:
    """
    Smart API WebSocket integration with Order Flow Engine
    Using proper Smart API WebSocket V2 format
    """
    
    def __init__(self, credentials_file="smart_api_credentials.json"):
        # Load credentials
        try:
            with open(credentials_file, 'r') as f:
                self.credentials = json.load(f)
        except FileNotFoundError:
            logger.error("Credentials file not found. Run test_smart_api_auth.py first.")
            raise
        
        # Order flow components
        self.symbols = ["RELIANCE", "TCS", "INFY"]
        self.analyzers = {symbol: AdvancedOrderFlowAnalyzer(symbol) for symbol in self.symbols}
        
        # Data tracking
        self.tick_count = 0
        self.signal_count = 0
        self.connection_start_time = None
        
        # Symbol tokens for NSE (these are example tokens - need real ones)
        self.symbol_tokens = {
            "RELIANCE": "2885",
            "TCS": "11536", 
            "INFY": "1594",
            "HDFCBANK": "1333",
            "ICICIBANK": "4963"
        }
        
        # WebSocket instance
        self.ws_client = None
        self.is_connected = False
    
    def create_websocket_client(self):
        """Create Smart API WebSocket client"""
        try:
            # Import the Smart API WebSocket class
            from smartWebSocketV2 import SmartWebSocketV2
            
            # Create WebSocket client
            self.ws_client = SmartWebSocketV2(
                auth_token=self.credentials['auth_token'],
                api_key=self.credentials['api_key'],
                client_code=self.credentials['client_code'],
                feed_token=self.credentials['feed_token']
            )
            
            # Set callback functions
            self.ws_client.on_open = self._on_open
            self.ws_client.on_data = self._on_data
            self.ws_client.on_error = self._on_error
            self.ws_client.on_close = self._on_close
            
            return True
            
        except ImportError:
            logger.error("Smart API WebSocket module not found")
            return False
        except Exception as e:
            logger.error(f"WebSocket client creation failed: {e}")
            return False
    
    def connect(self):
        """Connect to Smart API WebSocket"""
        logger.info("🔌 Connecting to Smart API WebSocket...")
        
        if not self.create_websocket_client():
            return False
        
        try:
            self.connection_start_time = time.time()
            
            # Connect to WebSocket
            self.ws_client.connect()
            
            return True
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    def _on_open(self, wsapp):
        """WebSocket opened callback"""
        connection_time = time.time() - self.connection_start_time
        logger.info(f"✅ WebSocket connected in {connection_time:.2f}s")
        self.is_connected = True
        
        # Subscribe to symbols
        self._subscribe_symbols()
    
    def _on_data(self, wsapp, data):
        """Data received callback"""
        try:
            if isinstance(data, dict):
                self._process_tick_data(data)
            else:
                logger.debug(f"Received non-dict data: {type(data)}")
                
        except Exception as e:
            logger.error(f"Data processing error: {e}")
    
    def _on_error(self, wsapp, error):
        """Error callback"""
        logger.error(f"❌ WebSocket error: {error}")
    
    def _on_close(self, wsapp):
        """Close callback"""
        logger.warning("🔌 WebSocket connection closed")
        self.is_connected = False
    
    def _subscribe_symbols(self):
        """Subscribe to symbol data using proper Smart API format"""
        logger.info("📡 Subscribing to symbols...")
        
        try:
            # Prepare token list for NSE CM (Cash Market)
            token_list = []
            
            # Get tokens for our symbols
            tokens = [self.symbol_tokens[symbol] for symbol in self.symbols if symbol in self.symbol_tokens]
            
            if tokens:
                token_list.append({
                    "exchangeType": 1,  # NSE CM
                    "tokens": tokens
                })
            
            # Subscribe to LTP mode
            if token_list:
                self.ws_client.subscribe("correlation_ltp", 1, token_list)  # Mode 1 = LTP
                logger.info(f"📊 Subscribed to LTP for {len(tokens)} symbols")
                
                # Also subscribe to Quote mode for more data
                self.ws_client.subscribe("correlation_quote", 2, token_list)  # Mode 2 = Quote
                logger.info(f"📈 Subscribed to Quote for {len(tokens)} symbols")
            else:
                logger.warning("No valid tokens found for subscription")
                
        except Exception as e:
            logger.error(f"Subscription failed: {e}")
    
    def _process_tick_data(self, data):
        """Process tick data from Smart API"""
        try:
            # Extract data based on Smart API format
            token = str(data.get('token', ''))
            ltp = data.get('last_traded_price', 0)
            volume = data.get('volume_traded_today', 0)
            
            # Find symbol by token
            symbol = None
            for sym, tok in self.symbol_tokens.items():
                if tok == token:
                    symbol = sym
                    break
            
            if not symbol:
                symbol = f"TOKEN_{token}"
            
            if ltp > 0:
                self.tick_count += 1
                
                # Create tick object
                tick = Tick(
                    timestamp=datetime.now(),
                    price=float(ltp),
                    volume=int(volume) if volume else 1,
                    buyer_initiated=True  # Simplified - would need bid/ask comparison
                )
                
                # Process through order flow engine
                if symbol in self.analyzers:
                    signal = self.analyzers[symbol].flow_engine.add_tick(tick)
                    
                    if signal:
                        self.signal_count += 1
                        self._handle_signal(symbol, signal, tick)
                
                # Log every 10th tick
                if self.tick_count % 10 == 0:
                    logger.info(f"📊 Tick #{self.tick_count}: {symbol} ₹{ltp:.2f}")
                
        except Exception as e:
            logger.error(f"Tick processing error: {e}")
    
    def _handle_signal(self, symbol, signal, tick):
        """Handle generated signal"""
        logger.info(f"🚨 SIGNAL #{self.signal_count}: {symbol} - {signal.signal_type} "
                   f"(Strength: {signal.strength:.2f}, Confidence: {signal.confidence:.2f})")
        
        # Print detailed signal
        print(f"\n{'='*60}")
        print(f"📊 ORDER FLOW SIGNAL - {symbol}")
        print(f"{'='*60}")
        print(f"Time: {tick.timestamp.strftime('%H:%M:%S')}")
        print(f"Signal: {signal.signal_type}")
        print(f"Strength: {signal.strength:.2f}")
        print(f"Confidence: {signal.confidence:.2f}")
        print(f"Price: ₹{tick.price:.2f}")
        print(f"Volume: {tick.volume:,}")
        print(f"Reasons: {', '.join(signal.reasons)}")
        
        # Get current metrics
        metrics = self.analyzers[symbol].flow_engine.get_current_metrics()
        print(f"\n📈 Current Metrics:")
        print(f"  VWAP: ₹{metrics.get('current_vwap', 0):.2f}")
        print(f"  VWAP Deviation: {metrics.get('vwap_deviation', 0)*100:.2f}%")
        print(f"  Cumulative Delta: {metrics.get('cumulative_delta', 0):,}")
        print(f"{'='*60}\n")
    
    def get_status(self):
        """Get connection status"""
        return {
            'connected': self.is_connected,
            'ticks_processed': self.tick_count,
            'signals_generated': self.signal_count,
            'uptime_seconds': time.time() - self.connection_start_time if self.connection_start_time else 0,
            'symbols_monitored': len(self.symbols)
        }
    
    def disconnect(self):
        """Disconnect WebSocket"""
        if self.ws_client:
            try:
                self.ws_client.close_connection()
                logger.info("🔌 WebSocket disconnected")
            except:
                pass
        self.is_connected = False


async def test_smart_api_websocket():
    """Test Smart API WebSocket with order flow"""
    print("🚀 SMART API WEBSOCKET ORDER FLOW TEST")
    print("=" * 60)
    
    try:
        # Create WebSocket client
        ws_client = SmartAPIOrderFlowWebSocket()
        
        # Connect
        if not ws_client.connect():
            print("❌ Connection failed")
            return
        
        print("⏳ Waiting for connection and data...")
        
        # Monitor for 2 minutes
        test_duration = 120
        start_time = time.time()
        
        while (time.time() - start_time) < test_duration:
            await asyncio.sleep(10)
            
            status = ws_client.get_status()
            elapsed = int(time.time() - start_time)
            
            print(f"\n⏱️  Status ({elapsed}s):")
            print(f"  Connected: {status['connected']}")
            print(f"  Ticks: {status['ticks_processed']}")
            print(f"  Signals: {status['signals_generated']}")
            
            if not status['connected']:
                print("❌ Connection lost")
                break
        
        # Final results
        final_status = ws_client.get_status()
        
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS")
        print("=" * 60)
        print(f"✅ Connection: {'SUCCESS' if final_status['connected'] else 'FAILED'}")
        print(f"📈 Ticks Processed: {final_status['ticks_processed']}")
        print(f"🚨 Signals Generated: {final_status['signals_generated']}")
        print(f"⏱️  Duration: {test_duration} seconds")
        
        if final_status['signals_generated'] > 0:
            print("🎯 Order flow engine working with live Smart API data!")
        elif final_status['ticks_processed'] > 0:
            print("📊 Receiving data but no signals (normal during low activity)")
        else:
            print("⚠️  No data received - check market hours and tokens")
        
        # Disconnect
        ws_client.disconnect()
        
    except KeyboardInterrupt:
        print("\n⏹️  Test stopped by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")


if __name__ == "__main__":
    asyncio.run(test_smart_api_websocket())
