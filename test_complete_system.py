"""
Test Complete Real-Time System
=============================

Test the complete real-time order flow system with simulated data
to ensure all components work together.
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import random
from datetime import datetime, timedelta
import logging

from order_flow_engine import AdvancedOrderFlowAnalyzer, Tick, OrderBook, OrderBookLevel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestOrderBookGUI:
    """Simplified GUI for testing"""
    
    def __init__(self, symbols):
        self.symbols = symbols
        self.root = tk.Tk()
        self.root.title("Test Order Book Display")
        self.root.geometry("800x600")
        
        # Create simple display
        self.text_area = tk.Text(self.root, wrap=tk.WORD, font=("Courier", 10))
        self.text_area.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Status label
        self.status_label = tk.Label(self.root, text="Starting...", font=("Arial", 12))
        self.status_label.pack(pady=5)
        
        self.running = True
        
    def update_display(self, symbol, order_book):
        """Update display with order book data"""
        try:
            timestamp = order_book.timestamp.strftime('%H:%M:%S')
            
            # Format order book display
            display_text = f"\n{'='*50}\n"
            display_text += f"📊 {symbol} Order Book - {timestamp}\n"
            display_text += f"{'='*50}\n"
            
            # Show top 5 asks (highest to lowest)
            display_text += "ASKS (Sell Orders):\n"
            for i, ask in enumerate(order_book.asks[:5]):
                display_text += f"  {i+1}. ₹{ask.price:.2f} x {ask.quantity:,}\n"
            
            # Show spread
            if order_book.bids and order_book.asks:
                spread = order_book.asks[0].price - order_book.bids[0].price
                display_text += f"\nSPREAD: ₹{spread:.2f}\n\n"
            
            # Show top 5 bids (highest to lowest)
            display_text += "BIDS (Buy Orders):\n"
            for i, bid in enumerate(order_book.bids[:5]):
                display_text += f"  {i+1}. ₹{bid.price:.2f} x {bid.quantity:,}\n"
            
            # Update GUI in main thread
            self.root.after(0, self._update_text, display_text)
            
        except Exception as e:
            logger.error(f"Display update error: {e}")
    
    def _update_text(self, text):
        """Update text area (must be called from main thread)"""
        try:
            self.text_area.insert(tk.END, text)
            self.text_area.see(tk.END)
            
            # Keep only last 100 lines
            lines = self.text_area.get("1.0", tk.END).split('\n')
            if len(lines) > 100:
                self.text_area.delete("1.0", f"{len(lines)-100}.0")
                
        except Exception as e:
            logger.error(f"Text update error: {e}")
    
    def update_status(self, status):
        """Update status label"""
        self.root.after(0, lambda: self.status_label.config(text=status))
    
    def run(self):
        """Start GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
    
    def on_closing(self):
        """Handle window closing"""
        self.running = False
        self.root.destroy()


class TestRealTimeSystem:
    """Test system with simulated data"""
    
    def __init__(self, symbols=None):
        if symbols is None:
            symbols = ["RELIANCE", "TCS", "INFY"]
        
        self.symbols = symbols
        self.analyzers = {symbol: AdvancedOrderFlowAnalyzer(symbol) for symbol in symbols}
        
        # GUI
        self.gui = TestOrderBookGUI(symbols)
        
        # Data tracking
        self.tick_count = 0
        self.signal_count = 0
        self.order_book_count = 0
        
        # Base prices for simulation
        self.base_prices = {
            "RELIANCE": 2500.0,
            "TCS": 3500.0,
            "INFY": 1800.0,
            "HDFCBANK": 1600.0
        }
        
        self.current_prices = self.base_prices.copy()
        
        # System state
        self.running = False
    
    def start_system(self):
        """Start the test system"""
        logger.info("🚀 Starting Test Real-Time System...")
        self.running = True
        
        # Start GUI in separate thread
        gui_thread = threading.Thread(target=self.gui.run, daemon=True)
        gui_thread.start()
        
        # Start data simulation
        data_thread = threading.Thread(target=self.simulate_data, daemon=True)
        data_thread.start()
        
        # Start monitoring
        monitor_thread = threading.Thread(target=self.monitor_system, daemon=True)
        monitor_thread.start()
        
        return True
    
    def simulate_data(self):
        """Simulate real-time market data"""
        logger.info("📊 Starting data simulation...")
        
        while self.running and self.gui.running:
            try:
                for symbol in self.symbols:
                    # Generate tick data
                    tick = self.generate_tick(symbol)
                    self.handle_tick(symbol, tick)
                    
                    # Generate order book every 5 ticks
                    if self.tick_count % 5 == 0:
                        order_book = self.generate_order_book(symbol)
                        self.handle_order_book(symbol, order_book)
                
                # Update every second
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Data simulation error: {e}")
                time.sleep(1)
    
    def generate_tick(self, symbol):
        """Generate simulated tick data"""
        # Random price movement
        base_price = self.base_prices[symbol]
        price_change = random.gauss(0, base_price * 0.001)  # 0.1% volatility
        
        self.current_prices[symbol] += price_change
        
        # Keep price within reasonable bounds
        self.current_prices[symbol] = max(
            self.current_prices[symbol], 
            base_price * 0.95
        )
        self.current_prices[symbol] = min(
            self.current_prices[symbol], 
            base_price * 1.05
        )
        
        # Generate volume
        volume = random.randint(100, 10000)
        
        # Occasionally generate large orders
        if random.random() < 0.1:  # 10% chance
            volume = random.randint(50000, 200000)
        
        return Tick(
            timestamp=datetime.now(),
            price=round(self.current_prices[symbol], 2),
            volume=volume,
            buyer_initiated=random.choice([True, False])
        )
    
    def generate_order_book(self, symbol):
        """Generate simulated order book"""
        current_price = self.current_prices[symbol]
        
        bids = []
        asks = []
        
        # Generate bid levels
        for i in range(10):
            price = current_price - (i + 1) * 0.05
            quantity = random.randint(1000, 20000)
            orders = random.randint(1, 10)
            bids.append(OrderBookLevel(price=price, quantity=quantity, orders=orders))
        
        # Generate ask levels
        for i in range(10):
            price = current_price + (i + 1) * 0.05
            quantity = random.randint(1000, 20000)
            orders = random.randint(1, 10)
            asks.append(OrderBookLevel(price=price, quantity=quantity, orders=orders))
        
        return OrderBook(
            timestamp=datetime.now(),
            bids=bids,
            asks=asks
        )
    
    def handle_tick(self, symbol, tick):
        """Handle tick data"""
        self.tick_count += 1
        
        # Process through order flow engine
        if symbol in self.analyzers:
            signal = self.analyzers[symbol].flow_engine.add_tick(tick)
            
            if signal:
                self.signal_count += 1
                self.handle_signal(symbol, signal, tick)
    
    def handle_order_book(self, symbol, order_book):
        """Handle order book data"""
        self.order_book_count += 1
        
        # Update GUI
        self.gui.update_display(symbol, order_book)
        
        # Process through order flow engine
        if symbol in self.analyzers:
            self.analyzers[symbol].flow_engine.add_order_book(order_book)
    
    def handle_signal(self, symbol, signal, tick):
        """Handle trading signal"""
        logger.info(f"🚨 SIGNAL #{self.signal_count}: {symbol} - {signal.signal_type} "
                   f"(Strength: {signal.strength:.2f}, Confidence: {signal.confidence:.2f})")
        
        # Print to console
        print(f"\n{'='*50}")
        print(f"📊 ORDER FLOW SIGNAL - {symbol}")
        print(f"{'='*50}")
        print(f"Time: {tick.timestamp.strftime('%H:%M:%S')}")
        print(f"Signal: {signal.signal_type}")
        print(f"Strength: {signal.strength:.2f}")
        print(f"Confidence: {signal.confidence:.2f}")
        print(f"Price: ₹{tick.price:.2f}")
        print(f"Volume: {tick.volume:,}")
        print(f"Reasons: {', '.join(signal.reasons)}")
        print(f"{'='*50}\n")
    
    def monitor_system(self):
        """Monitor system performance"""
        start_time = time.time()
        
        while self.running and self.gui.running:
            try:
                elapsed = time.time() - start_time
                
                # Update status
                status = (f"Running: {elapsed:.0f}s | "
                         f"Ticks: {self.tick_count} | "
                         f"Signals: {self.signal_count} | "
                         f"Order Books: {self.order_book_count}")
                
                self.gui.update_status(status)
                
                # Log every 30 seconds
                if int(elapsed) % 30 == 0:
                    tps = self.tick_count / elapsed if elapsed > 0 else 0
                    logger.info(f"📊 Performance: {tps:.1f} ticks/sec, "
                               f"{self.signal_count} signals, "
                               f"{self.order_book_count} order books")
                
                time.sleep(5)
                
            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                time.sleep(5)
    
    def stop_system(self):
        """Stop the system"""
        logger.info("🛑 Stopping test system...")
        self.running = False


def main():
    """Main test function"""
    print("🧪 TESTING COMPLETE REAL-TIME SYSTEM")
    print("=" * 60)
    print("This test simulates:")
    print("✅ Every second tick updates")
    print("✅ Real-time order book display")
    print("✅ Order flow analysis and signals")
    print("✅ GUI with live updates")
    print("=" * 60)
    
    try:
        # Create test system
        symbols = ["RELIANCE", "TCS", "INFY"]
        system = TestRealTimeSystem(symbols)
        
        print(f"\n📊 Testing with symbols: {', '.join(symbols)}")
        print("🔄 Starting test system... (Close GUI window to stop)")
        
        # Start system
        if system.start_system():
            # Keep main thread alive until GUI closes
            while system.gui.running:
                time.sleep(1)
        
        system.stop_system()
        
        print("\n✅ Test completed successfully!")
        print(f"📊 Final Stats:")
        print(f"  Ticks Processed: {system.tick_count}")
        print(f"  Signals Generated: {system.signal_count}")
        print(f"  Order Books: {system.order_book_count}")
        
    except KeyboardInterrupt:
        print("\n⏹️ Test stopped by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        logger.error(f"Test failure: {e}")


if __name__ == "__main__":
    main()
