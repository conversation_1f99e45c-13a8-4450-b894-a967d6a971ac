"""
Test script to verify depth chart is updating properly
Tests dynamic updates with visual feedback
"""

import tkinter as tk
import random
import time
from depth_chart_ui import DepthChartUI

def test_depth_chart_updates():
    """Test depth chart updates with dynamic data"""
    print("🧪 Testing DEPTH CHART UPDATES...")
    
    # Create test window
    root = tk.Tk()
    root.title("Depth Chart Update Test")
    root.geometry("800x600")
    root.configure(bg='#0a0a0a')
    
    # Create main container
    main_frame = tk.Frame(root, bg='#0a0a0a')
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # Title
    title_label = tk.Label(main_frame, text="📊 DEPTH CHART UPDATE TEST", 
                          font=('Consolas', 16, 'bold'),
                          bg='#0a0a0a', fg='#00ff88')
    title_label.pack(pady=(0, 10))
    
    # Status
    status_label = tk.Label(main_frame, text="Testing: Chart should update dynamically with changing data", 
                           font=('Consolas', 12),
                           bg='#0a0a0a', fg='#ffaa00')
    status_label.pack(pady=(0, 10))
    
    # Depth Chart Container
    chart_container = tk.Frame(main_frame, bg='#0a0a0a')
    chart_container.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
    
    # Initialize DepthChartUI
    depth_chart_ui = DepthChartUI(chart_container)
    
    # Test data generation with varying patterns
    base_price = 186.50
    update_count = 0
    pattern = 0  # 0=growing, 1=shrinking, 2=volatile
    
    def generate_dynamic_data():
        """Generate data with different patterns to test visual updates"""
        nonlocal update_count, pattern, base_price
        update_count += 1
        
        # Change pattern every 20 updates
        if update_count % 20 == 0:
            pattern = (pattern + 1) % 3
            print(f"🔄 Switching to pattern {pattern}")
        
        # Apply different patterns
        if pattern == 0:  # Growing quantities
            qty_multiplier = 1 + (update_count % 20) * 0.1
        elif pattern == 1:  # Shrinking quantities
            qty_multiplier = 2 - (update_count % 20) * 0.05
        else:  # Volatile quantities
            qty_multiplier = 1 + random.uniform(-0.5, 0.5)
        
        # Small price variations
        price_variation = random.uniform(-0.20, 0.20)
        current_base = base_price + price_variation
        
        # Generate 10 bid levels (for depth chart)
        bids = []
        for i in range(10):
            price = current_base - (0.05 * (i + 1))
            # Dynamic quantities based on pattern
            base_qty = 1000 * qty_multiplier
            qty = int(base_qty + random.randint(-200, 200))
            orders = random.randint(5, 20)
            
            # Mark top 5 as DEPTH for highlighting
            source = 'DEPTH' if i < 5 else 'LTP_GENERATED'
            bids.append([qty, orders, price, source])
        
        # Generate 10 ask levels (for depth chart)
        asks = []
        for i in range(10):
            price = current_base + (0.05 * (i + 1))
            # Dynamic quantities based on pattern
            base_qty = 1000 * qty_multiplier
            qty = int(base_qty + random.randint(-200, 200))
            orders = random.randint(5, 20)
            
            # Mark top 5 as DEPTH for highlighting
            source = 'DEPTH' if i < 5 else 'LTP_GENERATED'
            asks.append([price, orders, qty, source])
        
        return bids, asks
    
    # Dynamic update function
    def dynamic_update():
        """Update chart with dynamic data patterns"""
        try:
            bids, asks = generate_dynamic_data()
            
            # Update chart
            depth_chart_ui.update_chart(bids, asks)
            
            # Update status with pattern info
            pattern_names = ["Growing", "Shrinking", "Volatile"]
            status_label.config(text=f"Update #{update_count} - Pattern: {pattern_names[pattern]} - Chart should be updating!")
            
        except Exception as e:
            print(f"❌ Error in dynamic update: {e}")
            import traceback
            traceback.print_exc()
        
        # Schedule next update (every 500ms for visible changes)
        root.after(500, dynamic_update)
    
    # Control buttons
    control_frame = tk.Frame(main_frame, bg='#0a0a0a')
    control_frame.pack(fill=tk.X, pady=(10, 0))
    
    def start_test():
        """Start dynamic test"""
        print("🚀 Starting depth chart update test")
        dynamic_update()
    
    def stop_test():
        """Stop the test"""
        root.quit()
    
    def clear_chart():
        """Clear the chart"""
        depth_chart_ui.clear_chart()
    
    start_btn = tk.Button(control_frame, text="Start Dynamic Test", 
                         command=start_test,
                         bg='#00ff88', fg='black', font=('Consolas', 10, 'bold'))
    start_btn.pack(side=tk.LEFT, padx=(0, 10))
    
    clear_btn = tk.Button(control_frame, text="Clear Chart", 
                         command=clear_chart,
                         bg='#ffaa00', fg='black', font=('Consolas', 10, 'bold'))
    clear_btn.pack(side=tk.LEFT, padx=(0, 10))
    
    stop_btn = tk.Button(control_frame, text="Stop Test", 
                        command=stop_test,
                        bg='#ff0844', fg='white', font=('Consolas', 10, 'bold'))
    stop_btn.pack(side=tk.LEFT, padx=(0, 10))
    
    # Instructions
    instructions = tk.Label(control_frame, 
                           text="Watch for: Dynamic shapes, timestamp updates, blinking indicator", 
                           font=('Consolas', 10),
                           bg='#0a0a0a', fg='#888888')
    instructions.pack(side=tk.RIGHT)
    
    print("✅ Depth chart update test ready")
    print("📊 Features being tested:")
    print("   - Dynamic shape changes")
    print("   - Timestamp updates")
    print("   - Visual indicators")
    print("   - Pattern variations (growing/shrinking/volatile)")
    print("   - Enhanced visuals with outlines")
    print("   - Quantity scale and labels")
    print("\n🎯 Expected result: Chart should update dynamically with visible changes")
    
    # Run the test
    root.mainloop()

if __name__ == "__main__":
    test_depth_chart_updates()
