#!/usr/bin/env python3
"""
Test script to verify the Smart API fix
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_smart_api_initialization():
    """Test the Smart API initialization function"""
    print("🧪 Testing Smart API initialization fix...")
    
    try:
        # Import the main app
        from main_app import TradingAnalysisApp
        
        # Create a minimal instance just for testing
        class TestApp:
            def __init__(self):
                self.api_credentials = {
                    'api_key': 'xuyTns9P',
                    'client_code': 'AAAN362675',
                    'password': '4180',
                    'totp_secret': 'TU6ZEIE7ROJBSES7MYJ5YVRJE4'
                }
                self.smart_api = None
                self.cgcl_token = None
                self.previous_close_price = None
        
        # Create test instance
        test_app = TestApp()
        
        # Import the initialize_smart_api method
        app = TradingAnalysisApp.__new__(TradingAnalysisApp)
        app.api_credentials = test_app.api_credentials
        app.smart_api = None
        app.cgcl_token = None
        app.previous_close_price = None
        
        # Test the initialization
        print("🔧 Testing initialize_smart_api() function...")
        result = app.initialize_smart_api()
        
        if result:
            print("✅ Smart API initialization returned True - FIX SUCCESSFUL!")
            print(f"📊 CGCL Token: {app.cgcl_token}")
            print(f"💰 Previous Close: ₹{app.previous_close_price}")
            return True
        else:
            print("❌ Smart API initialization returned False - FIX FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Smart API Fix Test")
    print("=" * 50)
    
    success = test_smart_api_initialization()
    
    print("=" * 50)
    if success:
        print("🎉 API FIX VERIFICATION: SUCCESS!")
        print("✅ The WebSocket connection should now work properly")
    else:
        print("💥 API FIX VERIFICATION: FAILED!")
        print("❌ Additional debugging may be needed")
