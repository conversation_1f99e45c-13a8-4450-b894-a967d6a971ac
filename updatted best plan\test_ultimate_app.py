"""
Test the Ultimate Trading Analysis app with simulation
"""

import sys
import os
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ultimate_trading_analysis_v1 import UltimateTradingAnalysisV1


def test_app():
    """Test the application"""
    print("🚀 Testing Ultimate Trading Analysis v1 with Simulation")
    print("="*60)
    
    try:
        # Create the app
        app = UltimateTradingAnalysisV1()
        
        print("✅ Application created successfully")
        print("📊 Simulation should start automatically")
        print("🎯 CGCL order book data updating every second")
        print("🔄 Use the Live/Sim buttons to switch data sources")
        print("🔌 Use Connect/Disconnect to control data flow")
        print("\nGUI should be visible now. Close the window to exit.")
        
        # Run the application
        app.run()
        
    except KeyboardInterrupt:
        print("\n⏹️ Application stopped by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_app()
