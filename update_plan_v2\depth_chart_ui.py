"""
Professional Step Bar Depth Chart UI Module
Clean, seamless step bar chart implementation for market depth visualization
"""

import tkinter as tk
import time


class DepthChartUI:
    """Clean step bar depth chart with seamless joining"""

    def __init__(self, parent_frame):
        """Initialize clean step bar depth chart"""
        self.parent_frame = parent_frame
        self.chart_frame = None
        self.canvas = None

        # Clean professional styling
        self.bg_color = '#1a1a1a'  # Dark background
        self.text_color = '#ffffff'
        self.bid_color = '#00ff88'  # Bright green for bids
        self.ask_color = '#ff0844'  # Bright red for asks
        self.grid_color = '#333333'
        self.mid_line_color = '#ffaa00'

        # Enhanced chart dimensions for better visibility and no text overlap
        self.chart_width = 900
        self.chart_height = 380
        self.margin_top = 60
        self.margin_bottom = 80
        self.margin_left = 100
        self.margin_right = 100

        # Data storage
        self.current_bids = []
        self.current_asks = []
        self.mid_price = 0

        # Level control modes
        self.depth_levels = 10  # Current number of levels to display
        self.available_levels = [5, 10, 15, 20]  # Available level options

        # UI components
        self.mid_price_label = None

        # Create enhanced layout with analysis
        self.create_enhanced_layout()

        print("✅ Enhanced Trading Analysis DepthChartUI initialized")

    def create_enhanced_layout(self):
        """Create enhanced layout with trading analysis and level controls"""
        # Main chart frame (increased height significantly for no text overlap)
        self.chart_frame = tk.Frame(self.parent_frame, bg=self.bg_color, height=520)
        self.chart_frame.pack(fill=tk.X, padx=2, pady=2)
        self.chart_frame.pack_propagate(False)

        # Enhanced header with analysis metrics
        self.create_enhanced_header()

        # Level control panel
        self.create_level_control_panel()

        # Enhanced step bar chart canvas with axis labels
        self.create_enhanced_chart_canvas()

        # Analysis panel
        self.create_analysis_panel()

    def create_enhanced_header(self):
        """Create enhanced header with trading analysis metrics"""
        # Main header frame
        header_frame = tk.Frame(self.chart_frame, bg=self.bg_color, height=60)
        header_frame.pack(fill=tk.X, pady=(5, 0))
        header_frame.pack_propagate(False)

        # Top row - Title and key metrics
        top_row = tk.Frame(header_frame, bg=self.bg_color)
        top_row.pack(fill=tk.X, pady=(5, 0))

        # Title
        title_label = tk.Label(top_row, text="DEPTH CHART",
                             font=('Consolas', 12, 'bold'),
                             bg=self.bg_color, fg=self.text_color)
        title_label.pack(side=tk.LEFT, padx=(10, 0))

        # Key metrics container
        metrics_frame = tk.Frame(top_row, bg=self.bg_color)
        metrics_frame.pack(side=tk.RIGHT, padx=(0, 10))

        # Mid price display
        self.mid_price_label = tk.Label(metrics_frame, text="₹0.00",
                                       font=('Consolas', 14, 'bold'),
                                       bg=self.bg_color, fg=self.mid_line_color)
        self.mid_price_label.pack(side=tk.LEFT, padx=(0, 15))

        # Spread display
        self.spread_label = tk.Label(metrics_frame, text="Spread: ₹0.00",
                                    font=('Consolas', 10),
                                    bg=self.bg_color, fg='#888888')
        self.spread_label.pack(side=tk.LEFT, padx=(0, 15))

        # Imbalance indicator
        self.imbalance_label = tk.Label(metrics_frame, text="Imbalance: 0%",
                                       font=('Consolas', 10),
                                       bg=self.bg_color, fg='#888888')
        self.imbalance_label.pack(side=tk.LEFT)

        # Bottom row - Analysis metrics
        bottom_row = tk.Frame(header_frame, bg=self.bg_color)
        bottom_row.pack(fill=tk.X, pady=(2, 0))

        # Analysis metrics
        analysis_frame = tk.Frame(bottom_row, bg=self.bg_color)
        analysis_frame.pack(side=tk.LEFT, padx=(10, 0))

        # Support/Resistance levels
        self.support_label = tk.Label(analysis_frame, text="Support: ₹0.00",
                                     font=('Consolas', 9),
                                     bg=self.bg_color, fg=self.bid_color)
        self.support_label.pack(side=tk.LEFT, padx=(0, 15))

        self.resistance_label = tk.Label(analysis_frame, text="Resistance: ₹0.00",
                                        font=('Consolas', 9),
                                        bg=self.bg_color, fg=self.ask_color)
        self.resistance_label.pack(side=tk.LEFT, padx=(0, 15))

        # Volume metrics
        volume_frame = tk.Frame(bottom_row, bg=self.bg_color)
        volume_frame.pack(side=tk.RIGHT, padx=(0, 10))

        self.volume_ratio_label = tk.Label(volume_frame, text="Vol Ratio: 1.00",
                                          font=('Consolas', 9),
                                          bg=self.bg_color, fg='#888888')
        self.volume_ratio_label.pack(side=tk.LEFT, padx=(0, 10))

        self.depth_strength_label = tk.Label(volume_frame, text="Depth: Strong",
                                            font=('Consolas', 9),
                                            bg=self.bg_color, fg='#888888')
        self.depth_strength_label.pack(side=tk.LEFT)

    def create_level_control_panel(self):
        """Create level control panel for selecting depth levels"""
        control_frame = tk.Frame(self.chart_frame, bg=self.bg_color, height=35)
        control_frame.pack(fill=tk.X, pady=(5, 0))
        control_frame.pack_propagate(False)

        # Left side - Level controls
        left_controls = tk.Frame(control_frame, bg=self.bg_color)
        left_controls.pack(side=tk.LEFT, padx=(10, 0), pady=5)

        # Level selection label
        level_label = tk.Label(left_controls, text="📊 Levels:",
                             font=('Consolas', 10, 'bold'),
                             bg=self.bg_color, fg='#aaaaaa')
        level_label.pack(side=tk.LEFT, padx=(0, 5))

        # Level dropdown selector (like Order Flow Analysis)
        import customtkinter as ctk
        self.level_selector = ctk.CTkOptionMenu(
            left_controls,
            values=["5", "10", "15", "20"],
            command=self.on_level_change,
            width=60,
            height=28,
            font=ctk.CTkFont(family="Segoe UI", size=10, weight="bold"),
            dropdown_font=ctk.CTkFont(family="Segoe UI", size=10),
            fg_color="#333333",
            button_color="#444444",
            button_hover_color="#555555",
            dropdown_fg_color="#333333",
            dropdown_hover_color="#444444",
            text_color="#ffffff",
            dropdown_text_color="#ffffff"
        )
        self.level_selector.set(str(self.depth_levels))  # Set current level
        self.level_selector.pack(side=tk.LEFT, padx=(0, 10))

        # Right side - Mode info
        right_info = tk.Frame(control_frame, bg=self.bg_color)
        right_info.pack(side=tk.RIGHT, padx=(0, 10), pady=5)

        # Current mode display
        self.mode_info_label = tk.Label(right_info, text=f"Showing: {self.depth_levels} levels",
                                       font=('Consolas', 9),
                                       bg=self.bg_color, fg='#888888')
        self.mode_info_label.pack(side=tk.RIGHT)

    def create_enhanced_chart_canvas(self):
        """Create enhanced chart canvas with larger dimensions and no text overlap"""
        # Chart container with fixed height
        chart_container = tk.Frame(self.chart_frame, bg=self.bg_color, height=420)
        chart_container.pack(fill=tk.X, padx=5, pady=(0, 5))
        chart_container.pack_propagate(False)

        # Create canvas for enhanced chart with larger dimensions
        self.canvas = tk.Canvas(chart_container,
                              width=self.chart_width,
                              height=self.chart_height,
                              bg=self.bg_color,
                              highlightthickness=0,
                              bd=0)
        self.canvas.pack(anchor='center', pady=15)

        # Draw initial empty chart
        self.draw_empty_chart()

    def create_analysis_panel(self):
        """Create bottom analysis panel with key metrics"""
        analysis_frame = tk.Frame(self.chart_frame, bg=self.bg_color, height=40)
        analysis_frame.pack(fill=tk.X, pady=(0, 5))
        analysis_frame.pack_propagate(False)

        # Left side - Order flow analysis
        left_panel = tk.Frame(analysis_frame, bg=self.bg_color)
        left_panel.pack(side=tk.LEFT, padx=(10, 0), pady=5)

        # Order flow strength
        self.flow_strength_label = tk.Label(left_panel, text="Flow: Neutral",
                                           font=('Consolas', 9, 'bold'),
                                           bg=self.bg_color, fg='#888888')
        self.flow_strength_label.pack(side=tk.LEFT, padx=(0, 15))

        # Market pressure
        self.pressure_label = tk.Label(left_panel, text="Pressure: Balanced",
                                      font=('Consolas', 9),
                                      bg=self.bg_color, fg='#888888')
        self.pressure_label.pack(side=tk.LEFT, padx=(0, 15))

        # Right side - Volume analysis
        right_panel = tk.Frame(analysis_frame, bg=self.bg_color)
        right_panel.pack(side=tk.RIGHT, padx=(0, 10), pady=5)

        # Total volume
        self.total_volume_label = tk.Label(right_panel, text="Total Vol: 0",
                                          font=('Consolas', 9),
                                          bg=self.bg_color, fg='#888888')
        self.total_volume_label.pack(side=tk.LEFT, padx=(0, 15))

        # Average order size
        self.avg_order_label = tk.Label(right_panel, text="Avg Order: 0",
                                       font=('Consolas', 9),
                                       bg=self.bg_color, fg='#888888')
        self.avg_order_label.pack(side=tk.LEFT)
    
    def update_chart(self, bids, asks):
        """Update enhanced depth chart with analysis metrics"""
        # Use the selected depth levels
        new_bids = bids[:self.depth_levels] if bids else []
        new_asks = asks[:self.depth_levels] if asks else []

        self.current_bids = new_bids
        self.current_asks = new_asks

        # Calculate and update all metrics
        self.calculate_and_update_metrics()

        # Redraw enhanced chart
        self.draw_enhanced_step_chart()

    def on_level_change(self, selected_level):
        """Handle level selection change from dropdown"""
        try:
            levels = int(selected_level)
            self.set_depth_levels(levels)
        except ValueError:
            print(f"⚠️ [DEPTH_CHART] Invalid level selection: {selected_level}")

    def set_depth_levels(self, levels):
        """Set the number of depth levels to display"""
        self.depth_levels = levels

        # Update dropdown selector
        if hasattr(self, 'level_selector'):
            self.level_selector.set(str(levels))

        # Update mode info
        self.mode_info_label.config(text=f"Showing: {levels} levels")

        print(f"📊 [DEPTH_CHART] Depth levels changed to: {levels}")

        # Redraw chart with new level count
        if self.current_bids or self.current_asks:
            # Re-filter data with new level count
            self.current_bids = self.current_bids[:levels] if self.current_bids else []
            self.current_asks = self.current_asks[:levels] if self.current_asks else []
            self.draw_enhanced_step_chart()

    def set_chart_mode(self, mode):
        """Set chart mode (full/lite) - now maps to level counts"""
        if mode == "lite":
            self.set_depth_levels(5)
        else:  # full mode
            self.set_depth_levels(20)

        print(f"📊 [DEPTH_CHART] Mode changed to: {mode} ({self.depth_levels} levels)")

    def calculate_and_update_metrics(self):
        """Calculate and update all trading analysis metrics with improved accuracy"""
        if not self.current_bids or not self.current_asks:
            self.reset_metrics_to_default()
            return

        try:
            # Robust price extraction with validation
            best_bid = self.extract_price_safely(self.current_bids[0], 'bid')
            best_ask = self.extract_price_safely(self.current_asks[0], 'ask')

            if best_bid <= 0 or best_ask <= 0 or best_ask <= best_bid:
                self.reset_metrics_to_default()
                return

            # Core price metrics
            self.mid_price = (best_bid + best_ask) / 2
            self.mid_price_label.config(text=f"₹{self.mid_price:.2f}")

            # Enhanced spread analysis
            spread = best_ask - best_bid
            spread_pct = (spread / self.mid_price) * 100 if self.mid_price > 0 else 0
            self.spread_label.config(text=f"Spread: ₹{spread:.2f} ({spread_pct:.3f}%)")

            # Robust volume calculations
            total_bid_vol = self.calculate_total_volume(self.current_bids, 'bid')
            total_ask_vol = self.calculate_total_volume(self.current_asks, 'ask')
            total_volume = total_bid_vol + total_ask_vol

            if total_volume <= 0:
                self.reset_metrics_to_default()
                return

            # Enhanced imbalance calculation
            bid_ratio = total_bid_vol / total_volume
            imbalance = (bid_ratio - 0.5) * 200  # Convert to percentage
            imbalance_color = self.bid_color if imbalance > 5 else self.ask_color if imbalance < -5 else '#888888'
            self.imbalance_label.config(text=f"Imbalance: {imbalance:+.1f}%", fg=imbalance_color)

            # Volume ratio with safety check
            vol_ratio = total_bid_vol / total_ask_vol if total_ask_vol > 0 else float('inf')
            vol_ratio_text = f"{vol_ratio:.2f}" if vol_ratio != float('inf') else "∞"
            self.volume_ratio_label.config(text=f"Vol Ratio: {vol_ratio_text}")

            # Intelligent support and resistance levels
            price_range = max(best_ask - best_bid, self.mid_price * 0.001)  # Min 0.1% range
            support_level = best_bid - (price_range * 1.5)
            resistance_level = best_ask + (price_range * 1.5)
            self.support_label.config(text=f"Support: ₹{support_level:.2f}")
            self.resistance_label.config(text=f"Resistance: ₹{resistance_level:.2f}")

            # Enhanced depth strength analysis
            depth_strength, depth_color = self.analyze_depth_strength(total_volume, self.mid_price)
            self.depth_strength_label.config(text=f"Depth: {depth_strength}", fg=depth_color)

            # Improved order flow analysis
            flow_strength, flow_color = self.analyze_order_flow(self.current_bids, self.current_asks, imbalance)
            self.flow_strength_label.config(text=f"Flow: {flow_strength}", fg=flow_color)

            # Enhanced market pressure analysis
            pressure, pressure_color = self.analyze_market_pressure(imbalance, vol_ratio, spread_pct)
            self.pressure_label.config(text=f"Pressure: {pressure}", fg=pressure_color)

            # Volume metrics
            self.total_volume_label.config(text=f"Total Vol: {total_volume:,.0f}")
            avg_order = self.calculate_average_order_size(self.current_bids, self.current_asks)
            self.avg_order_label.config(text=f"Avg Order: {avg_order:,.0f}")

        except Exception as e:
            print(f"⚠️ [DEPTH_CHART] Error calculating metrics: {e}")
            self.reset_metrics_to_default()

    def extract_price_safely(self, level, side):
        """Safely extract price from bid/ask level"""
        try:
            if side == 'bid':
                return float(level[2]) if len(level) > 2 else 0
            else:  # ask
                return float(level[0]) if len(level) > 0 else 0
        except (IndexError, ValueError, TypeError):
            return 0

    def calculate_total_volume(self, levels, side):
        """Calculate total volume with error handling"""
        total = 0
        try:
            for level in levels:
                if side == 'bid':
                    qty = float(level[0]) if len(level) > 0 else 0
                else:  # ask
                    qty = float(level[2]) if len(level) > 2 else 0
                total += max(0, qty)  # Ensure non-negative
        except (IndexError, ValueError, TypeError):
            pass
        return total

    def analyze_depth_strength(self, total_volume, mid_price):
        """Analyze market depth strength based on volume and price"""
        # Adaptive thresholds based on price level
        if mid_price > 1000:
            strong_threshold = 200000
            moderate_threshold = 100000
        elif mid_price > 100:
            strong_threshold = 100000
            moderate_threshold = 50000
        else:
            strong_threshold = 50000
            moderate_threshold = 25000

        if total_volume >= strong_threshold:
            return "Strong", '#00ff88'
        elif total_volume >= moderate_threshold:
            return "Moderate", '#ffaa00'
        else:
            return "Weak", '#ff0844'

    def analyze_order_flow(self, bids, asks, imbalance):
        """Analyze order flow with multiple factors"""
        try:
            # Volume-weighted analysis
            bid_weighted = sum(bid[0] * bid[2] for bid in bids[:5] if len(bid) > 2) / sum(bid[0] for bid in bids[:5] if len(bid) > 0)
            ask_weighted = sum(ask[2] * ask[0] for ask in asks[:5] if len(ask) > 2) / sum(ask[2] for ask in asks[:5] if len(ask) > 2)

            # Combined analysis
            if imbalance > 15 and bid_weighted > ask_weighted * 1.1:
                return "Strong Bull", '#00ff88'
            elif imbalance > 5:
                return "Bullish", '#00cc66'
            elif imbalance < -15 and ask_weighted > bid_weighted * 1.1:
                return "Strong Bear", '#ff0844'
            elif imbalance < -5:
                return "Bearish", '#cc3366'
            else:
                return "Neutral", '#888888'
        except:
            return "Neutral", '#888888'

    def analyze_market_pressure(self, imbalance, vol_ratio, spread_pct):
        """Analyze market pressure with multiple indicators"""
        # Tight spread indicates active market
        tight_spread = spread_pct < 0.1

        if imbalance > 20 or (imbalance > 10 and vol_ratio > 2):
            return "Strong Buy", '#00ff88'
        elif imbalance > 10 or (imbalance > 5 and tight_spread):
            return "Buy Pressure", '#00cc66'
        elif imbalance < -20 or (imbalance < -10 and vol_ratio < 0.5):
            return "Strong Sell", '#ff0844'
        elif imbalance < -10 or (imbalance < -5 and tight_spread):
            return "Sell Pressure", '#cc3366'
        else:
            return "Balanced", '#888888'

    def calculate_average_order_size(self, bids, asks):
        """Calculate average order size across all levels"""
        try:
            total_orders = len(bids) + len(asks)
            total_volume = self.calculate_total_volume(bids, 'bid') + self.calculate_total_volume(asks, 'ask')
            return total_volume / total_orders if total_orders > 0 else 0
        except:
            return 0

    def reset_metrics_to_default(self):
        """Reset all metrics to default values"""
        self.mid_price_label.config(text="₹0.00")
        self.spread_label.config(text="Spread: ₹0.00 (0.00%)")
        self.imbalance_label.config(text="Imbalance: 0.0%", fg='#888888')
        self.support_label.config(text="Support: ₹0.00")
        self.resistance_label.config(text="Resistance: ₹0.00")
        self.volume_ratio_label.config(text="Vol Ratio: 0.00")
        self.depth_strength_label.config(text="Depth: -", fg='#888888')
        self.flow_strength_label.config(text="Flow: Neutral", fg='#888888')
        self.pressure_label.config(text="Pressure: Balanced", fg='#888888')
        self.total_volume_label.config(text="Total Vol: 0")
        self.avg_order_label.config(text="Avg Order: 0")
    
    def draw_empty_chart(self):
        """Draw empty step bar chart with enhanced layout and no overlap"""
        self.canvas.delete("all")

        # Draw basic axes with larger margins
        chart_left = self.margin_left
        chart_right = self.chart_width - self.margin_right
        chart_top = self.margin_top
        chart_bottom = self.chart_height - self.margin_bottom
        center_x = self.chart_width // 2

        # Draw axes
        self.canvas.create_line(chart_left, chart_bottom, chart_right, chart_bottom,
                              fill=self.text_color, width=2)
        self.canvas.create_line(chart_left, chart_top, chart_left, chart_bottom,
                              fill=self.text_color, width=2)

        # Draw center line
        self.canvas.create_line(center_x, chart_top, center_x, chart_bottom,
                              fill=self.grid_color, width=1, dash=(3, 3))

        # Add placeholder text
        center_y = (chart_top + chart_bottom) // 2
        self.canvas.create_text(center_x, center_y,
                              text="Waiting for order book data...",
                              fill='#666666', font=('Consolas', 14),
                              anchor='center')

        # Draw axis labels with better positioning
        self.canvas.create_text(25, center_y,
                              text="Cumulative Quantity", fill=self.text_color,
                              font=('Consolas', 11, 'bold'), angle=90, anchor='center')

        self.canvas.create_text(center_x, chart_bottom + 50,
                              text="Price Levels", fill=self.text_color,
                              font=('Consolas', 11, 'bold'), anchor='center')

        # Draw side labels with proper spacing below X-axis labels
        self.canvas.create_text(self.margin_left + 80, chart_bottom + 40,
                              text="BIDS", fill=self.bid_color,
                              font=('Consolas', 11, 'bold'))

        self.canvas.create_text(self.chart_width - self.margin_right - 80, chart_bottom + 40,
                              text="ASKS", fill=self.ask_color,
                              font=('Consolas', 11, 'bold'))

    def draw_enhanced_step_chart(self):
        """Draw enhanced step bar chart with price levels and analysis"""
        if not self.current_bids and not self.current_asks:
            self.draw_empty_chart()
            return

        # Clear canvas
        self.canvas.delete("all")

        # Calculate cumulative data
        bid_cumulative = self.calculate_cumulative_bids()
        ask_cumulative = self.calculate_cumulative_asks()

        if not bid_cumulative and not ask_cumulative:
            self.draw_empty_chart()
            return

        # Draw enhanced background with axes and labels
        self.draw_enhanced_grid_and_axes()

        # Draw step bars with enhanced styling
        self.draw_enhanced_bid_bars(bid_cumulative)
        self.draw_enhanced_ask_bars(ask_cumulative)

        # Draw enhanced labels
        self.draw_enhanced_labels(bid_cumulative, ask_cumulative)

    def calculate_cumulative_bids(self):
        """Calculate cumulative bid quantities"""
        if not self.current_bids:
            return []

        cumulative = []
        total_qty = 0

        # Sort bids by price descending
        sorted_bids = sorted(self.current_bids, key=lambda x: x[2], reverse=True)

        for bid in sorted_bids:
            qty = bid[0]
            price = bid[2]
            total_qty += qty
            cumulative.append((price, total_qty))

        return cumulative

    def calculate_cumulative_asks(self):
        """Calculate cumulative ask quantities"""
        if not self.current_asks:
            return []

        cumulative = []
        total_qty = 0

        # Sort asks by price ascending
        sorted_asks = sorted(self.current_asks, key=lambda x: x[0])

        for ask in sorted_asks:
            price = ask[0]
            qty = ask[2]
            total_qty += qty
            cumulative.append((price, total_qty))

        return cumulative

    def draw_enhanced_grid_and_axes(self):
        """Draw enhanced grid with quantity and price axis labels - improved spacing"""
        # Calculate chart area with larger margins
        chart_left = self.margin_left
        chart_right = self.chart_width - self.margin_right
        chart_top = self.margin_top
        chart_bottom = self.chart_height - self.margin_bottom
        center_x = self.chart_width // 2

        # Draw horizontal grid lines for quantity levels
        max_qty = self.get_max_cumulative_quantity()
        if max_qty > 0:
            for i in range(6):
                y = chart_top + i * (chart_bottom - chart_top) / 5
                # Grid line
                self.canvas.create_line(chart_left, y, chart_right, y,
                                      fill='#333333', width=1, dash=(2, 2))

                # Y-axis quantity labels with better spacing
                qty_value = max_qty * (5 - i) / 5
                qty_text = self.format_quantity(qty_value)
                self.canvas.create_text(chart_left - 15, y,
                                      text=qty_text, fill=self.text_color,
                                      font=('Consolas', 10), anchor='e')

        # Draw vertical grid lines for price levels
        price_levels = self.get_price_levels()
        if price_levels:
            num_levels = len(price_levels)
            for i, price in enumerate(price_levels):
                x = chart_left + i * (chart_right - chart_left) / (num_levels - 1)
                # Grid line
                self.canvas.create_line(x, chart_top, x, chart_bottom,
                                      fill='#333333', width=1, dash=(2, 2))

                # X-axis price labels with better spacing
                self.canvas.create_text(x, chart_bottom + 20,
                                      text=f"₹{price:.1f}", fill=self.text_color,
                                      font=('Consolas', 9), anchor='center')

        # Draw main axes
        # Y-axis
        self.canvas.create_line(chart_left, chart_top, chart_left, chart_bottom,
                              fill=self.text_color, width=2)
        # X-axis
        self.canvas.create_line(chart_left, chart_bottom, chart_right, chart_bottom,
                              fill=self.text_color, width=2)

        # Axis labels with better positioning
        # Y-axis label (rotated)
        self.canvas.create_text(25, (chart_top + chart_bottom) // 2,
                              text="Cumulative Quantity", fill=self.text_color,
                              font=('Consolas', 11, 'bold'), angle=90, anchor='center')

        # X-axis label
        self.canvas.create_text(center_x, chart_bottom + 50,
                              text="Price Levels", fill=self.text_color,
                              font=('Consolas', 11, 'bold'), anchor='center')

        # Center line for mid-price
        self.canvas.create_line(center_x, chart_top, center_x, chart_bottom,
                              fill=self.mid_line_color, width=2, dash=(5, 3))

    def get_max_cumulative_quantity(self):
        """Get maximum cumulative quantity for Y-axis scaling"""
        max_qty = 0
        if self.current_bids:
            bid_cumulative = self.calculate_cumulative_bids()
            if bid_cumulative:
                max_qty = max(max_qty, max(qty for _, qty in bid_cumulative))

        if self.current_asks:
            ask_cumulative = self.calculate_cumulative_asks()
            if ask_cumulative:
                max_qty = max(max_qty, max(qty for _, qty in ask_cumulative))

        return max_qty

    def get_price_levels(self):
        """Get price levels for X-axis labels"""
        if not self.current_bids or not self.current_asks:
            return []

        # Get price range from current data
        bid_prices = [bid[2] for bid in self.current_bids if len(bid) > 2]
        ask_prices = [ask[0] for ask in self.current_asks if len(ask) > 0]

        if not bid_prices or not ask_prices:
            return []

        min_price = min(min(bid_prices), min(ask_prices))
        max_price = max(max(bid_prices), max(ask_prices))

        # Create 7 evenly spaced price levels
        price_range = max_price - min_price
        if price_range <= 0:
            return [min_price]

        return [min_price + i * price_range / 6 for i in range(7)]

    def format_quantity(self, qty):
        """Format quantity for display"""
        if qty >= 1000000:
            return f"{qty/1000000:.1f}M"
        elif qty >= 1000:
            return f"{qty/1000:.1f}K"
        else:
            return f"{qty:.0f}"

    def draw_enhanced_bid_bars(self, bid_cumulative):
        """Draw enhanced step bars for bids with better styling and scaling"""
        if not bid_cumulative:
            return

        # Use new margin system
        center_x = self.chart_width // 2
        chart_bottom = self.chart_height - self.margin_bottom
        chart_top = self.margin_top
        left_edge = self.margin_left

        # Find max quantity for proper scaling
        max_qty = self.get_max_cumulative_quantity()
        if max_qty <= 0:
            return

        bar_width = max(1, (center_x - left_edge) // len(bid_cumulative))

        # Draw enhanced step bars
        for i, (price, cum_qty) in enumerate(bid_cumulative):
            x1 = center_x - (i + 1) * bar_width
            x2 = center_x - i * bar_width

            height_ratio = cum_qty / max_qty
            bar_height = height_ratio * (chart_bottom - chart_top)
            y1 = chart_bottom
            y2 = chart_bottom - bar_height

            # Draw main bar with enhanced styling
            self.canvas.create_rectangle(x1, y1, x2, y2,
                                       fill=self.bid_color, outline='',
                                       width=0)

            # Add top border for better definition
            self.canvas.create_line(x1, y2, x2, y2,
                                  fill='#00ff88', width=2)

            # Draw connection lines for seamless appearance
            if i < len(bid_cumulative) - 1:
                next_qty = bid_cumulative[i + 1][1]
                next_height_ratio = next_qty / max_qty
                next_bar_height = next_height_ratio * (chart_bottom - chart_top)
                next_y2 = chart_bottom - next_bar_height

                self.canvas.create_line(x1, y2, x1, next_y2,
                                      fill=self.bid_color, width=2)

    def draw_enhanced_ask_bars(self, ask_cumulative):
        """Draw enhanced step bars for asks with better styling and scaling"""
        if not ask_cumulative:
            return

        # Use new margin system
        center_x = self.chart_width // 2
        chart_bottom = self.chart_height - self.margin_bottom
        chart_top = self.margin_top
        right_edge = self.chart_width - self.margin_right

        # Find max quantity for proper scaling
        max_qty = self.get_max_cumulative_quantity()
        if max_qty <= 0:
            return

        bar_width = max(1, (right_edge - center_x) // len(ask_cumulative))

        # Draw enhanced step bars
        for i, (price, cum_qty) in enumerate(ask_cumulative):
            x1 = center_x + i * bar_width
            x2 = center_x + (i + 1) * bar_width

            height_ratio = cum_qty / max_qty
            bar_height = height_ratio * (chart_bottom - chart_top)
            y1 = chart_bottom
            y2 = chart_bottom - bar_height

            # Draw main bar with enhanced styling
            self.canvas.create_rectangle(x1, y1, x2, y2,
                                       fill=self.ask_color, outline='',
                                       width=0)

            # Add top border for better definition
            self.canvas.create_line(x1, y2, x2, y2,
                                  fill='#ff0844', width=2)

            # Draw connection lines for seamless appearance
            if i < len(ask_cumulative) - 1:
                next_qty = ask_cumulative[i + 1][1]
                next_height_ratio = next_qty / max_qty
                next_bar_height = next_height_ratio * (chart_bottom - chart_top)
                next_y2 = chart_bottom - next_bar_height

                self.canvas.create_line(x2, y2, x2, next_y2,
                                      fill=self.ask_color, width=2)

    def draw_enhanced_center_line(self):
        """Draw enhanced center line with mid price indicator"""
        center_x = self.chart_width // 2
        chart_bottom = self.chart_height - self.margin_bottom
        chart_top = self.margin_top

        # Main center line
        self.canvas.create_line(center_x, chart_top,
                              center_x, chart_bottom,
                              fill=self.mid_line_color, width=3)

        # Mid price indicator circle
        mid_y = chart_top + (chart_bottom - chart_top) // 2
        self.canvas.create_oval(center_x - 6, mid_y - 6, center_x + 6, mid_y + 6,
                              fill=self.mid_line_color, outline='white', width=2)

    def draw_price_levels(self):
        """Draw support and resistance level indicators"""
        if not self.current_bids or not self.current_asks:
            return

        center_x = self.chart_width // 2
        chart_bottom = self.chart_height - self.margin_bottom

        # Draw support level (below current price)
        support_y = chart_bottom - 20
        self.canvas.create_line(self.margin_sides, support_y, center_x - 10, support_y,
                              fill=self.bid_color, width=2, dash=(5, 5))
        self.canvas.create_text(self.margin_sides + 5, support_y - 10,
                              text="S", fill=self.bid_color, font=('Consolas', 8, 'bold'),
                              anchor='w')

        # Draw resistance level (above current price)
        resistance_y = chart_bottom - 20
        self.canvas.create_line(center_x + 10, resistance_y, self.chart_width - self.margin_sides, resistance_y,
                              fill=self.ask_color, width=2, dash=(5, 5))
        self.canvas.create_text(self.chart_width - self.margin_sides - 5, resistance_y - 10,
                              text="R", fill=self.ask_color, font=('Consolas', 8, 'bold'),
                              anchor='e')

    def draw_enhanced_labels(self, bid_cumulative, ask_cumulative):
        """Draw enhanced labels with detailed information and proper spacing from X-axis"""
        chart_bottom = self.chart_height - self.margin_bottom
        center_x = self.chart_width // 2

        # Bid side labels (left side) with proper spacing below X-axis labels
        if bid_cumulative:
            bid_total = bid_cumulative[-1][1]
            best_bid_price = self.extract_price_safely(self.current_bids[0], 'bid') if self.current_bids else 0

            # Total volume label - moved down to avoid X-axis overlap
            self.canvas.create_text(self.margin_left + 80, chart_bottom + 40,
                                  text=f"BIDS: {self.format_quantity(bid_total)}",
                                  fill=self.bid_color,
                                  font=('Consolas', 11, 'bold'))

            # Best bid price - moved down accordingly
            if best_bid_price > 0:
                self.canvas.create_text(self.margin_left + 80, chart_bottom + 60,
                                      text=f"Best: ₹{best_bid_price:.2f}",
                                      fill=self.bid_color,
                                      font=('Consolas', 10))

        # Ask side labels (right side) with proper spacing below X-axis labels
        if ask_cumulative:
            ask_total = ask_cumulative[-1][1]
            best_ask_price = self.extract_price_safely(self.current_asks[0], 'ask') if self.current_asks else 0

            # Total volume label - moved down to avoid X-axis overlap
            self.canvas.create_text(self.chart_width - self.margin_right - 80, chart_bottom + 40,
                                  text=f"ASKS: {self.format_quantity(ask_total)}",
                                  fill=self.ask_color,
                                  font=('Consolas', 11, 'bold'))

            # Best ask price - moved down accordingly
            if best_ask_price > 0:
                self.canvas.create_text(self.chart_width - self.margin_right - 80, chart_bottom + 60,
                                      text=f"Best: ₹{best_ask_price:.2f}",
                                      fill=self.ask_color,
                                      font=('Consolas', 10))

        # Center mid-price label with enhanced styling and proper positioning below X-axis
        if self.mid_price > 0:
            # Background for mid price - moved down to avoid X-axis overlap
            self.canvas.create_rectangle(center_x - 50, chart_bottom + 35,
                                        center_x + 50, chart_bottom + 55,
                                        fill='#2c3e50', outline=self.mid_line_color, width=1)

            self.canvas.create_text(center_x, chart_bottom + 45,
                                  text=f"MID: ₹{self.mid_price:.2f}",
                                  fill=self.mid_line_color,
                                  font=('Consolas', 10, 'bold'),
                                  anchor='center')
    
    def clear_chart(self):
        """Clear the enhanced depth chart"""
        self.current_bids = []
        self.current_asks = []
        self.mid_price = 0

        # Reset all labels to default values
        self.mid_price_label.config(text="₹0.00")
        self.spread_label.config(text="Spread: ₹0.00")
        self.imbalance_label.config(text="Imbalance: 0%", fg='#888888')
        self.support_label.config(text="Support: ₹0.00")
        self.resistance_label.config(text="Resistance: ₹0.00")
        self.volume_ratio_label.config(text="Vol Ratio: 0.00")
        self.depth_strength_label.config(text="Depth: -")
        self.flow_strength_label.config(text="Flow: Neutral", fg='#888888')
        self.pressure_label.config(text="Pressure: Balanced", fg='#888888')
        self.total_volume_label.config(text="Total Vol: 0")
        self.avg_order_label.config(text="Avg Order: 0")

        self.draw_empty_chart()
        print("✅ [DEPTH_CHART] Enhanced depth chart cleared")

    def get_frame(self):
        """Get the main chart frame"""
        return self.chart_frame



