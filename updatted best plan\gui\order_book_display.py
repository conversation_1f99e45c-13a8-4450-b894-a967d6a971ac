"""
Order Book Display Component
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Optional

from config.settings import COLORS
from core.data_structures import OrderBook<PERSON>napshot
from .components import VolumeBar, AnalyticsPanel


class OrderBookDisplay(tk.Frame):
    """Order book visualization component"""
    
    def __init__(self, parent):
        super().__init__(parent, bg=COLORS['bg_dark'])
        self.current_snapshot: Optional[OrderBookSnapshot] = None
        self.create_widgets()
        
    def create_widgets(self):
        """Create order book display widgets"""
        # Main container
        main_container = tk.Frame(self, bg=COLORS['bg_dark'])
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # Left panel - Order Book
        left_panel = tk.Frame(main_container, bg=COLORS['bg_dark'])
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Order book header
        self.create_order_book_header(left_panel)
        
        # Order book content
        self.create_order_book_content(left_panel)
        
        # Right panel - Analytics
        right_panel = tk.Frame(main_container, bg=COLORS['bg_dark'], width=300)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y)
        right_panel.pack_propagate(False)
        
        # Analytics panel
        self.analytics_panel = AnalyticsPanel(right_panel)
        self.analytics_panel.pack(fill=tk.BOTH, expand=True)
        
    def create_order_book_header(self, parent):
        """Create order book header"""
        header_frame = tk.Frame(parent, bg=COLORS['bg_medium'], height=40)
        header_frame.pack(fill=tk.X, pady=(0, 5))
        header_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(
            header_frame,
            text="📊 CGCL Order Book - Live Market Depth",
            bg=COLORS['bg_medium'],
            fg=COLORS['text_white'],
            font=('Arial', 14, 'bold')
        )
        title_label.pack(side=tk.LEFT, padx=10, pady=10)
        
        # Spread info
        self.spread_label = tk.Label(
            header_frame,
            text="Spread: ₹--.--",
            bg=COLORS['bg_medium'],
            fg=COLORS['text_gray'],
            font=('Arial', 12)
        )
        self.spread_label.pack(side=tk.RIGHT, padx=10, pady=10)
    
    def create_order_book_content(self, parent):
        """Create order book content area"""
        # Content frame with scrollbar
        content_frame = tk.Frame(parent, bg=COLORS['bg_dark'])
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Canvas for scrolling
        self.canvas = tk.Canvas(
            content_frame,
            bg=COLORS['bg_dark'],
            highlightthickness=0
        )
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(content_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.canvas.configure(yscrollcommand=scrollbar.set)
        
        # Scrollable frame
        self.scrollable_frame = tk.Frame(self.canvas, bg=COLORS['bg_dark'])
        self.canvas_window = self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        
        # Bind canvas resize
        self.canvas.bind('<Configure>', self.on_canvas_configure)
        self.scrollable_frame.bind('<Configure>', self.on_frame_configure)
        
        # Create order book table
        self.create_order_book_table()
    
    def create_order_book_table(self):
        """Create order book table"""
        # Table header
        header_frame = tk.Frame(self.scrollable_frame, bg=COLORS['bg_medium'], height=30)
        header_frame.pack(fill=tk.X, pady=(0, 2))
        header_frame.pack_propagate(False)
        
        # Header columns
        headers = ["Orders", "Quantity", "Price", "Price", "Quantity", "Orders"]
        widths = [80, 100, 100, 100, 100, 80]
        
        for i, (header, width) in enumerate(zip(headers, widths)):
            label = tk.Label(
                header_frame,
                text=header,
                bg=COLORS['bg_medium'],
                fg=COLORS['text_white'],
                font=('Arial', 10, 'bold'),
                width=width//8
            )
            label.pack(side=tk.LEFT, padx=1)
        
        # Order book rows container
        self.rows_container = tk.Frame(self.scrollable_frame, bg=COLORS['bg_dark'])
        self.rows_container.pack(fill=tk.BOTH, expand=True)
        
        # Create initial empty rows
        self.order_rows = []
        for i in range(20):  # 20 levels
            self.create_order_row(i)
    
    def create_order_row(self, row_index):
        """Create a single order book row"""
        row_frame = tk.Frame(self.rows_container, bg=COLORS['bg_dark'], height=25)
        row_frame.pack(fill=tk.X, pady=1)
        row_frame.pack_propagate(False)
        
        # Row widgets
        row_widgets = {}
        
        # Bid side (left)
        row_widgets['bid_orders'] = tk.Label(
            row_frame, text="", bg=COLORS['bg_dark'], fg=COLORS['text_gray'],
            font=('Arial', 9), width=10, anchor='center'
        )
        row_widgets['bid_orders'].pack(side=tk.LEFT, padx=1)
        
        row_widgets['bid_quantity'] = tk.Label(
            row_frame, text="", bg=COLORS['bg_dark'], fg=COLORS['text_gray'],
            font=('Arial', 9), width=12, anchor='center'
        )
        row_widgets['bid_quantity'].pack(side=tk.LEFT, padx=1)
        
        row_widgets['bid_price'] = tk.Label(
            row_frame, text="", bg=COLORS['bid_green_bg'], fg=COLORS['bid_green'],
            font=('Arial', 9, 'bold'), width=12, anchor='center'
        )
        row_widgets['bid_price'].pack(side=tk.LEFT, padx=1)
        
        # Ask side (right)
        row_widgets['ask_price'] = tk.Label(
            row_frame, text="", bg=COLORS['ask_red_bg'], fg=COLORS['ask_red'],
            font=('Arial', 9, 'bold'), width=12, anchor='center'
        )
        row_widgets['ask_price'].pack(side=tk.LEFT, padx=1)
        
        row_widgets['ask_quantity'] = tk.Label(
            row_frame, text="", bg=COLORS['bg_dark'], fg=COLORS['text_gray'],
            font=('Arial', 9), width=12, anchor='center'
        )
        row_widgets['ask_quantity'].pack(side=tk.LEFT, padx=1)
        
        row_widgets['ask_orders'] = tk.Label(
            row_frame, text="", bg=COLORS['bg_dark'], fg=COLORS['text_gray'],
            font=('Arial', 9), width=10, anchor='center'
        )
        row_widgets['ask_orders'].pack(side=tk.LEFT, padx=1)
        
        # Volume bars
        row_widgets['bid_volume_bar'] = VolumeBar(row_frame, width=50, height=20)
        row_widgets['ask_volume_bar'] = VolumeBar(row_frame, width=50, height=20)
        
        self.order_rows.append(row_widgets)
    
    def update_display(self, snapshot: OrderBookSnapshot, flow_analysis: Dict = None):
        """Update order book display"""
        try:
            self.current_snapshot = snapshot
            
            # Update spread
            if snapshot.spread > 0:
                self.spread_label.config(
                    text=f"Spread: ₹{snapshot.spread:.2f}",
                    fg=COLORS['text_white']
                )
            
            # Update order book rows
            max_levels = min(len(self.order_rows), max(len(snapshot.bids), len(snapshot.asks)))
            
            for i in range(max_levels):
                row_widgets = self.order_rows[i]
                
                # Update bid side
                if i < len(snapshot.bids):
                    bid = snapshot.bids[i]
                    row_widgets['bid_orders'].config(text=str(bid.orders))
                    row_widgets['bid_quantity'].config(text=f"{bid.quantity:,}")
                    row_widgets['bid_price'].config(text=f"₹{bid.price:.2f}")
                    
                    # Update volume bar
                    max_qty = max([b.quantity for b in snapshot.bids[:5]], default=1)
                    # row_widgets['bid_volume_bar'].update_volume(bid.quantity, max_qty)
                else:
                    # Clear bid side
                    row_widgets['bid_orders'].config(text="")
                    row_widgets['bid_quantity'].config(text="")
                    row_widgets['bid_price'].config(text="")
                
                # Update ask side
                if i < len(snapshot.asks):
                    ask = snapshot.asks[i]
                    row_widgets['ask_price'].config(text=f"₹{ask.price:.2f}")
                    row_widgets['ask_quantity'].config(text=f"{ask.quantity:,}")
                    row_widgets['ask_orders'].config(text=str(ask.orders))
                    
                    # Update volume bar
                    max_qty = max([a.quantity for a in snapshot.asks[:5]], default=1)
                    # row_widgets['ask_volume_bar'].update_volume(ask.quantity, max_qty)
                else:
                    # Clear ask side
                    row_widgets['ask_price'].config(text="")
                    row_widgets['ask_quantity'].config(text="")
                    row_widgets['ask_orders'].config(text="")
            
            # Update analytics panel
            if flow_analysis and self.analytics_panel:
                self.analytics_panel.update_analytics(flow_analysis.__dict__ if hasattr(flow_analysis, '__dict__') else flow_analysis)
            
        except Exception as e:
            print(f"Error updating order book display: {e}")
    
    def on_canvas_configure(self, event):
        """Handle canvas resize"""
        try:
            # Update scroll region
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            
            # Update canvas window width
            canvas_width = event.width
            self.canvas.itemconfig(self.canvas_window, width=canvas_width)
            
        except Exception as e:
            print(f"Error in canvas configure: {e}")
    
    def on_frame_configure(self, event):
        """Handle frame resize"""
        try:
            # Update scroll region
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            
        except Exception as e:
            print(f"Error in frame configure: {e}")
    
    def highlight_price_level(self, price: float, color: str = COLORS['text_green']):
        """Highlight a specific price level"""
        try:
            if not self.current_snapshot:
                return
            
            # Find and highlight the price level
            for i, row_widgets in enumerate(self.order_rows):
                # Check bid side
                if i < len(self.current_snapshot.bids):
                    bid = self.current_snapshot.bids[i]
                    if abs(bid.price - price) < 0.01:  # Close enough
                        row_widgets['bid_price'].config(bg=color)
                        # Reset after 2 seconds
                        self.after(2000, lambda: row_widgets['bid_price'].config(bg=COLORS['bid_green_bg']))
                
                # Check ask side
                if i < len(self.current_snapshot.asks):
                    ask = self.current_snapshot.asks[i]
                    if abs(ask.price - price) < 0.01:  # Close enough
                        row_widgets['ask_price'].config(bg=color)
                        # Reset after 2 seconds
                        self.after(2000, lambda: row_widgets['ask_price'].config(bg=COLORS['ask_red_bg']))
                        
        except Exception as e:
            print(f"Error highlighting price level: {e}")
