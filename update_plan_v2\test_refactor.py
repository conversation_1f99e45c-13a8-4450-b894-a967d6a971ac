"""
Test script to verify the order book UI refactoring
"""

import tkinter as tk
from order_book_ui import OrderBook<PERSON>

def test_order_book_ui():
    """Test the modular OrderBookUI component"""
    print("🧪 Testing OrderBookUI refactoring...")
    
    # Create test window
    root = tk.Tk()
    root.title("Order Book UI Test")
    root.geometry("800x600")
    root.configure(bg='#0a0a0a')
    
    # Create container frame
    container = tk.Frame(root, bg='#0a0a0a')
    container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # Initialize OrderBookUI
    order_book_ui = OrderBookUI(container)
    
    # Test data
    test_bids = [
        [1500, 5, 186.50, 'LTP_GENERATED'],
        [2200, 8, 186.45, 'LTP_GENERATED'],
        [1800, 3, 186.40, 'LTP_GENERATED'],
        [3000, 12, 186.35, 'LTP_GENERATED'],
        [2500, 7, 186.30, 'LTP_GENERATED']
    ]
    
    test_asks = [
        [186.55, 6, 1200, 'LTP_GENERATED'],
        [186.60, 9, 1800, 'LTP_GENERATED'],
        [186.65, 4, 2100, 'LTP_GENERATED'],
        [186.70, 11, 1600, 'LTP_GENERATED'],
        [186.75, 8, 2800, 'LTP_GENERATED']
    ]
    
    # Update with test data
    def update_test_data():
        order_book_ui.update_display(test_bids, test_asks)
        print("✅ Test data updated successfully")
        
        # Schedule next update
        root.after(2000, update_test_data)
    
    # Start test updates
    root.after(1000, update_test_data)
    
    print("✅ OrderBookUI test started - window should display order book")
    print("📊 Test will update every 2 seconds with sample data")
    
    # Run the test
    root.mainloop()

if __name__ == "__main__":
    test_order_book_ui()
