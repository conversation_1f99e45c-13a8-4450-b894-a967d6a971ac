"""
Historical Order Book Storage with Compression
"""

import json
import gzip
from collections import deque
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

from core.data_structures import OrderBookSnapshot


class CompressedOrderBookSnapshot:
    """Compressed order book snapshot for efficient storage"""
    
    def __init__(self, timestamp: datetime, bids: List[Tuple], asks: List[Tuple],
                 spread: float, imbalance: float, total_bid_qty: int, 
                 total_ask_qty: int, mid_price: float):
        self.timestamp = timestamp
        self.bids = bids
        self.asks = asks
        self.spread = spread
        self.imbalance = imbalance
        self.total_bid_qty = total_bid_qty
        self.total_ask_qty = total_ask_qty
        self.mid_price = mid_price
    
    def compress(self) -> Dict:
        """Compress snapshot for storage"""
        return {
            'ts': int(self.timestamp.timestamp() * 1000),  # milliseconds
            'b': [(round(p, 2), q, o) for p, q, o in self.bids[:10]],  # Top 10 bids
            'a': [(round(p, 2), q, o) for p, q, o in self.asks[:10]],  # Top 10 asks
            's': round(self.spread, 3),
            'i': round(self.imbalance, 1),
            'tbq': self.total_bid_qty,
            'taq': self.total_ask_qty,
            'mp': round(self.mid_price, 2)
        }
    
    @classmethod
    def decompress(cls, data: Dict) -> 'CompressedOrderBookSnapshot':
        """Decompress snapshot from storage"""
        return cls(
            timestamp=datetime.fromtimestamp(data['ts'] / 1000),
            bids=data['b'],
            asks=data['a'],
            spread=data['s'],
            imbalance=data['i'],
            total_bid_qty=data['tbq'],
            total_ask_qty=data['taq'],
            mid_price=data['mp']
        )


class HistoricalOrderBookStorage:
    """Efficient historical order book storage with 5+ minute retention"""
    
    def __init__(self, retention_minutes: int = 10, max_snapshots_per_minute: int = 60, 
                 compression_enabled: bool = True):
        self.retention_minutes = retention_minutes
        self.max_snapshots_per_minute = max_snapshots_per_minute
        self.compression_enabled = compression_enabled
        
        # Time-based storage buckets (minute-level indexing)
        self.storage_buckets: Dict[str, List] = {}  # "YYYY-MM-DD-HH-MM" -> [snapshots]
        self.total_snapshots = 0
        self.total_storage_size_bytes = 0
        
        # Fast access indices
        self.timestamp_index: List[datetime] = []  # Sorted timestamps for binary search
        self.price_level_index: Dict[float, List[datetime]] = {}  # price -> timestamps
        
        # Storage statistics
        self.storage_stats = {
            'snapshots_stored': 0,
            'snapshots_retrieved': 0,
            'compression_ratio': 0.0,
            'storage_efficiency': 0.0,
            'oldest_snapshot': None,
            'newest_snapshot': None
        }
    
    def store_snapshot(self, snapshot: OrderBookSnapshot) -> bool:
        """Store order book snapshot with compression"""
        try:
            # Create compressed snapshot
            compressed = CompressedOrderBookSnapshot(
                timestamp=snapshot.timestamp,
                bids=[(level.price, level.quantity, level.orders) for level in snapshot.bids],
                asks=[(level.price, level.quantity, level.orders) for level in snapshot.asks],
                spread=snapshot.spread,
                imbalance=snapshot.imbalance,
                total_bid_qty=snapshot.total_bid_quantity,
                total_ask_qty=snapshot.total_ask_quantity,
                mid_price=snapshot.mid_price
            )
            
            # Get time bucket key
            bucket_key = snapshot.timestamp.strftime("%Y-%m-%d-%H-%M")
            
            # Initialize bucket if needed
            if bucket_key not in self.storage_buckets:
                self.storage_buckets[bucket_key] = []
            
            # Check if bucket is full
            if len(self.storage_buckets[bucket_key]) >= self.max_snapshots_per_minute:
                # Remove oldest snapshot from this bucket
                self.storage_buckets[bucket_key].pop(0)
            
            # Store compressed data
            if self.compression_enabled:
                compressed_data = compressed.compress()
            else:
                compressed_data = compressed.__dict__
            
            self.storage_buckets[bucket_key].append(compressed_data)
            
            # Update indices
            self._update_indices(snapshot.timestamp, snapshot.mid_price)
            
            # Update statistics
            self.total_snapshots += 1
            self.storage_stats['snapshots_stored'] += 1
            self.storage_stats['newest_snapshot'] = snapshot.timestamp
            
            if self.storage_stats['oldest_snapshot'] is None:
                self.storage_stats['oldest_snapshot'] = snapshot.timestamp
            
            # Clean old data
            self._cleanup_old_data()
            
            return True
            
        except Exception as e:
            print(f"Error storing snapshot: {e}")
            return False
    
    def get_snapshots_in_range(self, start_time: datetime, end_time: datetime) -> List[CompressedOrderBookSnapshot]:
        """Retrieve snapshots within time range"""
        try:
            snapshots = []
            
            # Generate bucket keys for the time range
            current_time = start_time.replace(second=0, microsecond=0)
            while current_time <= end_time:
                bucket_key = current_time.strftime("%Y-%m-%d-%H-%M")
                
                if bucket_key in self.storage_buckets:
                    for snapshot_data in self.storage_buckets[bucket_key]:
                        snapshot = CompressedOrderBookSnapshot.decompress(snapshot_data)
                        if start_time <= snapshot.timestamp <= end_time:
                            snapshots.append(snapshot)
                
                current_time += timedelta(minutes=1)
            
            # Sort by timestamp
            snapshots.sort(key=lambda x: x.timestamp)
            
            self.storage_stats['snapshots_retrieved'] += len(snapshots)
            return snapshots
            
        except Exception as e:
            print(f"Error retrieving snapshots: {e}")
            return []
    
    def get_latest_snapshots(self, count: int = 100) -> List[CompressedOrderBookSnapshot]:
        """Get the most recent snapshots"""
        try:
            if count <= 0:
                return []
            
            # Get recent bucket keys
            now = datetime.now()
            snapshots = []
            
            # Look back up to retention period
            for minutes_back in range(self.retention_minutes):
                time_point = now - timedelta(minutes=minutes_back)
                bucket_key = time_point.strftime("%Y-%m-%d-%H-%M")
                
                if bucket_key in self.storage_buckets:
                    bucket_snapshots = []
                    for snapshot_data in self.storage_buckets[bucket_key]:
                        snapshot = CompressedOrderBookSnapshot.decompress(snapshot_data)
                        bucket_snapshots.append(snapshot)
                    
                    # Sort bucket by timestamp (newest first)
                    bucket_snapshots.sort(key=lambda x: x.timestamp, reverse=True)
                    snapshots.extend(bucket_snapshots)
                
                if len(snapshots) >= count:
                    break
            
            # Return most recent snapshots
            snapshots.sort(key=lambda x: x.timestamp, reverse=True)
            return snapshots[:count]
            
        except Exception as e:
            print(f"Error getting latest snapshots: {e}")
            return []
    
    def _update_indices(self, timestamp: datetime, price: float):
        """Update fast access indices"""
        try:
            # Update timestamp index
            if not self.timestamp_index or timestamp > self.timestamp_index[-1]:
                self.timestamp_index.append(timestamp)
            else:
                # Insert in sorted order
                import bisect
                bisect.insort(self.timestamp_index, timestamp)
            
            # Update price level index
            rounded_price = round(price, 2)
            if rounded_price not in self.price_level_index:
                self.price_level_index[rounded_price] = []
            self.price_level_index[rounded_price].append(timestamp)
            
        except Exception as e:
            print(f"Error updating indices: {e}")
    
    def _cleanup_old_data(self):
        """Remove data older than retention period"""
        try:
            cutoff_time = datetime.now() - timedelta(minutes=self.retention_minutes)
            cutoff_bucket = cutoff_time.strftime("%Y-%m-%d-%H-%M")
            
            # Remove old buckets
            buckets_to_remove = []
            for bucket_key in self.storage_buckets:
                if bucket_key < cutoff_bucket:
                    buckets_to_remove.append(bucket_key)
            
            for bucket_key in buckets_to_remove:
                del self.storage_buckets[bucket_key]
            
            # Clean timestamp index
            self.timestamp_index = [ts for ts in self.timestamp_index if ts >= cutoff_time]
            
            # Clean price level index
            for price in list(self.price_level_index.keys()):
                self.price_level_index[price] = [
                    ts for ts in self.price_level_index[price] if ts >= cutoff_time
                ]
                if not self.price_level_index[price]:
                    del self.price_level_index[price]
            
            # Update oldest snapshot
            if self.timestamp_index:
                self.storage_stats['oldest_snapshot'] = min(self.timestamp_index)
            
        except Exception as e:
            print(f"Error cleaning old data: {e}")
    
    def get_storage_statistics(self) -> Dict:
        """Get storage performance statistics"""
        try:
            # Calculate storage efficiency
            total_buckets = len(self.storage_buckets)
            total_snapshots = sum(len(bucket) for bucket in self.storage_buckets.values())
            
            if total_buckets > 0:
                avg_snapshots_per_bucket = total_snapshots / total_buckets
                storage_efficiency = (avg_snapshots_per_bucket / self.max_snapshots_per_minute) * 100
            else:
                storage_efficiency = 0
            
            self.storage_stats.update({
                'total_buckets': total_buckets,
                'total_snapshots': total_snapshots,
                'storage_efficiency': storage_efficiency,
                'retention_minutes': self.retention_minutes,
                'compression_enabled': self.compression_enabled
            })
            
            return self.storage_stats.copy()
            
        except Exception as e:
            print(f"Error calculating storage statistics: {e}")
            return self.storage_stats.copy()
    
    def export_data(self, filepath: str, start_time: datetime = None, end_time: datetime = None) -> bool:
        """Export historical data to file"""
        try:
            if start_time is None:
                start_time = datetime.now() - timedelta(minutes=self.retention_minutes)
            if end_time is None:
                end_time = datetime.now()
            
            snapshots = self.get_snapshots_in_range(start_time, end_time)
            
            export_data = {
                'metadata': {
                    'export_time': datetime.now().isoformat(),
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'snapshot_count': len(snapshots)
                },
                'snapshots': [snapshot.compress() for snapshot in snapshots]
            }
            
            with gzip.open(filepath, 'wt') as f:
                json.dump(export_data, f)
            
            print(f"✅ Exported {len(snapshots)} snapshots to {filepath}")
            return True
            
        except Exception as e:
            print(f"Error exporting data: {e}")
            return False
