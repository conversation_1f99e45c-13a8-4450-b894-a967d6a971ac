"""
Main GUI Window for CGCL Trading System
"""

import tkinter as tk
from tkinter import ttk
import threading
from datetime import datetime
from typing import Optional

from config.settings import SYSTEM_CONFIG, COLORS
from core.data_source_manager import DataSourceManager, DataSourceType
from core.order_book import OrderBookStateManager, OrderBookEventSystem
from core.data_structures import MarketData, OrderBookSnapshot, OrderBookEventType
from analytics.flow_analysis import OrderFlowImbalanceDetector
from analytics.price_prediction import PricePredictionEngine
from analytics.trading_signals import TradingSignalGenerator
from analytics.market_depth import MarketDepthAnalytics
from storage.historical_data import HistoricalOrderBookStorage
from storage.reconstruction import OrderBookReconstructionEngine
from utils.market_hours import market_hours
from .order_book_display import OrderBookDisplay
from .components import PerformancePanel, StatusPanel, ClockWidget


class CGCLMainWindow:
    """Main application window"""
    
    def __init__(self):
        # Initialize GUI
        self.root = None
        self.initialize_gui()
        
        # Core components
        self.data_source_manager = DataSourceManager()
        self.order_book_manager = OrderBookStateManager()
        self.event_system = OrderBookEventSystem()
        self.flow_detector = OrderFlowImbalanceDetector()
        self.price_predictor = PricePredictionEngine()
        self.signal_generator = TradingSignalGenerator()
        self.market_analytics = MarketDepthAnalytics()
        self.historical_storage = HistoricalOrderBookStorage()
        self.reconstruction_engine = OrderBookReconstructionEngine()

        # GUI components
        self.order_book_display = None
        self.performance_panel = None
        self.status_panel = None
        self.clock_widget = None
        
        # Data
        self.current_market_data: Optional[MarketData] = None
        self.current_snapshot: Optional[OrderBookSnapshot] = None
        self.latest_flow_analysis = {}
        self.latest_price_prediction = {}
        self.latest_trading_signals = {}
        self.latest_market_analytics = {}
        
        # State
        self.is_running = False
        self.update_count = 0
        
        # Setup
        self.setup_data_source_callbacks()
        self.create_interface()
        self.setup_event_handlers()

        # Start with simulation by default
        self.start_simulation()
        
    def initialize_gui(self):
        """Initialize the main GUI window"""
        try:
            print("🖥️ Initializing GUI...")
            self.root = tk.Tk()
            self.root.title(SYSTEM_CONFIG['app_title'])
            self.root.geometry(SYSTEM_CONFIG['app_geometry'])
            self.root.configure(bg=COLORS['bg_dark'])
            
            # Configure style
            style = ttk.Style()
            style.theme_use('clam')
            style.configure('TLabel', background=COLORS['bg_dark'], foreground=COLORS['text_white'])
            style.configure('TFrame', background=COLORS['bg_dark'])
            
            print("✅ GUI initialized successfully")
            
        except Exception as e:
            print(f"❌ Error initializing GUI: {e}")
            self.root = None
            raise
    
    def create_interface(self):
        """Create the main interface"""
        try:
            if not self.root:
                raise Exception("GUI not initialized")
            
            # Main container
            main_frame = tk.Frame(self.root, bg=COLORS['bg_dark'])
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # Top panel - Performance, Status, and Clock
            top_panel = tk.Frame(main_frame, bg=COLORS['bg_dark'])
            top_panel.pack(fill=tk.X, pady=(0, 10))

            # Performance panel (left)
            self.performance_panel = PerformancePanel(top_panel)
            self.performance_panel.pack(side=tk.LEFT, fill=tk.X, expand=True)

            # Clock widget (center)
            self.clock_widget = ClockWidget(top_panel)
            self.clock_widget.pack(side=tk.LEFT, padx=(10, 10))

            # Status panel (right)
            self.status_panel = StatusPanel(top_panel)
            self.status_panel.pack(side=tk.RIGHT, padx=(10, 0))
            
            # Middle panel - Order Book Display
            middle_panel = tk.Frame(main_frame, bg=COLORS['bg_dark'])
            middle_panel.pack(fill=tk.BOTH, expand=True)
            
            # Order book display
            self.order_book_display = OrderBookDisplay(middle_panel)
            self.order_book_display.pack(fill=tk.BOTH, expand=True)
            
            # Bottom panel - Controls
            bottom_panel = tk.Frame(main_frame, bg=COLORS['bg_dark'])
            bottom_panel.pack(fill=tk.X, pady=(10, 0))
            
            # Control buttons
            self.create_control_buttons(bottom_panel)
            
            print("✅ Interface created successfully")
            
        except Exception as e:
            print(f"❌ Error creating interface: {e}")
            raise
    
    def create_control_buttons(self, parent):
        """Create control buttons"""
        button_frame = tk.Frame(parent, bg=COLORS['bg_dark'])
        button_frame.pack(fill=tk.X)
        
        # Connect button
        self.connect_button = tk.Button(
            button_frame,
            text="🔌 Connect",
            command=self.toggle_connection,
            bg=COLORS['bid_green'],
            fg=COLORS['text_white'],
            font=('Arial', 10, 'bold'),
            padx=20,
            pady=5
        )
        self.connect_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Status label
        self.connection_status_label = tk.Label(
            button_frame,
            text="⚪ Disconnected",
            bg=COLORS['bg_dark'],
            fg=COLORS['text_gray'],
            font=('Arial', 10)
        )
        self.connection_status_label.pack(side=tk.LEFT, padx=(0, 20))

        # Data source selection
        data_source_frame = tk.Frame(button_frame, bg=COLORS['bg_dark'])
        data_source_frame.pack(side=tk.LEFT, padx=(0, 20))

        tk.Label(
            data_source_frame,
            text="Data Source:",
            bg=COLORS['bg_dark'],
            fg=COLORS['text_white'],
            font=('Arial', 9)
        ).pack(side=tk.LEFT, padx=(0, 5))

        # Live data button
        self.live_data_button = tk.Button(
            data_source_frame,
            text="📡 Live",
            command=lambda: self.set_data_source(DataSourceType.LIVE),
            bg=COLORS['bg_medium'],
            fg=COLORS['text_white'],
            font=('Arial', 9),
            padx=10,
            pady=2
        )
        self.live_data_button.pack(side=tk.LEFT, padx=(0, 2))

        # Simulated data button
        self.sim_data_button = tk.Button(
            data_source_frame,
            text="🎯 Sim",
            command=lambda: self.set_data_source(DataSourceType.SIMULATED),
            bg=COLORS['bid_green'],  # Start with simulation active
            fg=COLORS['text_white'],
            font=('Arial', 9, 'bold'),
            padx=10,
            pady=2
        )
        self.sim_data_button.pack(side=tk.LEFT)

        # Analytics toggle
        self.analytics_button = tk.Button(
            button_frame,
            text="📊 Analytics: ON",
            command=self.toggle_analytics,
            bg=COLORS['bg_medium'],
            fg=COLORS['text_white'],
            font=('Arial', 10),
            padx=20,
            pady=5
        )
        self.analytics_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Exit button
        exit_button = tk.Button(
            button_frame,
            text="❌ Exit",
            command=self.on_closing,
            bg=COLORS['ask_red'],
            fg=COLORS['text_white'],
            font=('Arial', 10, 'bold'),
            padx=20,
            pady=5
        )
        exit_button.pack(side=tk.RIGHT)
    
    def setup_data_source_callbacks(self):
        """Setup data source callbacks"""
        self.data_source_manager.on_market_data = self.on_market_data
        self.data_source_manager.on_order_book_data = self.on_order_book_data
        self.data_source_manager.on_connection_status = self.on_connection_status
    
    def setup_event_handlers(self):
        """Setup event handlers"""
        # Window close event
        if self.root:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Start update timer
        self.schedule_updates()
    
    def schedule_updates(self):
        """Schedule periodic updates"""
        if self.root:
            # Update GUI every 100ms
            self.root.after(SYSTEM_CONFIG['gui_update_interval'], self.update_gui)
            
            # Update performance every 1 second
            self.root.after(SYSTEM_CONFIG['performance_update_interval'], self.update_performance)
            
            # Schedule next update
            self.root.after(1000, self.schedule_updates)
    
    def on_market_data(self, market_data: MarketData):
        """Handle incoming market data with market hours validation"""
        try:
            # Validate market data based on market hours
            if not market_hours.validate_market_data(market_data.ltp):
                # Market is closed or data is invalid, don't process
                print(f"⏰ Market closed - ignoring invalid data (Price: ₹{market_data.ltp:.2f})")
                return

            # Update last valid data
            market_hours.update_last_valid_data(market_data.ltp, market_data.__dict__)

            self.current_market_data = market_data
            self.update_count += 1
            
            # Update order book snapshot
            if self.order_book_manager.bids or self.order_book_manager.asks:
                self.current_snapshot = self.order_book_manager.get_snapshot(
                    market_data.symbol, market_data
                )
                
                # Perform comprehensive analysis
                if self.current_snapshot:
                    # Store historical data
                    self.historical_storage.store_snapshot(self.current_snapshot)

                    # Market depth analytics
                    self.latest_market_analytics = self.market_analytics.calculate_comprehensive_analytics(
                        self.current_snapshot, self.latest_flow_analysis
                    )

                    # Flow analysis
                    self.latest_flow_analysis = self.flow_detector.analyze_order_flow_imbalance(
                        self.current_snapshot
                    )

                    # Price prediction (every 30 seconds)
                    if self.update_count % 30 == 0:
                        self.latest_price_prediction = self.price_predictor.predict_price_30min(
                            market_data.ltp, self.current_snapshot, self.latest_flow_analysis, self.latest_market_analytics
                        )

                    # Trading signals (every 60 seconds)
                    if self.update_count % 60 == 0:
                        self.latest_trading_signals = self.signal_generator.generate_trading_signals(
                            market_data.ltp, self.current_snapshot, self.latest_flow_analysis,
                            {}, self.latest_price_prediction, self.latest_market_analytics
                        )
            
        except Exception as e:
            print(f"Error handling market data: {e}")
    
    def on_order_book_data(self, bids, asks):
        """Handle incoming order book data with market hours validation"""
        try:
            # Check if we should update order book based on market hours
            if not market_hours.should_update_order_book():
                print("⏰ Market closed - ignoring order book updates")
                return

            # Reconstruct order book if needed
            current_price = self.current_market_data.ltp if self.current_market_data else 180.0
            reconstructed_bids, reconstructed_asks, quality_metrics = self.reconstruction_engine.reconstruct_order_book(
                bids, asks, current_price, datetime.now()
            )

            # Update order book state with reconstructed data
            bid_changes = self.order_book_manager.update_bids(reconstructed_bids)
            ask_changes = self.order_book_manager.update_asks(reconstructed_asks)
            
            # Emit events for significant changes
            if len(bid_changes) > 5 or len(ask_changes) > 5:
                self.event_system.emit_event(
                    event_type=OrderBookEventType.VOLUME_SURGE,
                    symbol="CGCL",
                    data={'bid_changes': len(bid_changes), 'ask_changes': len(ask_changes)},
                    priority=2
                )
            
            self.update_count += 1
            
        except Exception as e:
            print(f"Error handling order book data: {e}")
    
    def on_connection_status(self, status: str):
        """Handle connection status changes"""
        try:
            if self.connection_status_label:
                if "CONNECTED_LIVE" in status:
                    self.connection_status_label.config(text="🟢 Live Data", fg=COLORS['text_green'])
                    self.connect_button.config(text="🔌 Disconnect")
                elif "CONNECTED_SIMULATED" in status:
                    self.connection_status_label.config(text="🟢 Simulated", fg=COLORS['bid_green'])
                    self.connect_button.config(text="🔌 Disconnect")
                elif status == "DISCONNECTED":
                    self.connection_status_label.config(text="🔴 Disconnected", fg=COLORS['ask_red'])
                    self.connect_button.config(text="🔌 Connect")
                else:
                    self.connection_status_label.config(text=f"🟡 {status}", fg=COLORS['text_gray'])

        except Exception as e:
            print(f"Error updating connection status: {e}")
    
    def toggle_connection(self):
        """Toggle data source connection"""
        try:
            success = self.data_source_manager.toggle_connection()
            if not success and self.data_source_manager.is_connected:
                print("❌ Failed to establish connection")

        except Exception as e:
            print(f"Error toggling connection: {e}")

    def set_data_source(self, source_type: DataSourceType):
        """Set the data source type"""
        try:
            success = self.data_source_manager.set_data_source(source_type)
            if success:
                self.update_data_source_buttons()
            else:
                print(f"❌ Failed to switch to {source_type.value} data source")
        except Exception as e:
            print(f"Error setting data source: {e}")

    def update_data_source_buttons(self):
        """Update data source button appearance"""
        current_source = self.data_source_manager.get_current_source()

        if current_source == DataSourceType.LIVE:
            # Live active
            self.live_data_button.config(
                bg=COLORS['bid_green'],
                font=('Arial', 9, 'bold')
            )
            self.sim_data_button.config(
                bg=COLORS['bg_medium'],
                font=('Arial', 9)
            )
        else:
            # Simulation active
            self.live_data_button.config(
                bg=COLORS['bg_medium'],
                font=('Arial', 9)
            )
            self.sim_data_button.config(
                bg=COLORS['bid_green'],
                font=('Arial', 9, 'bold')
            )

    def start_simulation(self):
        """Start simulation automatically"""
        try:
            print("🎯 Starting CGCL simulation...")
            success = self.data_source_manager.connect()
            if success:
                self.update_data_source_buttons()
                print("✅ CGCL simulation started successfully")
            else:
                print("❌ Failed to start simulation")
        except Exception as e:
            print(f"Error starting simulation: {e}")
    
    def toggle_analytics(self):
        """Toggle analytics processing"""
        # This would toggle analytics processing
        # For now, just update button text
        current_text = self.analytics_button.cget('text')
        if "ON" in current_text:
            self.analytics_button.config(text="📊 Analytics: OFF")
        else:
            self.analytics_button.config(text="📊 Analytics: ON")
    
    def update_gui(self):
        """Update GUI components"""
        try:
            # Update order book display
            if self.order_book_display and self.current_snapshot:
                self.order_book_display.update_display(self.current_snapshot, self.latest_flow_analysis)
            
            # Update status panel
            if self.status_panel:
                self.status_panel.update_status({
                    'symbol': 'CGCL',
                    'price': self.current_market_data.ltp if self.current_market_data else 0.0,
                    'change': self.current_market_data.price_change if self.current_market_data else 0.0,
                    'change_pct': self.current_market_data.price_change_pct if self.current_market_data else 0.0,
                    'volume': self.current_market_data.volume if self.current_market_data else 0,
                    'update_count': self.update_count
                })
                
        except Exception as e:
            print(f"Error updating GUI: {e}")
    
    def update_performance(self):
        """Update performance metrics"""
        try:
            if self.performance_panel:
                # Get metrics from current data source
                if self.data_source_manager.is_live_data():
                    metrics = self.data_source_manager.websocket_manager.get_performance_metrics()
                else:
                    # For simulation, create basic metrics
                    sim_stats = self.data_source_manager.get_simulator_stats()
                    metrics = {
                        'messages_received': self.update_count,
                        'current_price': sim_stats.get('current_price', 0),
                        'price_change': sim_stats.get('price_change', 0),
                        'data_source': 'Simulation',
                        'connection_status': 'Connected (Simulation)'
                    }
                self.performance_panel.update_metrics(metrics)

        except Exception as e:
            print(f"Error updating performance: {e}")
    
    def on_closing(self):
        """Handle window closing"""
        try:
            print("🔄 Shutting down application...")

            # Disconnect data source
            if self.data_source_manager:
                self.data_source_manager.disconnect()

            # Destroy GUI
            if self.root:
                self.root.quit()
                self.root.destroy()

            print("✅ Application shutdown complete")

        except Exception as e:
            print(f"Error during shutdown: {e}")
    
    def run(self):
        """Start the application"""
        try:
            if not self.root:
                raise Exception("GUI not initialized")
            
            print("🚀 Starting application main loop...")
            self.is_running = True
            
            # Auto-connect to WebSocket
            threading.Thread(target=self._auto_connect, daemon=True).start()
            
            # Start GUI main loop
            self.root.mainloop()
            
        except Exception as e:
            print(f"❌ Error running application: {e}")
            raise
    
    def _auto_connect(self):
        """Auto-connect to data source after startup"""
        try:
            # Wait a moment for GUI to fully load
            import time
            time.sleep(2)

            # Simulation is already started in start_simulation()
            # This method is kept for compatibility but not needed
            print("ℹ️ Simulation already running")

        except Exception as e:
            print(f"Error in auto-connect: {e}")
