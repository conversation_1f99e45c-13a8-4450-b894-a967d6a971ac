"""
Utility Helper Functions
"""

import time
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from functools import wraps


def timing_decorator(func):
    """Decorator to measure function execution time"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
        print(f"⏱️ {func.__name__} executed in {execution_time:.2f}ms")
        return result
    return wrapper


def safe_execute(func, default_return=None, error_message="Error in function"):
    """Safely execute a function with error handling"""
    try:
        return func()
    except Exception as e:
        print(f"{error_message}: {e}")
        return default_return


def format_number(number: float, decimals: int = 2) -> str:
    """Format number with proper decimal places and commas"""
    try:
        if abs(number) >= 1000000:
            return f"{number/1000000:.1f}M"
        elif abs(number) >= 1000:
            return f"{number/1000:.1f}K"
        else:
            return f"{number:,.{decimals}f}"
    except:
        return "0.00"


def format_currency(amount: float, symbol: str = "₹") -> str:
    """Format currency with symbol"""
    try:
        return f"{symbol}{amount:,.2f}"
    except:
        return f"{symbol}0.00"


def format_percentage(value: float, decimals: int = 2) -> str:
    """Format percentage with proper sign"""
    try:
        sign = "+" if value >= 0 else ""
        return f"{sign}{value:.{decimals}f}%"
    except:
        return "0.00%"


def calculate_percentage_change(current: float, previous: float) -> float:
    """Calculate percentage change between two values"""
    try:
        if previous == 0:
            return 0.0
        return ((current - previous) / previous) * 100
    except:
        return 0.0


def clamp(value: float, min_value: float, max_value: float) -> float:
    """Clamp value between min and max"""
    return max(min_value, min(value, max_value))


def moving_average(values: List[float], window: int) -> float:
    """Calculate moving average"""
    try:
        if len(values) < window:
            window = len(values)
        if window == 0:
            return 0.0
        return sum(values[-window:]) / window
    except:
        return 0.0


def exponential_moving_average(values: List[float], alpha: float = 0.1) -> float:
    """Calculate exponential moving average"""
    try:
        if not values:
            return 0.0
        
        ema = values[0]
        for value in values[1:]:
            ema = alpha * value + (1 - alpha) * ema
        return ema
    except:
        return 0.0


def standard_deviation(values: List[float]) -> float:
    """Calculate standard deviation"""
    try:
        if len(values) < 2:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
        return variance ** 0.5
    except:
        return 0.0


def normalize_value(value: float, min_val: float, max_val: float) -> float:
    """Normalize value to 0-1 range"""
    try:
        if max_val == min_val:
            return 0.0
        return (value - min_val) / (max_val - min_val)
    except:
        return 0.0


def interpolate(start: float, end: float, factor: float) -> float:
    """Linear interpolation between two values"""
    try:
        factor = clamp(factor, 0.0, 1.0)
        return start + (end - start) * factor
    except:
        return start


def debounce(wait_time: float):
    """Debounce decorator to limit function calls"""
    def decorator(func):
        last_called = [0.0]
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            now = time.time()
            if now - last_called[0] >= wait_time:
                last_called[0] = now
                return func(*args, **kwargs)
        return wrapper
    return decorator


def throttle(rate_limit: float):
    """Throttle decorator to limit function call rate"""
    def decorator(func):
        last_called = [0.0]
        lock = threading.Lock()
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            with lock:
                now = time.time()
                time_since_last = now - last_called[0]
                if time_since_last >= rate_limit:
                    last_called[0] = now
                    return func(*args, **kwargs)
        return wrapper
    return decorator


def retry(max_attempts: int = 3, delay: float = 1.0):
    """Retry decorator for functions that might fail"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        raise e
                    print(f"Attempt {attempt + 1} failed: {e}. Retrying in {delay}s...")
                    time.sleep(delay)
        return wrapper
    return decorator


def validate_price(price: float) -> bool:
    """Validate if price is reasonable"""
    return 0 < price < 100000  # Reasonable price range


def validate_quantity(quantity: int) -> bool:
    """Validate if quantity is reasonable"""
    return 0 <= quantity <= 1000000  # Reasonable quantity range


def validate_timestamp(timestamp: datetime, max_age_minutes: int = 60) -> bool:
    """Validate if timestamp is recent enough"""
    try:
        age = datetime.now() - timestamp
        return age <= timedelta(minutes=max_age_minutes)
    except:
        return False


def get_market_session() -> str:
    """Get current market session"""
    try:
        now = datetime.now()
        hour = now.hour
        minute = now.minute
        
        # Market hours: 9:15 AM to 3:30 PM
        market_start = 9 * 60 + 15  # 9:15 AM in minutes
        market_end = 15 * 60 + 30   # 3:30 PM in minutes
        current_time = hour * 60 + minute
        
        if market_start <= current_time <= market_end:
            return "OPEN"
        elif current_time < market_start:
            return "PRE_MARKET"
        else:
            return "POST_MARKET"
    except:
        return "UNKNOWN"


def is_market_open() -> bool:
    """Check if market is currently open"""
    return get_market_session() == "OPEN"


def time_until_market_open() -> Optional[timedelta]:
    """Calculate time until market opens"""
    try:
        now = datetime.now()
        
        # Next market open at 9:15 AM
        market_open = now.replace(hour=9, minute=15, second=0, microsecond=0)
        
        # If already past today's market open, get tomorrow's
        if now >= market_open:
            market_open += timedelta(days=1)
        
        return market_open - now
    except:
        return None


def time_until_market_close() -> Optional[timedelta]:
    """Calculate time until market closes"""
    try:
        now = datetime.now()
        
        # Market close at 3:30 PM
        market_close = now.replace(hour=15, minute=30, second=0, microsecond=0)
        
        # Only valid if market is currently open
        if get_market_session() == "OPEN":
            return market_close - now
        else:
            return None
    except:
        return None


class RateLimiter:
    """Rate limiter for API calls"""
    
    def __init__(self, max_calls: int, time_window: float):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
        self.lock = threading.Lock()
    
    def can_proceed(self) -> bool:
        """Check if we can make another call"""
        with self.lock:
            now = time.time()
            
            # Remove old calls outside the time window
            self.calls = [call_time for call_time in self.calls if now - call_time < self.time_window]
            
            # Check if we can make another call
            if len(self.calls) < self.max_calls:
                self.calls.append(now)
                return True
            
            return False
    
    def wait_time(self) -> float:
        """Get time to wait before next call"""
        with self.lock:
            if len(self.calls) < self.max_calls:
                return 0.0
            
            oldest_call = min(self.calls)
            return self.time_window - (time.time() - oldest_call)


class CircularBuffer:
    """Circular buffer for efficient data storage"""
    
    def __init__(self, max_size: int):
        self.max_size = max_size
        self.buffer = [None] * max_size
        self.head = 0
        self.size = 0
        self.lock = threading.Lock()
    
    def append(self, item: Any):
        """Add item to buffer"""
        with self.lock:
            self.buffer[self.head] = item
            self.head = (self.head + 1) % self.max_size
            self.size = min(self.size + 1, self.max_size)
    
    def get_all(self) -> List[Any]:
        """Get all items in order"""
        with self.lock:
            if self.size < self.max_size:
                return self.buffer[:self.size]
            else:
                return self.buffer[self.head:] + self.buffer[:self.head]
    
    def get_latest(self, count: int) -> List[Any]:
        """Get latest N items"""
        all_items = self.get_all()
        return all_items[-count:] if count <= len(all_items) else all_items
    
    def is_full(self) -> bool:
        """Check if buffer is full"""
        return self.size == self.max_size
    
    def clear(self):
        """Clear the buffer"""
        with self.lock:
            self.buffer = [None] * self.max_size
            self.head = 0
            self.size = 0
