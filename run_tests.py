"""
Test Runner for Order Flow Engine
=================================

This script sets up the environment and runs comprehensive tests
for the order flow engine and WebSocket connectivity.
"""

import subprocess
import sys
import os
from pathlib import Path
import asyncio

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    dependencies = [
        'numpy>=1.21.0',
        'pandas>=1.3.0',
        'websocket-client>=1.0.0',
        'asyncio'
    ]
    
    for dep in dependencies:
        try:
            print(f"  Installing {dep}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
        except subprocess.CalledProcessError as e:
            print(f"  ❌ Failed to install {dep}: {e}")
            return False
    
    print("  ✅ All dependencies installed!")
    return True

def check_files():
    """Check if all required files exist"""
    required_files = [
        'order_flow_engine.py',
        'order_flow_monitor.py',
        'test_order_flow_offline.py',
        'test_websocket_orderflow.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        return False
    
    print("✅ All required files present")
    return True

async def run_offline_tests():
    """Run offline tests"""
    print("\n🧪 Running Offline Tests...")
    print("=" * 50)
    
    try:
        # Import and run offline tests
        from test_order_flow_offline import OrderFlowTester
        
        tester = OrderFlowTester()
        await tester.run_comprehensive_test()
        
        return True
        
    except Exception as e:
        print(f"❌ Offline tests failed: {e}")
        return False

def run_websocket_tests():
    """Run WebSocket tests (requires user input)"""
    print("\n🌐 WebSocket Connection Test")
    print("=" * 50)
    
    print("This test requires Smart API credentials.")
    print("If you don't have them yet, you can skip this test.")
    
    choice = input("\nDo you want to run WebSocket tests? (y/n): ").lower().strip()
    
    if choice == 'y':
        try:
            # Run WebSocket test
            subprocess.run([sys.executable, 'test_websocket_orderflow.py'])
            return True
        except Exception as e:
            print(f"❌ WebSocket test failed: {e}")
            return False
    else:
        print("⏭️  Skipping WebSocket tests")
        return True

def main():
    """Main test runner"""
    print("🚀 ORDER FLOW ENGINE TEST SUITE")
    print("=" * 60)
    
    # Step 1: Check files
    print("1️⃣  Checking required files...")
    if not check_files():
        print("❌ Please ensure all files are present")
        return
    
    # Step 2: Install dependencies
    print("\n2️⃣  Installing dependencies...")
    if not install_dependencies():
        print("❌ Dependency installation failed")
        return
    
    # Step 3: Run offline tests
    print("\n3️⃣  Running offline tests...")
    try:
        asyncio.run(run_offline_tests())
    except Exception as e:
        print(f"❌ Offline tests failed: {e}")
        return
    
    # Step 4: Optional WebSocket tests
    print("\n4️⃣  WebSocket tests...")
    run_websocket_tests()
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎯 TEST SUITE COMPLETED")
    print("=" * 60)
    print("\n✅ If all tests passed, your order flow engine is ready!")
    print("\n📋 Next Steps:")
    print("  1. Get Smart API credentials from Angel One")
    print("  2. Run WebSocket tests with real credentials")
    print("  3. Configure daily stock selection")
    print("  4. Start paper trading")
    print("\n📚 Available Commands:")
    print("  • python test_order_flow_offline.py    - Run offline tests")
    print("  • python test_websocket_orderflow.py   - Test WebSocket connection")
    print("  • python order_flow_demo.py           - Run interactive demo")
    print("  • python setup_order_flow.py          - Setup configuration")

if __name__ == "__main__":
    main()
