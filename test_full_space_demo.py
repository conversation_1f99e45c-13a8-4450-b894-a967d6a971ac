"""
Test Full Space Utilization Demo
Shows all charts in separate rows, stretching full width
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np

# Colors
COLORS = {
    'bg_primary': '#0a0a0a',
    'bg_secondary': '#1a1a1a', 
    'bg_tertiary': '#2a2a2a',
    'text_primary': '#ffffff',
    'text_secondary': '#b0b0b0',
    'accent_blue': '#00d4ff',
    'accent_green': '#00ff88',
    'accent_red': '#ff4757',
    'accent_orange': '#ffa726',
}

def create_chart_row(parent, title, height=150):
    """Create a full-width chart row"""
    frame = tk.Frame(parent, bg=COLORS['bg_secondary'], height=height)
    frame.pack(fill=tk.X, pady=2)
    frame.pack_propagate(False)
    
    # Title
    tk.Label(frame, text=title, bg=COLORS['bg_secondary'], 
             fg=COLORS['accent_blue'], font=('Arial', 10, 'bold')).pack(pady=2)
    
    # Chart area
    chart_area = tk.Frame(frame, bg=COLORS['bg_tertiary'])
    chart_area.pack(fill=tk.BOTH, expand=True, padx=3, pady=3)
    
    # Create sample chart
    fig = Figure(figsize=(16, 1.8), facecolor=COLORS['bg_tertiary'])
    ax = fig.add_subplot(111)
    ax.set_facecolor(COLORS['bg_tertiary'])
    
    # Sample data
    x = np.linspace(0, 10, 100)
    y = np.sin(x) + np.random.normal(0, 0.1, 100)
    
    ax.plot(x, y, color=COLORS['accent_green'], linewidth=2)
    ax.set_title(title, color=COLORS['text_primary'], fontsize=10)
    ax.tick_params(colors=COLORS['text_secondary'], labelsize=8)
    ax.grid(True, alpha=0.3)
    
    # Style
    for spine in ax.spines.values():
        spine.set_color(COLORS['text_secondary'])
    
    fig.tight_layout()
    
    canvas = FigureCanvasTkAgg(fig, chart_area)
    canvas.draw()
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

def main():
    root = tk.Tk()
    root.title("Full Space Utilization Test")
    root.geometry("1200x800")
    root.configure(bg=COLORS['bg_primary'])
    
    # Header
    header = tk.Frame(root, bg=COLORS['bg_secondary'], height=60)
    header.pack(fill=tk.X, pady=(0, 5))
    header.pack_propagate(False)
    
    tk.Label(header, text="CGCL TRADING - FULL SPACE UTILIZATION TEST", 
             bg=COLORS['bg_secondary'], fg=COLORS['accent_blue'], 
             font=('Arial', 16, 'bold')).pack(pady=15)
    
    # Main content - minimal padding
    content = tk.Frame(root, bg=COLORS['bg_primary'])
    content.pack(fill=tk.BOTH, expand=True, padx=3, pady=3)
    
    # Add charts in rows
    charts = [
        "📊 Order Book Display",
        "📈 CVD Analysis", 
        "📊 Volume Profile",
        "🦶 Footprint Analysis",
        "🌊 Order Flow Patterns"
    ]
    
    for chart_title in charts:
        create_chart_row(content, chart_title)
    
    print("✅ Full space utilization demo ready!")
    print("🎯 All charts stretch full width")
    print("📊 Minimal padding/margins")
    print("🖥️ Maximum use of available space")
    
    root.mainloop()

if __name__ == "__main__":
    main()
