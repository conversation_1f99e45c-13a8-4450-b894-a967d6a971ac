"""
Real-Time Order Flow with GUI Order Book Display
===============================================

Features:
- Every second tick updates
- Real-time order book depth in separate GUI window
- 5-minute historical order book recording
- Live order flow analysis
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import threading
import time
import json
import asyncio
from datetime import datetime, timedelta
from collections import deque
import pandas as pd
import logging

from order_flow_engine import OrderFlowEngine, AdvancedOrderFlowAnalyzer, Tick, OrderBook, OrderBookLevel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OrderBookGUI:
    """GUI window for real-time order book display"""
    
    def __init__(self, symbols):
        self.symbols = symbols
        self.root = tk.Tk()
        self.root.title("Real-Time Order Book Depth")
        self.root.geometry("1200x800")
        
        # Order book data storage (5 minutes)
        self.order_book_history = {symbol: deque(maxlen=300) for symbol in symbols}  # 300 seconds = 5 minutes
        
        # Create GUI elements
        self.create_widgets()
        
        # Update thread
        self.running = True
        self.update_thread = threading.Thread(target=self.update_display_loop, daemon=True)
        self.update_thread.start()
        
        # Current order book data
        self.current_order_books = {symbol: None for symbol in symbols}
        
    def create_widgets(self):
        """Create GUI widgets"""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="Real-Time Order Book Depth", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # Notebook for multiple symbols
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create tabs for each symbol
        self.symbol_frames = {}
        self.order_book_trees = {}
        self.stats_labels = {}
        
        for symbol in self.symbols:
            # Create tab frame
            tab_frame = ttk.Frame(self.notebook)
            self.notebook.add(tab_frame, text=symbol)
            self.symbol_frames[symbol] = tab_frame
            
            # Create order book display
            self.create_order_book_display(symbol, tab_frame)
    
    def create_order_book_display(self, symbol, parent_frame):
        """Create order book display for a symbol"""
        # Top frame for stats
        stats_frame = ttk.Frame(parent_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Stats labels
        self.stats_labels[symbol] = {
            'last_update': ttk.Label(stats_frame, text="Last Update: --", font=("Arial", 10)),
            'spread': ttk.Label(stats_frame, text="Spread: --", font=("Arial", 10)),
            'imbalance': ttk.Label(stats_frame, text="Imbalance: --", font=("Arial", 10)),
            'total_volume': ttk.Label(stats_frame, text="Total Volume: --", font=("Arial", 10))
        }
        
        # Pack stats labels
        for i, (key, label) in enumerate(self.stats_labels[symbol].items()):
            label.grid(row=0, column=i, padx=10, sticky=tk.W)
        
        # Order book frame
        book_frame = ttk.Frame(parent_frame)
        book_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create treeview for order book
        columns = ('Side', 'Price', 'Quantity', 'Orders', 'Total')
        tree = ttk.Treeview(book_frame, columns=columns, show='headings', height=20)
        
        # Configure columns
        tree.heading('Side', text='Side')
        tree.heading('Price', text='Price (₹)')
        tree.heading('Quantity', text='Quantity')
        tree.heading('Orders', text='Orders')
        tree.heading('Total', text='Total Value (₹)')
        
        tree.column('Side', width=60, anchor=tk.CENTER)
        tree.column('Price', width=100, anchor=tk.E)
        tree.column('Quantity', width=100, anchor=tk.E)
        tree.column('Orders', width=80, anchor=tk.E)
        tree.column('Total', width=120, anchor=tk.E)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(book_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.order_book_trees[symbol] = tree
        
        # Configure row colors
        tree.tag_configure('ask', background='#ffcccc')  # Light red for asks
        tree.tag_configure('bid', background='#ccffcc')  # Light green for bids
        tree.tag_configure('spread', background='#ffffcc')  # Light yellow for spread
    
    def update_order_book(self, symbol, order_book):
        """Update order book display for a symbol"""
        if symbol not in self.current_order_books:
            return
            
        self.current_order_books[symbol] = order_book
        
        # Store in history
        self.order_book_history[symbol].append({
            'timestamp': order_book.timestamp,
            'bids': [(level.price, level.quantity, level.orders) for level in order_book.bids],
            'asks': [(level.price, level.quantity, level.orders) for level in order_book.asks]
        })
    
    def update_display_loop(self):
        """Update display every second"""
        while self.running:
            try:
                for symbol in self.symbols:
                    if self.current_order_books[symbol]:
                        self.update_symbol_display(symbol)
                time.sleep(1)  # Update every second
            except Exception as e:
                logger.error(f"Display update error: {e}")
                time.sleep(1)
    
    def update_symbol_display(self, symbol):
        """Update display for a specific symbol"""
        try:
            order_book = self.current_order_books[symbol]
            if not order_book:
                return
            
            tree = self.order_book_trees[symbol]
            
            # Clear existing items
            for item in tree.get_children():
                tree.delete(item)
            
            # Calculate statistics
            if order_book.bids and order_book.asks:
                best_bid = order_book.bids[0].price
                best_ask = order_book.asks[0].price
                spread = best_ask - best_bid
                spread_pct = (spread / best_bid) * 100
                
                # Calculate imbalance
                total_bid_qty = sum(level.quantity for level in order_book.bids[:5])
                total_ask_qty = sum(level.quantity for level in order_book.asks[:5])
                total_qty = total_bid_qty + total_ask_qty
                imbalance = (total_bid_qty - total_ask_qty) / total_qty * 100 if total_qty > 0 else 0
                
                # Update stats labels
                self.root.after(0, self.update_stats_labels, symbol, order_book.timestamp, 
                               spread, spread_pct, imbalance, total_qty)
            
            # Add ask levels (top to bottom, highest to lowest price)
            for i, ask in enumerate(order_book.asks[:10]):  # Top 10 ask levels
                total_value = ask.price * ask.quantity
                tree.insert('', 'end', values=(
                    'ASK',
                    f"{ask.price:.2f}",
                    f"{ask.quantity:,}",
                    f"{ask.orders}",
                    f"{total_value:,.0f}"
                ), tags=('ask',))
            
            # Add spread indicator
            if order_book.bids and order_book.asks:
                spread = order_book.asks[0].price - order_book.bids[0].price
                tree.insert('', 'end', values=(
                    'SPREAD',
                    f"{spread:.2f}",
                    '--',
                    '--',
                    '--'
                ), tags=('spread',))
            
            # Add bid levels (top to bottom, highest to lowest price)
            for i, bid in enumerate(order_book.bids[:10]):  # Top 10 bid levels
                total_value = bid.price * bid.quantity
                tree.insert('', 'end', values=(
                    'BID',
                    f"{bid.price:.2f}",
                    f"{bid.quantity:,}",
                    f"{bid.orders}",
                    f"{total_value:,.0f}"
                ), tags=('bid',))
                
        except Exception as e:
            logger.error(f"Symbol display update error: {e}")
    
    def update_stats_labels(self, symbol, timestamp, spread, spread_pct, imbalance, total_qty):
        """Update statistics labels"""
        try:
            labels = self.stats_labels[symbol]
            labels['last_update'].config(text=f"Last Update: {timestamp.strftime('%H:%M:%S')}")
            labels['spread'].config(text=f"Spread: ₹{spread:.2f} ({spread_pct:.3f}%)")
            labels['imbalance'].config(text=f"Imbalance: {imbalance:+.1f}%")
            labels['total_volume'].config(text=f"Total Volume: {total_qty:,}")
        except Exception as e:
            logger.error(f"Stats update error: {e}")
    
    def export_order_book_history(self, symbol, filename=None):
        """Export order book history to CSV"""
        if filename is None:
            filename = f"orderbook_history_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        try:
            history_data = []
            for record in self.order_book_history[symbol]:
                # Flatten the order book data
                for i, (price, qty, orders) in enumerate(record['bids'][:10]):
                    history_data.append({
                        'timestamp': record['timestamp'],
                        'side': 'BID',
                        'level': i + 1,
                        'price': price,
                        'quantity': qty,
                        'orders': orders
                    })
                
                for i, (price, qty, orders) in enumerate(record['asks'][:10]):
                    history_data.append({
                        'timestamp': record['timestamp'],
                        'side': 'ASK',
                        'level': i + 1,
                        'price': price,
                        'quantity': qty,
                        'orders': orders
                    })
            
            df = pd.DataFrame(history_data)
            df.to_csv(filename, index=False)
            logger.info(f"Order book history exported to {filename}")
            
        except Exception as e:
            logger.error(f"Export failed: {e}")
    
    def run(self):
        """Start the GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
    
    def on_closing(self):
        """Handle window closing"""
        self.running = False
        
        # Export order book history for all symbols
        for symbol in self.symbols:
            if len(self.order_book_history[symbol]) > 0:
                self.export_order_book_history(symbol)
        
        self.root.destroy()


class RealTimeOrderFlowSystem:
    """Main system for real-time order flow with GUI"""
    
    def __init__(self, credentials_file="smart_api_credentials.json"):
        # Load credentials
        try:
            with open(credentials_file, 'r') as f:
                self.credentials = json.load(f)
        except FileNotFoundError:
            logger.error("Credentials file not found. Run test_smart_api_auth.py first.")
            raise
        
        # Symbols to monitor
        self.symbols = ["RELIANCE", "TCS", "INFY", "HDFCBANK"]
        
        # Order flow analyzers
        self.analyzers = {symbol: AdvancedOrderFlowAnalyzer(symbol) for symbol in self.symbols}
        
        # GUI for order book
        self.order_book_gui = OrderBookGUI(self.symbols)
        
        # Data tracking
        self.tick_count = 0
        self.signal_count = 0
        self.last_tick_time = time.time()
        
        # Symbol tokens (example - need real ones)
        self.symbol_tokens = {
            "RELIANCE": "2885",
            "TCS": "11536",
            "INFY": "1594",
            "HDFCBANK": "1333"
        }
        
        # WebSocket connection
        self.ws_client = None
        self.is_connected = False
    
    def start_system(self):
        """Start the complete real-time system"""
        logger.info("🚀 Starting Real-Time Order Flow System...")
        
        # Start GUI in separate thread
        gui_thread = threading.Thread(target=self.order_book_gui.run, daemon=True)
        gui_thread.start()
        
        # Start WebSocket connection
        self.connect_websocket()
        
        # Start tick generation (simulated for demo)
        self.start_tick_simulation()
    
    def connect_websocket(self):
        """Connect to Smart API WebSocket"""
        # This would be the real WebSocket connection
        # For now, we'll simulate it
        logger.info("📡 Connecting to Smart API WebSocket...")
        self.is_connected = True
        logger.info("✅ WebSocket connected (simulated)")
    
    def start_tick_simulation(self):
        """Start tick simulation (replace with real WebSocket data)"""
        def simulate_ticks():
            base_prices = {
                "RELIANCE": 2500.0,
                "TCS": 3500.0,
                "INFY": 1800.0,
                "HDFCBANK": 1600.0
            }
            
            while True:
                try:
                    for symbol in self.symbols:
                        # Simulate tick every second
                        base_price = base_prices[symbol]
                        price = base_price + (time.time() % 100 - 50) * 0.1  # Small price movement
                        volume = int(1000 + (time.time() % 10) * 500)  # Varying volume
                        
                        # Create tick
                        tick = Tick(
                            timestamp=datetime.now(),
                            price=price,
                            volume=volume,
                            buyer_initiated=time.time() % 2 > 1  # Alternate buy/sell
                        )
                        
                        # Process tick
                        self.process_tick(symbol, tick)
                        
                        # Generate order book
                        order_book = self.generate_simulated_order_book(symbol, price)
                        self.process_order_book(symbol, order_book)
                    
                    time.sleep(1)  # Update every second
                    
                except Exception as e:
                    logger.error(f"Tick simulation error: {e}")
                    time.sleep(1)
        
        # Start simulation in thread
        sim_thread = threading.Thread(target=simulate_ticks, daemon=True)
        sim_thread.start()
    
    def process_tick(self, symbol, tick):
        """Process incoming tick data"""
        self.tick_count += 1
        
        # Process through order flow engine
        if symbol in self.analyzers:
            signal = self.analyzers[symbol].flow_engine.add_tick(tick)
            
            if signal:
                self.signal_count += 1
                self.handle_signal(symbol, signal, tick)
        
        # Log every 10th tick
        if self.tick_count % 10 == 0:
            logger.info(f"📊 Processed {self.tick_count} ticks, Generated {self.signal_count} signals")
    
    def process_order_book(self, symbol, order_book):
        """Process order book data"""
        # Update GUI
        self.order_book_gui.update_order_book(symbol, order_book)
        
        # Process through order flow engine
        if symbol in self.analyzers:
            self.analyzers[symbol].flow_engine.add_order_book(order_book)
    
    def generate_simulated_order_book(self, symbol, current_price):
        """Generate simulated order book (replace with real data)"""
        bids = []
        asks = []
        
        # Generate bid levels
        for i in range(10):
            price = current_price - (i + 1) * 0.05
            quantity = int(1000 + i * 500 + (time.time() % 10) * 100)
            orders = int(1 + i + (time.time() % 5))
            bids.append(OrderBookLevel(price=price, quantity=quantity, orders=orders))
        
        # Generate ask levels
        for i in range(10):
            price = current_price + (i + 1) * 0.05
            quantity = int(1000 + i * 500 + (time.time() % 10) * 100)
            orders = int(1 + i + (time.time() % 5))
            asks.append(OrderBookLevel(price=price, quantity=quantity, orders=orders))
        
        return OrderBook(
            timestamp=datetime.now(),
            bids=bids,
            asks=asks
        )
    
    def handle_signal(self, symbol, signal, tick):
        """Handle generated trading signal"""
        logger.info(f"🚨 SIGNAL #{self.signal_count}: {symbol} - {signal.signal_type} "
                   f"(Strength: {signal.strength:.2f}, Confidence: {signal.confidence:.2f})")
        
        # Print to console
        print(f"\n{'='*50}")
        print(f"📊 ORDER FLOW SIGNAL - {symbol}")
        print(f"{'='*50}")
        print(f"Time: {tick.timestamp.strftime('%H:%M:%S')}")
        print(f"Signal: {signal.signal_type}")
        print(f"Strength: {signal.strength:.2f}")
        print(f"Confidence: {signal.confidence:.2f}")
        print(f"Price: ₹{tick.price:.2f}")
        print(f"Volume: {tick.volume:,}")
        print(f"Reasons: {', '.join(signal.reasons)}")
        print(f"{'='*50}\n")


def main():
    """Main function"""
    print("🚀 Real-Time Order Flow System with GUI")
    print("=" * 60)
    print("Features:")
    print("✅ Every second tick updates")
    print("✅ Real-time order book GUI")
    print("✅ 5-minute order book history")
    print("✅ Live order flow analysis")
    print("=" * 60)
    
    try:
        # Create and start system
        system = RealTimeOrderFlowSystem()
        system.start_system()
        
        # Keep main thread alive
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  System stopped by user")
    except Exception as e:
        print(f"\n❌ System failed: {e}")


if __name__ == "__main__":
    main()
