"""
Data Source Manager
Handles switching between live WebSocket data and simulated data
"""

from enum import Enum
from typing import Callable, Optional
import threading

from core.websocket_manager import SmartAPIWebSocketManager
from core.market_simulator import CGCLMarketSimulator


class DataSourceType(Enum):
    """Types of data sources"""
    LIVE = "live"
    SIMULATED = "simulated"


class DataSourceManager:
    """Manages switching between live and simulated data sources"""
    
    def __init__(self):
        self.current_source = DataSourceType.SIMULATED  # Start with simulation
        self.websocket_manager = SmartAPIWebSocketManager()
        self.market_simulator = CGCLMarketSimulator()
        
        # Callbacks
        self.on_market_data: Optional[Callable] = None
        self.on_order_book_data: Optional[Callable] = None
        self.on_connection_status: Optional[Callable] = None
        
        # State
        self.is_connected = False
        self._lock = threading.RLock()
        
        # Setup callbacks
        self._setup_websocket_callbacks()
        self._setup_simulator_callbacks()
        
        print("🔄 Data Source Manager initialized")
    
    def _setup_websocket_callbacks(self):
        """Setup WebSocket manager callbacks"""
        self.websocket_manager.on_market_data = self._handle_live_market_data
        self.websocket_manager.on_order_book_data = self._handle_live_order_book_data
        self.websocket_manager.on_connection_status = self._handle_connection_status
    
    def _setup_simulator_callbacks(self):
        """Setup simulator callbacks"""
        self.market_simulator.on_market_data_update = self._handle_simulated_market_data
        self.market_simulator.on_order_book_update = self._handle_simulated_order_book_data
    
    def set_data_source(self, source_type: DataSourceType) -> bool:
        """Switch to specified data source"""
        with self._lock:
            if self.current_source == source_type:
                print(f"ℹ️ Already using {source_type.value} data source")
                return True
            
            print(f"🔄 Switching from {self.current_source.value} to {source_type.value}")
            
            # Stop current source
            self._stop_current_source()
            
            # Switch to new source
            self.current_source = source_type
            success = self._start_current_source()
            
            if success:
                print(f"✅ Successfully switched to {source_type.value} data source")
                if self.on_connection_status:
                    self.on_connection_status(f"CONNECTED_{source_type.value.upper()}")
            else:
                print(f"❌ Failed to switch to {source_type.value} data source")
                if self.on_connection_status:
                    self.on_connection_status("DISCONNECTED")
            
            return success
    
    def _stop_current_source(self):
        """Stop the current data source"""
        if self.current_source == DataSourceType.LIVE:
            self.websocket_manager.disconnect()
        elif self.current_source == DataSourceType.SIMULATED:
            self.market_simulator.stop_simulation()
        
        self.is_connected = False
    
    def _start_current_source(self) -> bool:
        """Start the current data source"""
        if self.current_source == DataSourceType.LIVE:
            success = self.websocket_manager.connect()
            if success:
                self.is_connected = True
            return success

        elif self.current_source == DataSourceType.SIMULATED:
            self.market_simulator.start_simulation()
            self.is_connected = True
            # Trigger connection status callback for simulation
            if self.on_connection_status:
                self.on_connection_status("CONNECTED_SIMULATED")
            return True

        return False
    
    def connect(self) -> bool:
        """Connect to current data source"""
        return self._start_current_source()
    
    def disconnect(self):
        """Disconnect from current data source"""
        self._stop_current_source()
        if self.on_connection_status:
            self.on_connection_status("DISCONNECTED")
    
    def toggle_connection(self) -> bool:
        """Toggle connection state"""
        if self.is_connected:
            self.disconnect()
            return False
        else:
            return self.connect()
    
    def get_current_source(self) -> DataSourceType:
        """Get current data source type"""
        return self.current_source
    
    def is_live_data(self) -> bool:
        """Check if currently using live data"""
        return self.current_source == DataSourceType.LIVE
    
    def is_simulated_data(self) -> bool:
        """Check if currently using simulated data"""
        return self.current_source == DataSourceType.SIMULATED
    
    def get_connection_status(self) -> str:
        """Get current connection status"""
        if not self.is_connected:
            return "DISCONNECTED"
        
        if self.current_source == DataSourceType.LIVE:
            return "CONNECTED_LIVE"
        else:
            return "CONNECTED_SIMULATED"
    
    # Live data handlers
    def _handle_live_market_data(self, symbol: str, market_data):
        """Handle market data from WebSocket"""
        if self.current_source == DataSourceType.LIVE and self.on_market_data:
            self.on_market_data(symbol, market_data)
    
    def _handle_live_order_book_data(self, symbol: str, order_book):
        """Handle order book data from WebSocket"""
        if self.current_source == DataSourceType.LIVE and self.on_order_book_data:
            self.on_order_book_data(symbol, order_book)
    
    def _handle_connection_status(self, status: str):
        """Handle WebSocket connection status"""
        if self.current_source == DataSourceType.LIVE:
            self.is_connected = (status == "CONNECTED")
            if self.on_connection_status:
                self.on_connection_status(f"{status}_LIVE" if status == "CONNECTED" else status)
    
    # Simulated data handlers
    def _handle_simulated_market_data(self, symbol: str, market_data):
        """Handle market data from simulator"""
        if self.current_source == DataSourceType.SIMULATED and self.on_market_data:
            self.on_market_data(symbol, market_data)
    
    def _handle_simulated_order_book_data(self, symbol: str, order_book):
        """Handle order book data from simulator"""
        if self.current_source == DataSourceType.SIMULATED and self.on_order_book_data:
            self.on_order_book_data(symbol, order_book)
    
    # Simulator controls
    def set_trend(self, strength: float):
        """Set trend strength for simulator"""
        if self.current_source == DataSourceType.SIMULATED:
            self.market_simulator.set_trend(strength)
    
    def create_volume_spike(self, multiplier: float = 3.0):
        """Create volume spike in simulator"""
        if self.current_source == DataSourceType.SIMULATED:
            self.market_simulator.create_volume_spike(multiplier)
    
    def get_simulator_stats(self) -> dict:
        """Get simulator statistics"""
        if self.current_source == DataSourceType.SIMULATED:
            return self.market_simulator.get_statistics()
        return {}
    
    def get_status_info(self) -> dict:
        """Get comprehensive status information"""
        return {
            'current_source': self.current_source.value,
            'is_connected': self.is_connected,
            'connection_status': self.get_connection_status(),
            'is_live': self.is_live_data(),
            'is_simulated': self.is_simulated_data(),
            'simulator_stats': self.get_simulator_stats() if self.is_simulated_data() else None
        }
