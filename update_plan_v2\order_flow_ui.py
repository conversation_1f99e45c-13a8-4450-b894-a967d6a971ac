"""
Order Flow Analysis UI Component
Professional order flow visualization for HFT platforms

Features:
- Real-time order flow velocity display
- Enhanced spread analysis visualization
- Order book imbalance scoring
- Psychological level indicators
- Professional HFT-style layout
"""

import tkinter as tk
import customtkinter as ctk
from typing import Dict, Optional


class OrderFlowUI:
    """
    Professional Order Flow Analysis UI Component
    
    Displays real-time order flow metrics in a clean, professional layout
    suitable for high-frequency trading platforms.
    """
    
    def __init__(self, parent_frame):
        """
        Initialize Order Flow UI

        Args:
            parent_frame: Parent tkinter frame to contain this component
        """
        self.parent_frame = parent_frame
        self.main_frame = None

        # UI Components
        self.velocity_labels = {}
        self.spread_labels = {}
        self.imbalance_labels = {}
        self.liquidity_labels = {}
        self.summary_labels = {}

        # Level selection for Order Flow Analysis (independent from other UIs)
        self.current_levels = 10  # Default to 10 levels
        self.level_selector = None

        # Create the UI
        self.create_ui()

        print("🎯 [ORDER_FLOW_UI] Order Flow UI initialized")
        print(f"📊 [ORDER_FLOW_UI] Default analysis levels: {self.current_levels}")
    
    def create_ui(self):
        """Create the main order flow analysis UI"""
        # Main container frame
        self.main_frame = ctk.CTkFrame(self.parent_frame, fg_color="#1a1a1a", corner_radius=8)
        self.main_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Header with level selector
        header_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        header_label = ctk.CTkLabel(
            header_frame,
            text="🔬 ORDER FLOW ANALYSIS",
            font=ctk.CTkFont(family="Segoe UI", size=14, weight="bold"),
            text_color="#00ff88"
        )
        header_label.pack(side=tk.LEFT)

        # Level selector (independent for Order Flow Analysis)
        self.create_level_selector(header_frame)
        
        # Create sections in a grid layout
        content_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        content_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # Configure grid
        content_frame.grid_columnconfigure(0, weight=1)
        content_frame.grid_columnconfigure(1, weight=1)
        content_frame.grid_columnconfigure(2, weight=1)
        content_frame.grid_columnconfigure(3, weight=1)
        
        # Create sections
        self.create_velocity_section(content_frame, row=0, column=0)
        self.create_spread_section(content_frame, row=0, column=1)
        self.create_imbalance_section(content_frame, row=0, column=2)
        self.create_summary_section(content_frame, row=0, column=3)

    def create_level_selector(self, parent_frame):
        """Create independent level selector for Order Flow Analysis"""
        # Level selector container
        level_frame = ctk.CTkFrame(parent_frame, fg_color="transparent")
        level_frame.pack(side=tk.RIGHT, padx=(10, 0))

        # Level label
        level_label = ctk.CTkLabel(
            level_frame,
            text="📊 Levels:",
            font=ctk.CTkFont(family="Segoe UI", size=11, weight="bold"),
            text_color="#aaaaaa"
        )
        level_label.pack(side=tk.LEFT, padx=(0, 5))

        # Level selector dropdown
        self.level_selector = ctk.CTkOptionMenu(
            level_frame,
            values=["5", "10", "15", "20"],
            command=self.on_level_change,
            width=60,
            height=28,
            font=ctk.CTkFont(family="Segoe UI", size=10, weight="bold"),
            dropdown_font=ctk.CTkFont(family="Segoe UI", size=10),
            fg_color="#333333",
            button_color="#444444",
            button_hover_color="#555555",
            dropdown_fg_color="#333333",
            dropdown_hover_color="#444444",
            text_color="#ffffff",
            dropdown_text_color="#ffffff"
        )
        self.level_selector.set("10")  # Default to 10 levels
        self.level_selector.pack(side=tk.LEFT)

    def on_level_change(self, selected_level):
        """Handle level selection change for Order Flow Analysis"""
        try:
            self.current_levels = int(selected_level)
            print(f"📊 [ORDER_FLOW_UI] Analysis levels changed to: {self.current_levels}")

            # Update level display in UI (visual feedback)
            if hasattr(self, 'level_indicator'):
                self.level_indicator.configure(text=f"({self.current_levels}L)")

            # Force immediate update of analysis with new levels
            print(f"🔄 [ORDER_FLOW_UI] Forcing analysis update with {self.current_levels} levels")

        except ValueError:
            print(f"⚠️ [ORDER_FLOW_UI] Invalid level selection: {selected_level}")
            self.current_levels = 10  # Fallback to default
    
    def create_velocity_section(self, parent, row, column):
        """Create order flow velocity section"""
        section_frame = ctk.CTkFrame(parent, fg_color="#2a2a2a", corner_radius=6)
        section_frame.grid(row=row, column=column, padx=5, pady=5, sticky="nsew")

        # Section header with level indicator
        header_container = ctk.CTkFrame(section_frame, fg_color="transparent")
        header_container.pack(pady=(10, 8))

        header = ctk.CTkLabel(
            header_container,
            text="⚡ FLOW VELOCITY",
            font=ctk.CTkFont(family="Segoe UI", size=12, weight="bold"),
            text_color="#ffaa00"
        )
        header.pack(side=tk.LEFT)

        # Level indicator
        self.level_indicator = ctk.CTkLabel(
            header_container,
            text="(10L)",
            font=ctk.CTkFont(family="Segoe UI", size=9, weight="bold"),
            text_color="#888888"
        )
        self.level_indicator.pack(side=tk.LEFT, padx=(5, 0))

        # Velocity metrics with improved labels
        self.velocity_labels['total'] = self.create_metric_label(section_frame, "Flow Rate:", "0.0/s")
        self.velocity_labels['bid'] = self.create_metric_label(section_frame, "Bid Flow:", "0.0/s")
        self.velocity_labels['ask'] = self.create_metric_label(section_frame, "Ask Flow:", "0.0/s")
        self.velocity_labels['trend'] = self.create_metric_label(section_frame, "Trend:", "Neutral", "#888888")

        # Enhanced roadmap features
        self.velocity_labels['intensity'] = self.create_metric_label(section_frame, "Intensity:", "Low", "#ffaa00")
        self.velocity_labels['addition_rate'] = self.create_metric_label(section_frame, "Add Rate:", "0.0/s", "#88ff88")
    
    def create_spread_section(self, parent, row, column):
        """Create enhanced spread analysis section"""
        section_frame = ctk.CTkFrame(parent, fg_color="#2a2a2a", corner_radius=6)
        section_frame.grid(row=row, column=column, padx=5, pady=5, sticky="nsew")

        # Section header
        header = ctk.CTkLabel(
            section_frame,
            text="📊 SPREAD ANALYSIS",
            font=ctk.CTkFont(family="Segoe UI", size=12, weight="bold"),
            text_color="#00aaff"
        )
        header.pack(pady=(10, 8))

        # Spread metrics with improved labels
        self.spread_labels['current'] = self.create_metric_label(section_frame, "Spread:", "₹0.00")
        self.spread_labels['bps'] = self.create_metric_label(section_frame, "Basis Pts:", "0.0")
        self.spread_labels['percentile'] = self.create_metric_label(section_frame, "Percentile:", "50%")
        self.spread_labels['anomaly'] = self.create_metric_label(section_frame, "Alert:", "Normal", "#888888")

        # Enhanced roadmap features
        self.spread_labels['quality'] = self.create_metric_label(section_frame, "Quality:", "50/100", "#00aaff")
        self.spread_labels['efficiency'] = self.create_metric_label(section_frame, "Efficiency:", "1.0x", "#88ff88")
    
    def create_imbalance_section(self, parent, row, column):
        """Create order book imbalance section"""
        section_frame = ctk.CTkFrame(parent, fg_color="#2a2a2a", corner_radius=6)
        section_frame.grid(row=row, column=column, padx=5, pady=5, sticky="nsew")

        # Section header
        header = ctk.CTkLabel(
            section_frame,
            text="⚖️ ORDER BALANCE",
            font=ctk.CTkFont(family="Segoe UI", size=12, weight="bold"),
            text_color="#ff6600"
        )
        header.pack(pady=(10, 8))

        # Imbalance metrics with improved labels
        self.imbalance_labels['score'] = self.create_metric_label(section_frame, "Balance:", "50.0")
        self.imbalance_labels['bias'] = self.create_metric_label(section_frame, "Direction:", "Neutral", "#888888")
        self.imbalance_labels['strength'] = self.create_metric_label(section_frame, "Strength:", "Weak")
        self.imbalance_labels['momentum'] = self.create_metric_label(section_frame, "Momentum:", "Stable", "#888888")

        # Enhanced roadmap features - Week 3-4
        self.imbalance_labels['confidence'] = self.create_metric_label(section_frame, "Confidence:", "0%", "#00aaff")
        self.imbalance_labels['pressure'] = self.create_metric_label(section_frame, "Pressure:", "Low", "#ffaa00")
    
    def create_summary_section(self, parent, row, column):
        """Create overall summary section"""
        section_frame = ctk.CTkFrame(parent, fg_color="#2a2a2a", corner_radius=6)
        section_frame.grid(row=row, column=column, padx=5, pady=5, sticky="nsew")

        # Section header
        header = ctk.CTkLabel(
            section_frame,
            text="🎯 MARKET SIGNAL",
            font=ctk.CTkFont(family="Segoe UI", size=12, weight="bold"),
            text_color="#ff0088"
        )
        header.pack(pady=(10, 8))

        # Summary metrics with improved labels
        self.summary_labels['sentiment'] = self.create_metric_label(section_frame, "Sentiment:", "Neutral", "#888888")
        self.summary_labels['confidence'] = self.create_metric_label(section_frame, "Confidence:", "Medium")
        self.summary_labels['risk'] = self.create_metric_label(section_frame, "Risk Level:", "Moderate")
        self.summary_labels['signals'] = self.create_metric_label(section_frame, "Alerts:", "0", "#888888")

        # Enhanced roadmap features - Week 3-4 Psychological Levels
        self.summary_labels['nearest_level'] = self.create_metric_label(section_frame, "Near Level:", "₹850", "#ff88ff")
        self.summary_labels['level_distance'] = self.create_metric_label(section_frame, "Distance:", "0.0", "#ff88ff")
    
    def create_metric_label(self, parent, label_text: str, value_text: str, color: str = "#ffffff") -> ctk.CTkLabel:
        """Create a metric label with fixed layout to prevent shifting"""
        # Main container with fixed height
        container = ctk.CTkFrame(parent, fg_color="transparent", height=28)
        container.pack(fill=tk.X, padx=10, pady=3)
        container.pack_propagate(False)  # Prevent container from shrinking

        # Label container (left side) - fixed width
        label_container = ctk.CTkFrame(container, fg_color="transparent", width=80)
        label_container.pack(side=tk.LEFT, fill=tk.Y)
        label_container.pack_propagate(False)

        # Label text
        label = ctk.CTkLabel(
            label_container,
            text=label_text,
            font=ctk.CTkFont(family="Segoe UI", size=10),
            text_color="#bbbbbb",
            anchor="w"  # Left align
        )
        label.pack(side=tk.LEFT, fill=tk.BOTH, padx=(2, 0))

        # Value container (right side) - fixed width with monospace font
        value_container = ctk.CTkFrame(container, fg_color="transparent", width=70)
        value_container.pack(side=tk.RIGHT, fill=tk.Y)
        value_container.pack_propagate(False)

        # Value text with monospace font to prevent shifting
        value_label = ctk.CTkLabel(
            value_container,
            text=value_text,
            font=ctk.CTkFont(family="Consolas", size=10, weight="bold"),  # Monospace font
            text_color=color,
            anchor="e"  # Right align
        )
        value_label.pack(side=tk.RIGHT, fill=tk.BOTH, padx=(0, 2))

        return value_label
    
    def update_order_flow_data(self, analysis_result: Dict):
        """
        Update the UI with new order flow analysis data
        
        Args:
            analysis_result: Dictionary containing order flow analysis results
        """
        try:
            # Update velocity section
            velocity = analysis_result.get('velocity', {})
            self.velocity_labels['total'].configure(text=f"{velocity.get('total_velocity', 0):.1f}/s")
            self.velocity_labels['bid'].configure(text=f"{velocity.get('bid_velocity', 0):.1f}/s")
            self.velocity_labels['ask'].configure(text=f"{velocity.get('ask_velocity', 0):.1f}/s")
            
            trend = velocity.get('velocity_trend', 'neutral').title()
            trend_color = self.get_trend_color(trend.lower())
            self.velocity_labels['trend'].configure(text=trend, text_color=trend_color)

            # Enhanced roadmap velocity features
            intensity = velocity.get('velocity_intensity', 'low').title()
            intensity_color = self.get_intensity_color(intensity.lower())
            self.velocity_labels['intensity'].configure(text=intensity, text_color=intensity_color)

            add_rate = velocity.get('order_addition_rate', 0)
            self.velocity_labels['addition_rate'].configure(text=f"{add_rate:.1f}/s")
            
            # Update spread section
            spread = analysis_result.get('spread', {})
            self.spread_labels['current'].configure(text=f"₹{spread.get('current_spread', 0):.2f}")
            self.spread_labels['bps'].configure(text=f"{spread.get('spread_bps', 0):.1f}")
            self.spread_labels['percentile'].configure(text=f"{spread.get('spread_percentile', 50):.0f}%")
            
            anomaly = "Alert" if spread.get('spread_anomaly', False) else "Normal"
            anomaly_color = "#ff4444" if spread.get('spread_anomaly', False) else "#00ff88"
            self.spread_labels['anomaly'].configure(text=anomaly, text_color=anomaly_color)

            # Enhanced roadmap spread features
            quality_score = spread.get('liquidity_quality_score', 50)
            self.spread_labels['quality'].configure(text=f"{quality_score:.0f}/100")

            efficiency = spread.get('spread_efficiency_ratio', 1.0)
            self.spread_labels['efficiency'].configure(text=f"{efficiency:.1f}x")
            
            # Update imbalance section
            imbalance = analysis_result.get('imbalance', {})
            score = imbalance.get('imbalance_score', 50)
            self.imbalance_labels['score'].configure(text=f"{score:.1f}")
            
            bias = imbalance.get('directional_bias', 'neutral').title()
            bias_color = self.get_bias_color(bias.lower())
            self.imbalance_labels['bias'].configure(text=bias, text_color=bias_color)

            strength = imbalance.get('imbalance_strength', 'weak').title()
            self.imbalance_labels['strength'].configure(text=strength)

            momentum = "Shift" if imbalance.get('momentum_shift', False) else "Stable"
            momentum_color = "#ffaa00" if imbalance.get('momentum_shift', False) else "#888888"
            self.imbalance_labels['momentum'].configure(text=momentum, text_color=momentum_color)

            # Enhanced roadmap features - Week 3-4
            confidence = imbalance.get('bias_confidence', 0)
            self.imbalance_labels['confidence'].configure(text=f"{confidence:.0f}%")

            pressure = imbalance.get('pressure_intensity', 'low').title()
            pressure_color = self.get_pressure_color(pressure.lower())
            self.imbalance_labels['pressure'].configure(text=pressure, text_color=pressure_color)
            
            # Update summary section
            summary = analysis_result.get('summary', {})
            sentiment = summary.get('overall_sentiment', 'neutral').title()
            sentiment_color = self.get_sentiment_color(sentiment.lower())
            self.summary_labels['sentiment'].configure(text=sentiment, text_color=sentiment_color)

            confidence = summary.get('confidence_level', 'medium').title()
            self.summary_labels['confidence'].configure(text=confidence)

            risk = summary.get('risk_level', 'moderate').title()
            risk_color = self.get_risk_color(risk.lower())
            self.summary_labels['risk'].configure(text=risk, text_color=risk_color)
            
            signal_count = len(summary.get('key_signals', []))
            self.summary_labels['signals'].configure(text=str(signal_count))

            # Enhanced roadmap features - Week 3-4 Psychological Levels
            psychological = analysis_result.get('psychological', {})
            nearest_level = psychological.get('nearest_major_level', 0)
            self.summary_labels['nearest_level'].configure(text=f"₹{nearest_level:.0f}")

            level_distance = psychological.get('level_distance', 0)
            self.summary_labels['level_distance'].configure(text=f"{level_distance:.1f}")
            
        except Exception as e:
            print(f"⚠️ [ORDER_FLOW_UI] Error updating UI: {e}")
            import traceback
            traceback.print_exc()
    
    def get_trend_color(self, trend: str) -> str:
        """Get color for velocity trend"""
        colors = {
            'increasing': '#00ff88',
            'decreasing': '#ff4444',
            'neutral': '#888888'
        }
        return colors.get(trend, '#888888')
    
    def get_bias_color(self, bias: str) -> str:
        """Get color for directional bias"""
        colors = {
            'bullish': '#00ff88',
            'bearish': '#ff4444',
            'neutral': '#888888'
        }
        return colors.get(bias, '#888888')

    def get_intensity_color(self, intensity: str) -> str:
        """Get color for velocity intensity (roadmap feature)"""
        colors = {
            'low': '#888888',
            'medium': '#ffaa00',
            'high': '#ff6600',
            'extreme': '#ff0000'
        }
        return colors.get(intensity, '#888888')

    def get_pressure_color(self, pressure: str) -> str:
        """Get color for pressure intensity (roadmap feature)"""
        colors = {
            'low': '#888888',
            'medium': '#ffaa00',
            'high': '#ff6600',
            'extreme': '#ff0000'
        }
        return colors.get(pressure, '#888888')
    
    def get_sentiment_color(self, sentiment: str) -> str:
        """Get color for overall sentiment"""
        colors = {
            'bullish': '#00ff88',
            'bearish': '#ff4444',
            'neutral': '#888888'
        }
        return colors.get(sentiment, '#888888')
    
    def get_risk_color(self, risk: str) -> str:
        """Get color for risk level"""
        colors = {
            'low': '#00ff88',
            'moderate': '#ffaa00',
            'elevated': '#ff6600',
            'high': '#ff4444'
        }
        return colors.get(risk, '#ffaa00')
    
    def reset_display(self):
        """Reset all displays to default values"""
        try:
            # Reset velocity
            self.velocity_labels['total'].configure(text="0.0/s")
            self.velocity_labels['bid'].configure(text="0.0/s")
            self.velocity_labels['ask'].configure(text="0.0/s")
            self.velocity_labels['trend'].configure(text="Neutral", text_color="#888888")

            # Reset spread
            self.spread_labels['current'].configure(text="₹0.00")
            self.spread_labels['bps'].configure(text="0.0")
            self.spread_labels['percentile'].configure(text="50%")
            self.spread_labels['anomaly'].configure(text="Normal", text_color="#00ff88")

            # Reset imbalance
            self.imbalance_labels['score'].configure(text="50.0")
            self.imbalance_labels['bias'].configure(text="Neutral", text_color="#888888")
            self.imbalance_labels['strength'].configure(text="Weak")
            self.imbalance_labels['momentum'].configure(text="Stable", text_color="#888888")

            # Reset summary
            self.summary_labels['sentiment'].configure(text="Neutral", text_color="#888888")
            self.summary_labels['confidence'].configure(text="Medium")
            self.summary_labels['risk'].configure(text="Moderate")
            self.summary_labels['signals'].configure(text="0", text_color="#888888")

        except Exception as e:
            print(f"⚠️ [ORDER_FLOW_UI] Error resetting display: {e}")

    def get_selected_levels(self):
        """Get the currently selected number of levels for analysis"""
        return self.current_levels

    def set_levels(self, levels):
        """Set the number of levels programmatically"""
        if levels in [5, 10, 15, 20]:
            self.current_levels = levels
            if self.level_selector:
                self.level_selector.set(str(levels))
            if hasattr(self, 'level_indicator'):
                self.level_indicator.configure(text=f"({levels}L)")
            print(f"📊 [ORDER_FLOW_UI] Levels set to: {levels}")
        else:
            print(f"⚠️ [ORDER_FLOW_UI] Invalid level value: {levels}")
