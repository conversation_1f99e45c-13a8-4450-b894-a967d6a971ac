"""
Standalone Component Demo - Full Space Utilization
All charts in separate rows, stretching full width
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np

# Professional color scheme
COLORS = {
    'bg_primary': '#0a0a0a',
    'bg_secondary': '#1a1a1a',
    'bg_tertiary': '#2a2a2a',
    'bg_accent': '#3a3a3a',
    'text_primary': '#ffffff',
    'text_secondary': '#b0b0b0',
    'text_muted': '#808080',
    'accent_blue': '#00d4ff',
    'accent_green': '#00ff88',
    'accent_red': '#ff4757',
    'accent_orange': '#ffa726',
    'accent_purple': '#9c27b0',
    'border_light': '#404040',
}

def create_header(parent):
    """Create header with minimal padding"""
    header_frame = tk.Frame(parent, bg=COLORS['bg_secondary'], height=100)
    header_frame.pack(fill=tk.X, pady=(0, 5))
    header_frame.pack_propagate(False)
    
    # Title
    title_label = tk.Label(header_frame,
                          text="CGCL TRADING ANALYTICS - FULL SPACE UTILIZATION",
                          bg=COLORS['bg_secondary'],
                          fg=COLORS['accent_blue'],
                          font=('Segoe UI', 20, 'bold'))
    title_label.pack(pady=20)
    
    # Metrics row
    metrics_frame = tk.Frame(header_frame, bg=COLORS['bg_secondary'])
    metrics_frame.pack(fill=tk.X, padx=20)
    
    metrics = [("Price: ₹184.75", COLORS['accent_green']), 
               ("Change: +2.45%", COLORS['accent_green']),
               ("Volume: 1.2M", COLORS['accent_blue']), 
               ("Status: LIVE", COLORS['accent_red'])]
    
    for text, color in metrics:
        label = tk.Label(metrics_frame, text=text, bg=COLORS['bg_secondary'], 
                        fg=color, font=('Segoe UI', 12, 'bold'))
        label.pack(side=tk.LEFT, padx=20)

def create_chart_row(parent, title, chart_type, height=180):
    """Create a full-width chart row"""
    # Chart frame
    chart_frame = tk.Frame(parent, bg=COLORS['bg_secondary'], height=height)
    chart_frame.pack(fill=tk.X, pady=2)
    chart_frame.pack_propagate(False)
    
    # Title
    title_frame = tk.Frame(chart_frame, bg=COLORS['bg_secondary'], height=25)
    title_frame.pack(fill=tk.X, padx=5, pady=(2, 0))
    title_frame.pack_propagate(False)
    
    title_label = tk.Label(title_frame, text=title, bg=COLORS['bg_secondary'],
                          fg=COLORS['accent_blue'], font=('Segoe UI', 11, 'bold'))
    title_label.pack(side=tk.LEFT, pady=2)
    
    # Chart area
    chart_area = tk.Frame(chart_frame, bg=COLORS['bg_tertiary'])
    chart_area.pack(fill=tk.BOTH, expand=True, padx=5, pady=2)
    
    # Create chart
    if chart_type == "order_book":
        create_order_book(chart_area)
    elif chart_type == "cvd":
        create_cvd_chart(chart_area)
    elif chart_type == "volume_profile":
        create_volume_profile(chart_area)
    elif chart_type == "footprint":
        create_footprint(chart_area)
    elif chart_type == "order_flow":
        create_order_flow(chart_area)

def create_order_book(parent):
    """Order book display"""
    # Headers
    header_frame = tk.Frame(parent, bg=COLORS['bg_accent'], height=25)
    header_frame.pack(fill=tk.X)
    header_frame.pack_propagate(False)
    
    headers = ["Bid Qty", "Bid Orders", "Bid Price", "Ask Price", "Ask Orders", "Ask Qty"]
    colors = [COLORS['accent_green'], COLORS['text_secondary'], COLORS['accent_green'],
              COLORS['accent_red'], COLORS['text_secondary'], COLORS['accent_red']]
    
    for header, color in zip(headers, colors):
        tk.Label(header_frame, text=header, bg=COLORS['bg_accent'], fg=color,
                font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT, fill=tk.X, expand=True)
    
    # Sample data rows
    bids = [(1250, 15, 184.70), (890, 12, 184.65), (1100, 18, 184.60)]
    asks = [(184.75, 10, 980), (184.80, 16, 1200), (184.85, 12, 850)]
    
    for i in range(3):
        row_frame = tk.Frame(parent, bg=COLORS['bg_secondary'], height=20)
        row_frame.pack(fill=tk.X, pady=1)
        row_frame.pack_propagate(False)
        
        # Bid data
        if i < len(bids):
            qty, orders, price = bids[i]
            tk.Label(row_frame, text=f"{qty:,}", bg=COLORS['bg_secondary'], 
                    fg=COLORS['accent_green'], font=('Consolas', 9)).pack(side=tk.LEFT, fill=tk.X, expand=True)
            tk.Label(row_frame, text=str(orders), bg=COLORS['bg_secondary'], 
                    fg=COLORS['text_secondary'], font=('Consolas', 9)).pack(side=tk.LEFT, fill=tk.X, expand=True)
            tk.Label(row_frame, text=f"₹{price:.2f}", bg=COLORS['bg_secondary'], 
                    fg=COLORS['accent_green'], font=('Consolas', 9, 'bold')).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Ask data
        if i < len(asks):
            price, orders, qty = asks[i]
            tk.Label(row_frame, text=f"₹{price:.2f}", bg=COLORS['bg_secondary'], 
                    fg=COLORS['accent_red'], font=('Consolas', 9, 'bold')).pack(side=tk.LEFT, fill=tk.X, expand=True)
            tk.Label(row_frame, text=str(orders), bg=COLORS['bg_secondary'], 
                    fg=COLORS['text_secondary'], font=('Consolas', 9)).pack(side=tk.LEFT, fill=tk.X, expand=True)
            tk.Label(row_frame, text=f"{qty:,}", bg=COLORS['bg_secondary'], 
                    fg=COLORS['accent_red'], font=('Consolas', 9)).pack(side=tk.LEFT, fill=tk.X, expand=True)

def create_cvd_chart(parent):
    """CVD chart"""
    fig = Figure(figsize=(18, 2), facecolor=COLORS['bg_tertiary'])
    ax = fig.add_subplot(111)
    ax.set_facecolor(COLORS['bg_tertiary'])
    
    times = np.arange(0, 60, 1)
    price = 184.75 + np.cumsum(np.random.randn(60) * 0.02)
    cvd = np.cumsum(np.random.randn(60) * 100)
    
    ax_twin = ax.twinx()
    ax.plot(times, price, color=COLORS['accent_blue'], linewidth=2, label='Price')
    ax_twin.plot(times, cvd, color=COLORS['accent_orange'], linewidth=2, label='CVD')
    
    ax.set_ylabel('Price (₹)', color=COLORS['text_secondary'], fontsize=9)
    ax_twin.set_ylabel('CVD', color=COLORS['text_secondary'], fontsize=9)
    ax.tick_params(colors=COLORS['text_secondary'], labelsize=8)
    ax_twin.tick_params(colors=COLORS['text_secondary'], labelsize=8)
    ax.grid(True, alpha=0.3)
    
    fig.tight_layout()
    canvas = FigureCanvasTkAgg(fig, parent)
    canvas.draw()
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

def create_volume_profile(parent):
    """Volume profile chart"""
    fig = Figure(figsize=(18, 2), facecolor=COLORS['bg_tertiary'])
    ax = fig.add_subplot(111)
    ax.set_facecolor(COLORS['bg_tertiary'])
    
    prices = np.linspace(184.00, 185.50, 20)
    volumes = np.random.gamma(2, 500, 20)
    
    bars = ax.barh(prices, volumes, color=COLORS['accent_blue'], alpha=0.7, height=0.06)
    hvn_index = np.argmax(volumes)
    bars[hvn_index].set_color(COLORS['accent_green'])
    
    ax.set_xlabel('Volume', color=COLORS['text_secondary'], fontsize=9)
    ax.set_ylabel('Price (₹)', color=COLORS['text_secondary'], fontsize=9)
    ax.tick_params(colors=COLORS['text_secondary'], labelsize=8)
    ax.grid(True, alpha=0.3)
    
    fig.tight_layout()
    canvas = FigureCanvasTkAgg(fig, parent)
    canvas.draw()
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

def create_footprint(parent):
    """Footprint chart"""
    fig = Figure(figsize=(18, 2), facecolor=COLORS['bg_tertiary'])
    ax = fig.add_subplot(111)
    ax.set_facecolor(COLORS['bg_tertiary'])
    
    prices = np.linspace(184.50, 185.00, 8)
    bid_vols = np.random.randint(100, 1000, 8)
    ask_vols = np.random.randint(100, 1000, 8)
    
    ax.barh(prices, -bid_vols, color=COLORS['accent_green'], alpha=0.7, height=0.05, label='Bid')
    ax.barh(prices, ask_vols, color=COLORS['accent_red'], alpha=0.7, height=0.05, label='Ask')
    
    ax.axvline(x=0, color=COLORS['text_secondary'], alpha=0.5)
    ax.set_xlabel('Volume (Bid ← | → Ask)', color=COLORS['text_secondary'], fontsize=9)
    ax.set_ylabel('Price (₹)', color=COLORS['text_secondary'], fontsize=9)
    ax.tick_params(colors=COLORS['text_secondary'], labelsize=8)
    ax.legend(fontsize=8)
    ax.grid(True, alpha=0.3)
    
    fig.tight_layout()
    canvas = FigureCanvasTkAgg(fig, parent)
    canvas.draw()
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

def create_order_flow(parent):
    """Order flow chart"""
    fig = Figure(figsize=(18, 2), facecolor=COLORS['bg_tertiary'])
    ax = fig.add_subplot(111)
    ax.set_facecolor(COLORS['bg_tertiary'])
    
    times = np.arange(0, 20, 1)
    buying = np.array([1, 1.2, 1.5, 1.8, 2.0, 1.9, 1.7, 1.5, 1.2, 1.0,
                      0.8, 0.6, 0.4, 0.2, 0.1, -0.1, -0.3, -0.5, -0.7, -0.9])
    
    ax.plot(times, buying, color=COLORS['accent_green'], linewidth=3, label='Order Flow')
    ax.axhline(y=0, color=COLORS['text_secondary'], alpha=0.5)
    ax.axvspan(8, 12, alpha=0.2, color=COLORS['accent_orange'], label='Exhaustion Zone')
    
    ax.set_xlabel('Time', color=COLORS['text_secondary'], fontsize=9)
    ax.set_ylabel('Flow Strength', color=COLORS['text_secondary'], fontsize=9)
    ax.tick_params(colors=COLORS['text_secondary'], labelsize=8)
    ax.legend(fontsize=8)
    ax.grid(True, alpha=0.3)
    
    fig.tight_layout()
    canvas = FigureCanvasTkAgg(fig, parent)
    canvas.draw()
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

def main():
    """Main demo function"""
    root = tk.Tk()
    root.title("CGCL Trading Dashboard - Full Space Utilization")
    root.geometry("1400x900")
    root.configure(bg=COLORS['bg_primary'])
    root.state('zoomed')
    
    # Main container - minimal padding
    main_frame = tk.Frame(root, bg=COLORS['bg_primary'])
    main_frame.pack(fill=tk.BOTH, expand=True, padx=3, pady=3)
    
    # Header
    create_header(main_frame)
    
    # Charts - each in separate row, full width
    print("📊 Creating Order Book...")
    create_chart_row(main_frame, "📊 Professional Order Book", "order_book", 120)
    
    print("📊 Creating CVD Analysis...")
    create_chart_row(main_frame, "📈 Cumulative Volume Delta (CVD) Analysis", "cvd", 160)
    
    print("📊 Creating Volume Profile...")
    create_chart_row(main_frame, "📊 Volume Profile (Fair Value Analysis)", "volume_profile", 160)
    
    print("📊 Creating Footprint Analysis...")
    create_chart_row(main_frame, "🦶 Footprint Analysis (Bid/Ask Imbalances)", "footprint", 160)
    
    print("📊 Creating Order Flow...")
    create_chart_row(main_frame, "🌊 Order Flow Exhaustion Patterns", "order_flow", 160)
    
    print("✅ All charts loaded - Full space utilization achieved!")
    print("🎯 Each chart stretches full width with readable data")
    
    root.mainloop()

if __name__ == "__main__":
    main()
