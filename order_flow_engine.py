"""
Advanced Order Flow Analysis Engine
===================================

This module provides sophisticated real-time order flow analysis for detecting
institutional activity and market microstructure patterns.

Key Features:
- Real-time tick-by-tick analysis
- Order book imbalance detection
- Volume profile analysis
- Large order detection
- VWAP analysis and deviations
- Buying/selling pressure calculation
- Market microstructure insights
"""

import numpy as np
import pandas as pd
from collections import deque, defaultdict
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
import time
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Tick:
    """Individual tick data structure"""
    timestamp: datetime
    price: float
    volume: int
    buyer_initiated: bool  # True if trade was buyer initiated
    
@dataclass
class OrderBookLevel:
    """Order book level data"""
    price: float
    quantity: int
    orders: int

@dataclass
class OrderBook:
    """Complete order book snapshot"""
    timestamp: datetime
    bids: List[OrderBookLevel]  # Sorted highest to lowest
    asks: List[OrderBookLevel]  # Sorted lowest to highest
    
@dataclass
class FlowSignal:
    """Order flow signal output"""
    timestamp: datetime
    signal_type: str  # 'BUY', 'SELL', 'NEUTRAL'
    strength: float  # 0.0 to 1.0
    confidence: float  # 0.0 to 1.0
    reasons: List[str]  # Why this signal was generated
    
class OrderFlowEngine:
    """
    Advanced Order Flow Analysis Engine
    
    Analyzes real-time market data to detect institutional activity
    and generate trading signals based on order flow patterns.
    """
    
    def __init__(self, symbol: str, lookback_minutes: int = 30):
        self.symbol = symbol
        self.lookback_minutes = lookback_minutes
        
        # Data storage
        self.ticks = deque(maxlen=10000)  # Store last 10k ticks
        self.order_books = deque(maxlen=1000)  # Store last 1k order book snapshots
        
        # Analysis parameters
        self.volume_spike_threshold = 2.0  # 2x average volume
        self.large_order_threshold = 1000000  # ₹10 lakh
        self.imbalance_threshold = 0.7  # 70% imbalance
        self.vwap_deviation_threshold = 0.005  # 0.5%
        
        # Real-time calculations
        self.current_vwap = 0.0
        self.volume_profile = defaultdict(int)
        self.cumulative_delta = 0  # Buying pressure - Selling pressure
        
        # Performance tracking
        self.last_analysis_time = time.time()
        self.analysis_count = 0
        
    def add_tick(self, tick: Tick) -> Optional[FlowSignal]:
        """
        Add new tick data and analyze order flow
        
        Args:
            tick: New tick data
            
        Returns:
            FlowSignal if significant pattern detected, None otherwise
        """
        self.ticks.append(tick)
        self._update_vwap(tick)
        self._update_volume_profile(tick)
        self._update_cumulative_delta(tick)
        
        # Generate signal if conditions met
        return self._analyze_flow()
    
    def add_order_book(self, order_book: OrderBook) -> None:
        """Add new order book snapshot"""
        self.order_books.append(order_book)
        
    def _update_vwap(self, tick: Tick) -> None:
        """Update Volume Weighted Average Price"""
        if not self.ticks:
            self.current_vwap = tick.price
            return
            
        # Calculate VWAP for recent period
        recent_ticks = [t for t in self.ticks 
                       if (tick.timestamp - t.timestamp).seconds <= self.lookback_minutes * 60]
        
        if recent_ticks:
            total_volume = sum(t.volume for t in recent_ticks)
            total_value = sum(t.price * t.volume for t in recent_ticks)
            self.current_vwap = total_value / total_volume if total_volume > 0 else tick.price
    
    def _update_volume_profile(self, tick: Tick) -> None:
        """Update volume profile at each price level"""
        price_level = round(tick.price, 2)  # Round to nearest paisa
        self.volume_profile[price_level] += tick.volume
        
    def _update_cumulative_delta(self, tick: Tick) -> None:
        """Update cumulative delta (buying pressure - selling pressure)"""
        if tick.buyer_initiated:
            self.cumulative_delta += tick.volume
        else:
            self.cumulative_delta -= tick.volume
    
    def _analyze_flow(self) -> Optional[FlowSignal]:
        """
        Analyze current order flow and generate signals
        
        Returns:
            FlowSignal if significant pattern detected
        """
        if len(self.ticks) < 10:  # Need minimum data
            return None
            
        current_tick = self.ticks[-1]
        signals = []
        reasons = []
        
        # 1. Volume Spike Analysis
        volume_signal = self._analyze_volume_spike()
        if volume_signal:
            signals.append(volume_signal)
            reasons.append(f"Volume spike detected: {volume_signal['strength']:.2f}")
        
        # 2. VWAP Deviation Analysis
        vwap_signal = self._analyze_vwap_deviation(current_tick)
        if vwap_signal:
            signals.append(vwap_signal)
            reasons.append(f"VWAP deviation: {vwap_signal['strength']:.2f}")
        
        # 3. Order Book Imbalance Analysis
        if self.order_books:
            imbalance_signal = self._analyze_order_book_imbalance()
            if imbalance_signal:
                signals.append(imbalance_signal)
                reasons.append(f"Order imbalance: {imbalance_signal['strength']:.2f}")
        
        # 4. Large Order Detection
        large_order_signal = self._detect_large_orders()
        if large_order_signal:
            signals.append(large_order_signal)
            reasons.append(f"Large order detected: {large_order_signal['strength']:.2f}")
        
        # 5. Cumulative Delta Analysis
        delta_signal = self._analyze_cumulative_delta()
        if delta_signal:
            signals.append(delta_signal)
            reasons.append(f"Delta pressure: {delta_signal['strength']:.2f}")
        
        # Combine signals
        if signals:
            return self._combine_signals(signals, reasons, current_tick.timestamp)
        
        return None
    
    def _analyze_volume_spike(self) -> Optional[Dict]:
        """Detect volume spikes indicating institutional activity"""
        if len(self.ticks) < 20:
            return None
            
        # Get recent volume data
        recent_volumes = [tick.volume for tick in list(self.ticks)[-20:]]
        current_volume = recent_volumes[-1]
        avg_volume = np.mean(recent_volumes[:-1])
        
        if current_volume > avg_volume * self.volume_spike_threshold:
            strength = min(current_volume / avg_volume / self.volume_spike_threshold, 1.0)
            signal_type = 'BUY' if self.ticks[-1].buyer_initiated else 'SELL'
            
            return {
                'type': signal_type,
                'strength': strength,
                'source': 'volume_spike'
            }
        
        return None
    
    def _analyze_vwap_deviation(self, current_tick: Tick) -> Optional[Dict]:
        """Analyze price deviation from VWAP"""
        if self.current_vwap == 0:
            return None
            
        deviation = (current_tick.price - self.current_vwap) / self.current_vwap
        
        if abs(deviation) > self.vwap_deviation_threshold:
            strength = min(abs(deviation) / self.vwap_deviation_threshold, 1.0)
            signal_type = 'BUY' if deviation > 0 else 'SELL'
            
            return {
                'type': signal_type,
                'strength': strength,
                'source': 'vwap_deviation'
            }
        
        return None
    
    def _analyze_order_book_imbalance(self) -> Optional[Dict]:
        """Analyze order book imbalance"""
        if not self.order_books:
            return None
            
        latest_book = self.order_books[-1]
        
        # Calculate top 5 levels imbalance
        total_bid_qty = sum(level.quantity for level in latest_book.bids[:5])
        total_ask_qty = sum(level.quantity for level in latest_book.asks[:5])
        total_qty = total_bid_qty + total_ask_qty
        
        if total_qty == 0:
            return None
            
        bid_ratio = total_bid_qty / total_qty
        
        if bid_ratio > self.imbalance_threshold:
            strength = (bid_ratio - 0.5) * 2  # Scale to 0-1
            return {
                'type': 'BUY',
                'strength': strength,
                'source': 'order_imbalance'
            }
        elif bid_ratio < (1 - self.imbalance_threshold):
            strength = (0.5 - bid_ratio) * 2  # Scale to 0-1
            return {
                'type': 'SELL',
                'strength': strength,
                'source': 'order_imbalance'
            }
        
        return None
    
    def _detect_large_orders(self) -> Optional[Dict]:
        """Detect large orders indicating institutional activity"""
        if len(self.ticks) < 5:
            return None
            
        # Check recent ticks for large orders
        recent_ticks = list(self.ticks)[-5:]
        
        for tick in recent_ticks:
            order_value = tick.price * tick.volume
            if order_value > self.large_order_threshold:
                strength = min(order_value / self.large_order_threshold / 5, 1.0)
                signal_type = 'BUY' if tick.buyer_initiated else 'SELL'
                
                return {
                    'type': signal_type,
                    'strength': strength,
                    'source': 'large_order'
                }
        
        return None
    
    def _analyze_cumulative_delta(self) -> Optional[Dict]:
        """Analyze cumulative delta for buying/selling pressure"""
        if len(self.ticks) < 50:
            return None
            
        # Calculate recent delta change
        recent_ticks = list(self.ticks)[-50:]
        recent_delta = sum(tick.volume if tick.buyer_initiated else -tick.volume 
                          for tick in recent_ticks)
        
        total_volume = sum(tick.volume for tick in recent_ticks)
        
        if total_volume == 0:
            return None
            
        delta_ratio = recent_delta / total_volume
        
        if abs(delta_ratio) > 0.3:  # 30% imbalance
            strength = min(abs(delta_ratio) / 0.3, 1.0)
            signal_type = 'BUY' if delta_ratio > 0 else 'SELL'
            
            return {
                'type': signal_type,
                'strength': strength,
                'source': 'cumulative_delta'
            }
        
        return None
    
    def _combine_signals(self, signals: List[Dict], reasons: List[str], 
                        timestamp: datetime) -> FlowSignal:
        """Combine multiple signals into final flow signal"""
        
        # Count signal types
        buy_signals = [s for s in signals if s['type'] == 'BUY']
        sell_signals = [s for s in signals if s['type'] == 'SELL']
        
        # Determine overall signal
        if len(buy_signals) > len(sell_signals):
            signal_type = 'BUY'
            relevant_signals = buy_signals
        elif len(sell_signals) > len(buy_signals):
            signal_type = 'SELL'
            relevant_signals = sell_signals
        else:
            signal_type = 'NEUTRAL'
            relevant_signals = signals
        
        # Calculate combined strength and confidence
        if relevant_signals:
            avg_strength = np.mean([s['strength'] for s in relevant_signals])
            confidence = min(len(relevant_signals) / 3.0, 1.0)  # More signals = higher confidence
        else:
            avg_strength = 0.0
            confidence = 0.0
        
        return FlowSignal(
            timestamp=timestamp,
            signal_type=signal_type,
            strength=avg_strength,
            confidence=confidence,
            reasons=reasons
        )
    
    def get_current_metrics(self) -> Dict:
        """Get current order flow metrics"""
        if not self.ticks:
            return {}
            
        current_tick = self.ticks[-1]
        
        # Calculate recent volume
        recent_ticks = [t for t in self.ticks 
                       if (current_tick.timestamp - t.timestamp).seconds <= 300]  # 5 minutes
        recent_volume = sum(t.volume for t in recent_ticks)
        
        # Calculate volume profile POC (Point of Control)
        if self.volume_profile:
            poc_price = max(self.volume_profile.items(), key=lambda x: x[1])[0]
        else:
            poc_price = current_tick.price
        
        return {
            'symbol': self.symbol,
            'current_price': current_tick.price,
            'current_vwap': self.current_vwap,
            'vwap_deviation': (current_tick.price - self.current_vwap) / self.current_vwap if self.current_vwap > 0 else 0,
            'recent_volume_5min': recent_volume,
            'cumulative_delta': self.cumulative_delta,
            'poc_price': poc_price,
            'total_ticks_processed': len(self.ticks),
            'last_update': current_tick.timestamp
        }
    
    def reset(self) -> None:
        """Reset all data and calculations"""
        self.ticks.clear()
        self.order_books.clear()
        self.volume_profile.clear()
        self.cumulative_delta = 0
        self.current_vwap = 0.0
        logger.info(f"Order flow engine reset for {self.symbol}")


class AdvancedOrderFlowAnalyzer:
    """
    Advanced Order Flow Analyzer with sophisticated pattern detection

    This class provides additional advanced analysis on top of the basic
    order flow engine, including:
    - Footprint charts analysis
    - Volume at price analysis
    - Time-based flow patterns
    - Institutional signature detection
    """

    def __init__(self, symbol: str):
        self.symbol = symbol
        self.flow_engine = OrderFlowEngine(symbol)

        # Advanced analysis parameters
        self.institutional_volume_threshold = 5000000  # ₹50 lakh
        self.sweep_detection_levels = 5  # Number of levels to check for sweeps
        self.absorption_threshold = 0.8  # 80% absorption rate

        # Pattern tracking
        self.recent_patterns = deque(maxlen=100)
        self.institutional_signatures = []

    def analyze_footprint(self, price_levels: List[float],
                         timeframe_minutes: int = 5) -> Dict:
        """
        Analyze footprint chart patterns

        Args:
            price_levels: List of price levels to analyze
            timeframe_minutes: Timeframe for analysis

        Returns:
            Dictionary with footprint analysis results
        """
        if len(self.flow_engine.ticks) < 50:
            return {}

        current_time = self.flow_engine.ticks[-1].timestamp
        cutoff_time = current_time - timedelta(minutes=timeframe_minutes)

        # Filter ticks for timeframe
        relevant_ticks = [tick for tick in self.flow_engine.ticks
                         if tick.timestamp >= cutoff_time]

        footprint_data = {}

        for price in price_levels:
            price_rounded = round(price, 2)

            # Get ticks at this price level
            price_ticks = [tick for tick in relevant_ticks
                          if abs(tick.price - price_rounded) < 0.01]

            if price_ticks:
                buy_volume = sum(tick.volume for tick in price_ticks if tick.buyer_initiated)
                sell_volume = sum(tick.volume for tick in price_ticks if not tick.buyer_initiated)
                total_volume = buy_volume + sell_volume

                footprint_data[price_rounded] = {
                    'buy_volume': buy_volume,
                    'sell_volume': sell_volume,
                    'total_volume': total_volume,
                    'buy_ratio': buy_volume / total_volume if total_volume > 0 else 0,
                    'imbalance': (buy_volume - sell_volume) / total_volume if total_volume > 0 else 0
                }

        return footprint_data

    def detect_order_flow_patterns(self) -> List[Dict]:
        """
        Detect advanced order flow patterns

        Returns:
            List of detected patterns with details
        """
        patterns = []

        if len(self.flow_engine.ticks) < 100:
            return patterns

        # 1. Detect Volume Sweeps
        sweep_pattern = self._detect_volume_sweeps()
        if sweep_pattern:
            patterns.append(sweep_pattern)

        # 2. Detect Absorption
        absorption_pattern = self._detect_absorption()
        if absorption_pattern:
            patterns.append(absorption_pattern)

        # 3. Detect Iceberg Orders
        iceberg_pattern = self._detect_iceberg_orders()
        if iceberg_pattern:
            patterns.append(iceberg_pattern)

        # 4. Detect Institutional Signatures
        institutional_pattern = self._detect_institutional_signatures()
        if institutional_pattern:
            patterns.append(institutional_pattern)

        return patterns

    def _detect_volume_sweeps(self) -> Optional[Dict]:
        """Detect volume sweeps through multiple price levels"""
        if not self.flow_engine.order_books:
            return None

        latest_book = self.flow_engine.order_books[-1]
        recent_ticks = list(self.flow_engine.ticks)[-20:]

        # Check for aggressive buying/selling through levels
        for i, tick in enumerate(recent_ticks[-5:]):  # Last 5 ticks
            if tick.volume * tick.price > self.institutional_volume_threshold:

                # Check if this tick swept through multiple levels
                if tick.buyer_initiated:
                    # Check if it swept through ask levels
                    swept_levels = 0
                    for ask_level in latest_book.asks[:self.sweep_detection_levels]:
                        if tick.price >= ask_level.price:
                            swept_levels += 1

                    if swept_levels >= 3:
                        return {
                            'pattern': 'volume_sweep',
                            'direction': 'bullish',
                            'strength': min(swept_levels / self.sweep_detection_levels, 1.0),
                            'volume': tick.volume,
                            'price': tick.price,
                            'timestamp': tick.timestamp
                        }
                else:
                    # Check if it swept through bid levels
                    swept_levels = 0
                    for bid_level in latest_book.bids[:self.sweep_detection_levels]:
                        if tick.price <= bid_level.price:
                            swept_levels += 1

                    if swept_levels >= 3:
                        return {
                            'pattern': 'volume_sweep',
                            'direction': 'bearish',
                            'strength': min(swept_levels / self.sweep_detection_levels, 1.0),
                            'volume': tick.volume,
                            'price': tick.price,
                            'timestamp': tick.timestamp
                        }

        return None

    def _detect_absorption(self) -> Optional[Dict]:
        """Detect absorption patterns where large volume is absorbed"""
        if len(self.flow_engine.ticks) < 50:
            return None

        recent_ticks = list(self.flow_engine.ticks)[-50:]

        # Group ticks by price level
        price_groups = defaultdict(list)
        for tick in recent_ticks:
            price_level = round(tick.price, 2)
            price_groups[price_level].append(tick)

        # Look for absorption at key levels
        for price, ticks in price_groups.items():
            if len(ticks) >= 10:  # Significant activity at this level
                total_volume = sum(tick.volume for tick in ticks)
                buy_volume = sum(tick.volume for tick in ticks if tick.buyer_initiated)
                sell_volume = total_volume - buy_volume

                # Check for absorption (one side absorbing the other)
                if total_volume > 0:
                    absorption_ratio = max(buy_volume, sell_volume) / total_volume

                    if absorption_ratio > self.absorption_threshold:
                        return {
                            'pattern': 'absorption',
                            'direction': 'bullish' if buy_volume > sell_volume else 'bearish',
                            'strength': absorption_ratio,
                            'price_level': price,
                            'total_volume': total_volume,
                            'absorption_ratio': absorption_ratio
                        }

        return None

    def _detect_iceberg_orders(self) -> Optional[Dict]:
        """Detect iceberg orders (large orders split into smaller pieces)"""
        if not self.flow_engine.order_books or len(self.flow_engine.order_books) < 10:
            return None

        # Look for consistent replenishment at same price level
        recent_books = list(self.flow_engine.order_books)[-10:]

        # Track quantity changes at each price level
        price_tracking = defaultdict(list)

        for book in recent_books:
            for bid in book.bids[:5]:
                price_tracking[('bid', bid.price)].append(bid.quantity)
            for ask in book.asks[:5]:
                price_tracking[('ask', ask.price)].append(ask.quantity)

        # Look for iceberg patterns (quantity replenishment)
        for (side, price), quantities in price_tracking.items():
            if len(quantities) >= 5:
                # Check for replenishment pattern
                replenishments = 0
                for i in range(1, len(quantities)):
                    if quantities[i] > quantities[i-1] * 1.5:  # 50% increase
                        replenishments += 1

                if replenishments >= 2:  # Multiple replenishments
                    return {
                        'pattern': 'iceberg_order',
                        'direction': 'bullish' if side == 'bid' else 'bearish',
                        'price_level': price,
                        'replenishments': replenishments,
                        'strength': min(replenishments / 3.0, 1.0)
                    }

        return None

    def _detect_institutional_signatures(self) -> Optional[Dict]:
        """Detect institutional trading signatures"""
        if len(self.flow_engine.ticks) < 100:
            return None

        recent_ticks = list(self.flow_engine.ticks)[-100:]

        # Look for institutional patterns
        large_orders = [tick for tick in recent_ticks
                       if tick.volume * tick.price > self.institutional_volume_threshold]

        if len(large_orders) >= 3:  # Multiple large orders
            # Analyze timing and direction
            buy_orders = [order for order in large_orders if order.buyer_initiated]
            sell_orders = [order for order in large_orders if not order.buyer_initiated]

            total_buy_value = sum(order.volume * order.price for order in buy_orders)
            total_sell_value = sum(order.volume * order.price for order in sell_orders)

            if total_buy_value > total_sell_value * 2:  # Strong buying
                return {
                    'pattern': 'institutional_signature',
                    'direction': 'bullish',
                    'strength': min(total_buy_value / total_sell_value / 2, 1.0) if total_sell_value > 0 else 1.0,
                    'large_orders_count': len(large_orders),
                    'net_institutional_value': total_buy_value - total_sell_value
                }
            elif total_sell_value > total_buy_value * 2:  # Strong selling
                return {
                    'pattern': 'institutional_signature',
                    'direction': 'bearish',
                    'strength': min(total_sell_value / total_buy_value / 2, 1.0) if total_buy_value > 0 else 1.0,
                    'large_orders_count': len(large_orders),
                    'net_institutional_value': total_buy_value - total_sell_value
                }

        return None

    def get_comprehensive_analysis(self) -> Dict:
        """Get comprehensive order flow analysis"""
        basic_metrics = self.flow_engine.get_current_metrics()
        patterns = self.detect_order_flow_patterns()

        # Get current signal
        current_signal = None
        if self.flow_engine.ticks:
            current_signal = self.flow_engine._analyze_flow()

        return {
            'basic_metrics': basic_metrics,
            'detected_patterns': patterns,
            'current_signal': current_signal.__dict__ if current_signal else None,
            'analysis_timestamp': datetime.now(),
            'engine_status': 'active' if self.flow_engine.ticks else 'waiting_for_data'
        }
