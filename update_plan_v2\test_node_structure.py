#!/usr/bin/env python3
"""
Test the new clean node structure
"""

def print_node_structure():
    print("🌳 NODE STRUCTURE:")
    print("ROOT (UI)")
    print("├── TOP_CONTAINER")
    print("│   ├── LEFT_CONTAINER (50% width)")
    print("│   │   └── ORDER_BOOK_LEVELS")
    print("│   │       ├── 🔴 ASKS (red text)")
    print("│   │       ├── 📊 SPREAD (yellow)")
    print("│   │       └── 🟢 BIDS (green text)")
    print("│   └── RIGHT_CONTAINER (50% width)")
    print("│       └── TRADING_ANALYSIS_SIGNALS")
    print("│           ├── 📈 Trading Signals")
    print("│           └── 📊 Market Analytics")
    print("└── BOTTOM_CONTAINER (sibling)")
    print("    └── CHARTS")
    print("        ├── 🕯️ Candlestick Chart")
    print("        ├── 📊 Volume Chart")
    print("        └── 📈 RSI Chart")
    print()
    print("✅ Clean, maintainable node hierarchy!")
    print("✅ Equal left/right containers!")
    print("✅ Green/red order book text!")
    print("✅ Charts in separate sibling container!")

if __name__ == "__main__":
    print_node_structure()
