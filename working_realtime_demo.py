"""
Working Real-Time Demo
=====================

A working demonstration of the real-time order flow system
with proper GUI threading and every-second updates.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import threading
import time
import random
import queue
from datetime import datetime
import logging

from order_flow_engine import AdvancedOrderFlowAnal<PERSON>zer, Tick, OrderBook, OrderBookLevel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealTimeOrderBookGUI:
    """Real-time order book GUI with proper threading"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Real-Time Order Flow System")
        self.root.geometry("1000x700")
        
        # Data queue for thread-safe updates
        self.update_queue = queue.Queue()
        
        # Create GUI
        self.create_widgets()
        
        # Start update processor
        self.process_updates()
        
        # Data storage
        self.order_book_history = {}
        
    def create_widgets(self):
        """Create GUI widgets"""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="Real-Time Order Flow System", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # Status frame
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(status_frame, text="Starting...", font=("Arial", 12))
        self.status_label.pack(side=tk.LEFT)
        
        # Notebook for different views
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Order Book Tab
        self.order_book_frame = ttk.Frame(notebook)
        notebook.add(self.order_book_frame, text="Order Book")
        
        # Create order book display
        self.order_book_text = scrolledtext.ScrolledText(
            self.order_book_frame, 
            wrap=tk.WORD, 
            font=("Courier", 10),
            height=25
        )
        self.order_book_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Signals Tab
        self.signals_frame = ttk.Frame(notebook)
        notebook.add(self.signals_frame, text="Trading Signals")
        
        self.signals_text = scrolledtext.ScrolledText(
            self.signals_frame,
            wrap=tk.WORD,
            font=("Courier", 10),
            height=25
        )
        self.signals_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Metrics Tab
        self.metrics_frame = ttk.Frame(notebook)
        notebook.add(self.metrics_frame, text="Performance Metrics")
        
        self.metrics_text = scrolledtext.ScrolledText(
            self.metrics_frame,
            wrap=tk.WORD,
            font=("Courier", 10),
            height=25
        )
        self.metrics_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def update_order_book(self, symbol, order_book):
        """Thread-safe order book update"""
        self.update_queue.put(('order_book', symbol, order_book))
    
    def update_signal(self, symbol, signal, tick):
        """Thread-safe signal update"""
        self.update_queue.put(('signal', symbol, signal, tick))
    
    def update_status(self, status):
        """Thread-safe status update"""
        self.update_queue.put(('status', status))
    
    def update_metrics(self, metrics):
        """Thread-safe metrics update"""
        self.update_queue.put(('metrics', metrics))
    
    def process_updates(self):
        """Process updates from queue"""
        try:
            while True:
                try:
                    update = self.update_queue.get_nowait()
                    
                    if update[0] == 'order_book':
                        self._display_order_book(update[1], update[2])
                    elif update[0] == 'signal':
                        self._display_signal(update[1], update[2], update[3])
                    elif update[0] == 'status':
                        self.status_label.config(text=update[1])
                    elif update[0] == 'metrics':
                        self._display_metrics(update[1])
                        
                except queue.Empty:
                    break
                    
        except Exception as e:
            logger.error(f"Update processing error: {e}")
        
        # Schedule next update
        self.root.after(100, self.process_updates)  # Check every 100ms
    
    def _display_order_book(self, symbol, order_book):
        """Display order book data"""
        try:
            timestamp = order_book.timestamp.strftime('%H:%M:%S')
            
            display_text = f"\n{'='*60}\n"
            display_text += f"📊 {symbol} ORDER BOOK - {timestamp}\n"
            display_text += f"{'='*60}\n"
            
            # Calculate spread and imbalance
            if order_book.bids and order_book.asks:
                best_bid = order_book.bids[0].price
                best_ask = order_book.asks[0].price
                spread = best_ask - best_bid
                spread_pct = (spread / best_bid) * 100
                
                total_bid_qty = sum(level.quantity for level in order_book.bids[:5])
                total_ask_qty = sum(level.quantity for level in order_book.asks[:5])
                total_qty = total_bid_qty + total_ask_qty
                imbalance = (total_bid_qty - total_ask_qty) / total_qty * 100 if total_qty > 0 else 0
                
                display_text += f"💰 Best Bid: ₹{best_bid:.2f} | Best Ask: ₹{best_ask:.2f}\n"
                display_text += f"📏 Spread: ₹{spread:.2f} ({spread_pct:.3f}%)\n"
                display_text += f"⚖️  Imbalance: {imbalance:+.1f}% (Positive = More Buyers)\n\n"
            
            # Show asks (sell orders)
            display_text += "🔴 ASKS (Sell Orders):\n"
            display_text += f"{'Level':<5} {'Price':<10} {'Quantity':<12} {'Total Value':<15}\n"
            display_text += f"{'-'*50}\n"
            
            for i, ask in enumerate(order_book.asks[:5]):
                total_value = ask.price * ask.quantity
                display_text += f"{i+1:<5} ₹{ask.price:<9.2f} {ask.quantity:<12,} ₹{total_value:<14,.0f}\n"
            
            display_text += f"\n{'-'*50}\n"
            display_text += f"{'SPREAD':<5} ₹{spread:.2f}\n" if order_book.bids and order_book.asks else ""
            display_text += f"{'-'*50}\n"
            
            # Show bids (buy orders)
            display_text += "🟢 BIDS (Buy Orders):\n"
            display_text += f"{'Level':<5} {'Price':<10} {'Quantity':<12} {'Total Value':<15}\n"
            display_text += f"{'-'*50}\n"
            
            for i, bid in enumerate(order_book.bids[:5]):
                total_value = bid.price * bid.quantity
                display_text += f"{i+1:<5} ₹{bid.price:<9.2f} {bid.quantity:<12,} ₹{total_value:<14,.0f}\n"
            
            # Update display
            self.order_book_text.insert(tk.END, display_text)
            self.order_book_text.see(tk.END)
            
            # Keep only last 50 order books
            lines = self.order_book_text.get("1.0", tk.END).split('\n')
            if len(lines) > 500:  # Approximately 10 order books
                self.order_book_text.delete("1.0", "250.0")
                
        except Exception as e:
            logger.error(f"Order book display error: {e}")
    
    def _display_signal(self, symbol, signal, tick):
        """Display trading signal"""
        try:
            timestamp = tick.timestamp.strftime('%H:%M:%S.%f')[:-3]
            
            signal_text = f"\n{'='*60}\n"
            signal_text += f"🚨 TRADING SIGNAL - {symbol}\n"
            signal_text += f"{'='*60}\n"
            signal_text += f"⏰ Time: {timestamp}\n"
            signal_text += f"📊 Signal: {signal.signal_type}\n"
            signal_text += f"💪 Strength: {signal.strength:.2f}\n"
            signal_text += f"🎯 Confidence: {signal.confidence:.2f}\n"
            signal_text += f"💰 Price: ₹{tick.price:.2f}\n"
            signal_text += f"📈 Volume: {tick.volume:,}\n"
            signal_text += f"🔍 Reasons: {', '.join(signal.reasons)}\n"
            signal_text += f"{'='*60}\n"
            
            # Update signals display
            self.signals_text.insert(tk.END, signal_text)
            self.signals_text.see(tk.END)
            
            # Keep only last 20 signals
            lines = self.signals_text.get("1.0", tk.END).split('\n')
            if len(lines) > 200:
                self.signals_text.delete("1.0", "100.0")
                
        except Exception as e:
            logger.error(f"Signal display error: {e}")
    
    def _display_metrics(self, metrics):
        """Display performance metrics"""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            
            metrics_text = f"\n📊 PERFORMANCE METRICS - {timestamp}\n"
            metrics_text += f"{'='*50}\n"
            
            for key, value in metrics.items():
                if isinstance(value, float):
                    metrics_text += f"{key}: {value:.2f}\n"
                else:
                    metrics_text += f"{key}: {value}\n"
            
            metrics_text += f"{'='*50}\n"
            
            # Update metrics display
            self.metrics_text.insert(tk.END, metrics_text)
            self.metrics_text.see(tk.END)
            
            # Keep only last 10 metric updates
            lines = self.metrics_text.get("1.0", tk.END).split('\n')
            if len(lines) > 100:
                self.metrics_text.delete("1.0", "50.0")
                
        except Exception as e:
            logger.error(f"Metrics display error: {e}")
    
    def run(self):
        """Start the GUI"""
        self.root.mainloop()


class RealTimeSystemDemo:
    """Demo of real-time order flow system"""
    
    def __init__(self):
        self.symbols = ["RELIANCE", "TCS", "INFY"]
        self.analyzers = {symbol: AdvancedOrderFlowAnalyzer(symbol) for symbol in self.symbols}
        
        # GUI
        self.gui = RealTimeOrderBookGUI()
        
        # Data tracking
        self.tick_count = 0
        self.signal_count = 0
        self.order_book_count = 0
        self.start_time = time.time()
        
        # Base prices
        self.base_prices = {
            "RELIANCE": 2500.0,
            "TCS": 3500.0,
            "INFY": 1800.0
        }
        self.current_prices = self.base_prices.copy()
        
        # System state
        self.running = True
    
    def start_demo(self):
        """Start the demo"""
        logger.info("🚀 Starting Real-Time Order Flow Demo...")
        
        # Start data simulation in background thread
        data_thread = threading.Thread(target=self.simulate_data, daemon=True)
        data_thread.start()
        
        # Start performance monitoring
        monitor_thread = threading.Thread(target=self.monitor_performance, daemon=True)
        monitor_thread.start()
        
        # Run GUI in main thread
        self.gui.run()
    
    def simulate_data(self):
        """Simulate real-time market data"""
        logger.info("📊 Starting data simulation...")
        
        while self.running:
            try:
                for symbol in self.symbols:
                    # Generate tick every second
                    tick = self.generate_tick(symbol)
                    self.handle_tick(symbol, tick)
                    
                    # Generate order book every 3 seconds
                    if self.tick_count % 3 == 0:
                        order_book = self.generate_order_book(symbol)
                        self.handle_order_book(symbol, order_book)
                
                time.sleep(1)  # Every second update
                
            except Exception as e:
                logger.error(f"Data simulation error: {e}")
                time.sleep(1)
    
    def generate_tick(self, symbol):
        """Generate realistic tick data"""
        # Price movement
        base_price = self.base_prices[symbol]
        volatility = base_price * 0.001  # 0.1% volatility
        
        price_change = random.gauss(0, volatility)
        self.current_prices[symbol] += price_change
        
        # Keep within bounds
        self.current_prices[symbol] = max(self.current_prices[symbol], base_price * 0.98)
        self.current_prices[symbol] = min(self.current_prices[symbol], base_price * 1.02)
        
        # Volume with occasional spikes
        if random.random() < 0.15:  # 15% chance of large order
            volume = random.randint(50000, 200000)
        else:
            volume = random.randint(1000, 10000)
        
        return Tick(
            timestamp=datetime.now(),
            price=round(self.current_prices[symbol], 2),
            volume=volume,
            buyer_initiated=random.choice([True, False])
        )
    
    def generate_order_book(self, symbol):
        """Generate realistic order book"""
        current_price = self.current_prices[symbol]
        
        bids = []
        asks = []
        
        # Generate realistic bid/ask levels
        for i in range(10):
            # Bids
            bid_price = current_price - (i + 1) * 0.05
            bid_qty = random.randint(1000, 25000)
            bids.append(OrderBookLevel(price=bid_price, quantity=bid_qty, orders=random.randint(1, 8)))
            
            # Asks
            ask_price = current_price + (i + 1) * 0.05
            ask_qty = random.randint(1000, 25000)
            asks.append(OrderBookLevel(price=ask_price, quantity=ask_qty, orders=random.randint(1, 8)))
        
        return OrderBook(timestamp=datetime.now(), bids=bids, asks=asks)
    
    def handle_tick(self, symbol, tick):
        """Handle tick data"""
        self.tick_count += 1
        
        # Process through order flow engine
        signal = self.analyzers[symbol].flow_engine.add_tick(tick)
        
        if signal:
            self.signal_count += 1
            self.gui.update_signal(symbol, signal, tick)
            
            # Also log to console
            logger.info(f"🚨 SIGNAL #{self.signal_count}: {symbol} - {signal.signal_type} "
                       f"(Strength: {signal.strength:.2f})")
    
    def handle_order_book(self, symbol, order_book):
        """Handle order book data"""
        self.order_book_count += 1
        
        # Update GUI
        self.gui.update_order_book(symbol, order_book)
        
        # Process through order flow engine
        self.analyzers[symbol].flow_engine.add_order_book(order_book)
    
    def monitor_performance(self):
        """Monitor system performance"""
        while self.running:
            try:
                elapsed = time.time() - self.start_time
                
                metrics = {
                    'Uptime (seconds)': elapsed,
                    'Total Ticks': self.tick_count,
                    'Total Signals': self.signal_count,
                    'Total Order Books': self.order_book_count,
                    'Ticks per Second': self.tick_count / elapsed if elapsed > 0 else 0,
                    'Signals per Hour': (self.signal_count / elapsed) * 3600 if elapsed > 0 else 0,
                    'Order Books per Minute': (self.order_book_count / elapsed) * 60 if elapsed > 0 else 0
                }
                
                # Update GUI
                self.gui.update_metrics(metrics)
                
                # Update status
                status = f"Running: {elapsed:.0f}s | Ticks: {self.tick_count} | Signals: {self.signal_count}"
                self.gui.update_status(status)
                
                time.sleep(10)  # Update every 10 seconds
                
            except Exception as e:
                logger.error(f"Performance monitoring error: {e}")
                time.sleep(10)


def main():
    """Main demo function"""
    print("🚀 REAL-TIME ORDER FLOW SYSTEM DEMO")
    print("=" * 60)
    print("Features:")
    print("✅ Every second tick updates")
    print("✅ Real-time order book display in GUI")
    print("✅ Live trading signal generation")
    print("✅ Performance metrics monitoring")
    print("✅ Multi-symbol support")
    print("=" * 60)
    print("\n📊 Demo will open GUI window with 3 tabs:")
    print("  1. Order Book - Live order book depth")
    print("  2. Trading Signals - Real-time signals")
    print("  3. Performance Metrics - System stats")
    print("\n🔄 Starting demo... (Close GUI window to stop)")
    
    try:
        demo = RealTimeSystemDemo()
        demo.start_demo()
        
    except KeyboardInterrupt:
        print("\n⏹️ Demo stopped by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        logger.error(f"Demo failure: {e}")


if __name__ == "__main__":
    main()
