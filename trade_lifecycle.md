# Anatomy of a Trade: A Detailed Timeline

This document provides a granular, minute-by-minute timeline of how the trading bot identifies, executes, and manages a single trade based on a hypothetical "Reliance Earnings Announcement" event.

## Scenario
- **Event:** Reliance Earnings Announcement
- **Date:** July 17, 2025
- **Expected Market Impact:** High

---

## Detailed Trade Timeline

| Time (IST)        | Actor                | Action                                                                                             |
| ----------------- | -------------------- | -------------------------------------------------------------------------------------------------- |
| **T-1 Day (July 16)** | **System**           | Pre-fetches and confirms that the "Reliance Earnings" event is scheduled for the next day.         |
| **T-Day (July 17)** |                      |                                                                                                    |
| 8:00 AM           | **Event Processor**  | Confirms the event for today. Signals the `Flow Analyzer` to begin its analysis for "RELIANCE".    |
| 8:01 AM - 9:14 AM | **Flow Analyzer**    | Gathers and analyzes pre-market data, historical OI, and PCR for RELIANCE F&O contracts.           |
| 9:15 AM (Market Open) | **Flow Analyzer**    | Begins analyzing live, real-time Level 1, 2, and 3 data feeds for RELIANCE.                        |
| 9:30 AM           | **Flow Analyzer**    | Detects a significant build-up of sell orders and a drop in the PCR.                               |
| 9:31 AM           | **Flow Analyzer**    | **Output:** "Strong Bearish Institutional Positioning Detected".                                     |
| 9:32 AM           | **Playbook Executor**| Receives the event and flow analysis. Loads the "Earnings Short Playbook".                         |
| 9:33 AM           | **Playbook Executor**| Playbook rules confirm that a short trade is warranted. It calculates the precise options to trade.  |
| 9:34 AM           | **SmartAPIConnector**| Receives the order command from the playbook (e.g., "SELL RELIANCE 2500 CALL").                    |
| 9:34:01 AM        | **SmartAPIConnector**| Places the order on the exchange.                                                                  |
| 9:34:02 AM        | **SmartAPIConnector**| Receives confirmation that the order is filled. The bot is now in a live trade.                    |
| 9:35 AM - 3:15 PM | **Risk Manager**     | Continuously monitors the position's P&L and the underlying price of RELIANCE against the playbook's stop-loss and take-profit levels. |
| 2:45 PM           | **Risk Manager**     | The position reaches its pre-defined profit target.                                                |
| 2:45:01 PM        | **Risk Manager**     | Signals the `Playbook Executor` to exit the trade.                                                 |
| 2:45:02 PM        | **SmartAPIConnector**| Places the exit order (e.g., "BUY RELIANCE 2500 CALL").                                            |
| 2:45:03 PM        | **SmartAPIConnector**| Receives confirmation that the exit order is filled. The trade is now closed.                      |
| 4:00 PM (Market Close) | **System**           | Logs the completed trade, including entry/exit points, P&L, and all associated data.             |

---

## Sequence Diagram of a Trade

This diagram visualizes the interactions between the different components of the bot during a single trade.

```mermaid
sequenceDiagram
    participant User
    participant EventProcessor
    participant FlowAnalyzer
    participant PlaybookExecutor
    participant SmartAPIConnector
    participant Exchange

    User->>EventProcessor: Run daily check
    EventProcessor->>FlowAnalyzer: Event Found (Reliance Earnings)
    FlowAnalyzer->>SmartAPIConnector: Request F&O Data
    SmartAPIConnector-->>FlowAnalyzer: Provide Data
    FlowAnalyzer->>PlaybookExecutor: Analysis Complete (Strong Bearish)
    PlaybookExecutor->>SmartAPIConnector: Execute "Earnings Short Playbook"
    SmartAPIConnector->>Exchange: Place Sell Order
    Exchange-->>SmartAPIConnector: Order Filled
    SmartAPIConnector-->>PlaybookExecutor: Confirmation
    loop Position Monitoring
        PlaybookExecutor->>SmartAPIConnector: Request Position P&L
        SmartAPIConnector-->>PlaybookExecutor: Provide P&L
    end
    PlaybookExecutor->>SmartAPIConnector: Exit Position (Profit Target Hit)
    SmartAPIConnector->>Exchange: Place Buy Order
    Exchange-->>SmartAPIConnector: Order Filled
    SmartAPIConnector-->>PlaybookExecutor: Confirmation
    PlaybookExecutor-->>User: Log Trade Details
