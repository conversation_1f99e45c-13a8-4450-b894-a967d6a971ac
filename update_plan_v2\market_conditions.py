#!/usr/bin/env python3
"""
Market Condition Simulators
Different market conditions for realistic trading simulation
"""

import random
import time
import math
from typing import List, Tuple, Dict
from config import APP_CONFIG, SIMULATION_CONFIG


class BaseMarketCondition:
    """Base class for all market conditions"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.base_price = APP_CONFIG['base_price']
        self.current_price = self.base_price
        self.tick_size = SIMULATION_CONFIG['tick_size']
        self.spread = SIMULATION_CONFIG['spread']
        self.time_step = 0
        
        # Initialize order book
        self.bid_levels = []
        self.ask_levels = []
        self.generate_initial_order_book()
    
    def generate_initial_order_book(self):
        """Generate initial order book data with 20 levels"""
        # Generate bid levels (below current price)
        self.bid_levels = []
        for i in range(20):
            price = self.current_price - (i + 1) * self.tick_size
            quantity = random.randint(SIMULATION_CONFIG['min_quantity'], SIMULATION_CONFIG['max_quantity'])
            orders = random.randint(SIMULATION_CONFIG['min_orders'], SIMULATION_CONFIG['max_orders'])
            self.bid_levels.append((quantity, orders, price, "GENERATED"))

        # Generate ask levels (above current price)
        self.ask_levels = []
        for i in range(20):
            price = self.current_price + self.spread + (i * self.tick_size)
            quantity = random.randint(SIMULATION_CONFIG['min_quantity'], SIMULATION_CONFIG['max_quantity'])
            orders = random.randint(SIMULATION_CONFIG['min_orders'], SIMULATION_CONFIG['max_orders'])
            self.ask_levels.append((price, orders, quantity, "GENERATED"))
    
    def update_price(self):
        """Override in subclasses for specific market behavior"""
        pass
    
    def update_order_book(self):
        """Update order book with market-specific changes"""
        self.time_step += 1
        self.update_price()

        # Enhanced order flow simulation for better analysis
        self._simulate_order_additions_cancellations()

        # Update bid levels with realistic order flow
        for i in range(len(self.bid_levels)):
            quantity, orders, price, source = self.bid_levels[i]
            new_price = self.current_price - (i + 1) * self.tick_size

            # Market-specific quantity changes with order flow simulation
            quantity_change = self.get_quantity_change(i, 'bid')
            quantity = max(100, quantity + quantity_change)

            # Enhanced order count changes with realistic patterns
            order_change_prob = self.get_order_change_probability(i, 'bid')
            if random.random() < order_change_prob:
                orders_change = self.get_order_count_change(i, 'bid')
                orders = max(1, orders + orders_change)

            self.bid_levels[i] = (quantity, orders, new_price, source)

        # Update ask levels
        for i in range(len(self.ask_levels)):
            price, orders, quantity, source = self.ask_levels[i]
            new_price = self.current_price + self.spread + (i * self.tick_size)
            
            # Market-specific quantity changes
            quantity_change = self.get_quantity_change(i, 'ask')
            quantity = max(100, quantity + quantity_change)
            
            # Order count changes
            if random.random() < 0.2:
                orders_change = random.randint(-2, 3)
                orders = max(1, orders + orders_change)
            
            self.ask_levels[i] = (new_price, orders, quantity, source)
    
    def get_quantity_change(self, level: int, side: str) -> int:
        """Override in subclasses for market-specific quantity behavior"""
        if random.random() < 0.3:
            return random.randint(-1000, 1000)
        return 0
    
    def get_order_book_data(self) -> Tuple[List[Tuple], List[Tuple]]:
        """Get current order book data"""
        return self.bid_levels.copy(), self.ask_levels.copy()
    
    def get_market_analytics(self) -> Dict:
        """Generate market analytics specific to this condition"""
        volume = f"{random.uniform(0.8, 2.5):.1f}M"
        spread = abs(self.ask_levels[0][0] - self.bid_levels[0][2]) if self.ask_levels and self.bid_levels else 0.05
        
        return {
            'volume': volume,
            'spread': spread,
            'volatility': self.get_volatility_label(),
            'trend': self.name,
            'rsi': self.get_rsi()
        }
    
    def get_volatility_label(self) -> str:
        """Override in subclasses"""
        return "Medium"
    
    def get_rsi(self) -> float:
        """Override in subclasses"""
        return random.uniform(40, 60)

    def get_order_change_probability(self, level: int, side: str) -> float:
        """Get probability of order count changes - override in subclasses"""
        return 0.2  # Default 20% chance

    def get_order_count_change(self, level: int, side: str) -> int:
        """Get order count change amount - override in subclasses"""
        return random.randint(-2, 3)

    def _simulate_order_additions_cancellations(self):
        """Simulate realistic order additions and cancellations"""
        # Track order flow velocity for analysis
        self.order_additions = getattr(self, 'order_additions', 0)
        self.order_cancellations = getattr(self, 'order_cancellations', 0)

        # Simulate order activity based on market condition
        activity_level = self.get_order_activity_level()

        # Generate order additions
        if random.random() < activity_level:
            self.order_additions = random.randint(1, 10)
        else:
            self.order_additions = 0

        # Generate order cancellations
        if random.random() < activity_level * 0.7:  # Slightly less cancellations
            self.order_cancellations = random.randint(1, 8)
        else:
            self.order_cancellations = 0

    def get_order_activity_level(self) -> float:
        """Get order activity level - override in subclasses"""
        return 0.3  # Default 30% activity


class SidewaysMarket(BaseMarketCondition):
    """Sideways/Range-bound market condition"""
    
    def __init__(self):
        super().__init__("Sideways", "Range-bound market with minimal trend")
        self.range_center = self.base_price
        self.range_width = 2.0  # ±2 rupees range
    
    def update_price(self):
        """Sideways price movement within a range"""
        # Oscillate around center with mean reversion
        distance_from_center = self.current_price - self.range_center
        
        # Mean reversion force
        reversion_force = -distance_from_center * 0.1
        
        # Random noise
        noise = random.uniform(-0.2, 0.2)
        
        price_change = reversion_force + noise
        self.current_price += price_change
        
        # Keep within range bounds
        self.current_price = max(
            self.range_center - self.range_width,
            min(self.range_center + self.range_width, self.current_price)
        )
        
        # Round to tick size
        self.current_price = round(self.current_price / self.tick_size) * self.tick_size
    
    def get_quantity_change(self, level: int, side: str) -> int:
        """Balanced quantity changes for sideways market"""
        if random.random() < 0.35:  # Increased activity
            return random.randint(-1200, 1200)
        return 0

    def get_order_change_probability(self, level: int, side: str) -> float:
        """Moderate order changes in sideways market"""
        return 0.25  # 25% chance of order changes

    def get_order_count_change(self, level: int, side: str) -> int:
        """Small order count changes"""
        return random.randint(-3, 4)

    def get_order_activity_level(self) -> float:
        """Moderate order activity in sideways market"""
        return 0.4  # 40% activity level
    
    def get_volatility_label(self) -> str:
        return "Low"
    
    def get_rsi(self) -> float:
        return random.uniform(45, 55)  # Neutral RSI


class TrendingUpMarket(BaseMarketCondition):
    """Bullish trending market condition"""
    
    def __init__(self):
        super().__init__("Trending Up", "Strong bullish trend with higher highs")
        self.trend_strength = 0.8  # Stronger upward movement
        self.pullback_probability = 0.10  # Less frequent pullbacks
    
    def update_price(self):
        """Strong upward trending price movement with occasional pullbacks"""
        if random.random() < self.pullback_probability:
            # Small pullback
            price_change = random.uniform(-0.2, -0.05)
        else:
            # Strong upward movement
            price_change = random.uniform(0.2, self.trend_strength)

        self.current_price += price_change

        # Keep within reasonable bounds but allow more upward movement
        self.current_price = max(self.base_price * 0.98, min(self.base_price * 1.15, self.current_price))

        # Round to tick size
        self.current_price = round(self.current_price / self.tick_size) * self.tick_size
    
    def get_quantity_change(self, level: int, side: str) -> int:
        """Strong aggressive buying in trending up market"""
        if side == 'bid':
            # Very strong buying pressure - more bids added
            if random.random() < 0.8:  # High probability
                return random.randint(500, 4000)  # Strong buying additions
        else:  # ask
            # Asks being consumed/reduced
            if random.random() < 0.6:  # High probability
                return random.randint(-2500, 200)  # Asks being taken out
        return 0

    def get_order_change_probability(self, level: int, side: str) -> float:
        """Very high order activity in trending up market"""
        if side == 'bid':
            return 0.7  # Very high bid activity
        else:
            return 0.4  # Moderate ask activity (being consumed)

    def get_order_count_change(self, level: int, side: str) -> int:
        """Strong order count changes in trending up market"""
        if side == 'bid':
            return random.randint(2, 10)  # Many more bid orders added
        else:
            return random.randint(-6, 1)  # Ask orders being removed/consumed

    def get_order_activity_level(self) -> float:
        """Very high order activity in trending up market"""
        return 0.85  # 85% activity level - very high
    
    def get_volatility_label(self) -> str:
        return "Medium"
    
    def get_rsi(self) -> float:
        return random.uniform(60, 80)  # Strong bullish RSI

    def get_order_addition_rate(self) -> float:
        """High order addition rate for trending up"""
        return random.uniform(8, 15)  # 8-15 orders per second

    def get_bid_flow_velocity(self) -> float:
        """Strong bid flow velocity for trending up"""
        return random.uniform(5000, 12000)  # High bid velocity

    def get_ask_flow_velocity(self) -> float:
        """Lower ask flow velocity for trending up"""
        return random.uniform(500, 3000)  # Lower ask velocity


class TrendingDownMarket(BaseMarketCondition):
    """Bearish trending market condition"""
    
    def __init__(self):
        super().__init__("Trending Down", "Strong bearish trend with lower lows")
        self.trend_strength = -0.3
        self.bounce_probability = 0.12
    
    def update_price(self):
        """Downward trending price movement with occasional bounces"""
        if random.random() < self.bounce_probability:
            # Occasional bounce
            price_change = random.uniform(0.1, 0.3)
        else:
            # Downward movement
            price_change = random.uniform(self.trend_strength, -0.1)
        
        self.current_price += price_change
        
        # Keep within reasonable bounds
        self.current_price = max(self.base_price * 0.92, min(self.base_price * 1.05, self.current_price))
        
        # Round to tick size
        self.current_price = round(self.current_price / self.tick_size) * self.tick_size
    
    def get_quantity_change(self, level: int, side: str) -> int:
        """More aggressive selling in trending down market"""
        if side == 'ask':
            # Strong selling pressure with larger quantities
            if random.random() < 0.6:  # Increased probability
                return random.randint(-300, 2500)  # More aggressive selling
        else:  # bid
            # Moderate buying pressure (dip buyers)
            if random.random() < 0.35:  # Some dip buying
                return random.randint(-1500, 1000)
        return 0

    def get_order_change_probability(self, level: int, side: str) -> float:
        """Higher order activity in trending down market"""
        if side == 'ask':
            return 0.45  # High ask activity
        else:
            return 0.25  # Lower bid activity

    def get_order_count_change(self, level: int, side: str) -> int:
        """Larger order count changes in trending down market"""
        if side == 'ask':
            return random.randint(-2, 6)  # More ask orders added
        else:
            return random.randint(-5, 2)  # Bid orders removed

    def get_order_activity_level(self) -> float:
        """High order activity in trending down market"""
        return 0.7  # 70% activity level
    
    def get_volatility_label(self) -> str:
        return "Medium"
    
    def get_rsi(self) -> float:
        return random.uniform(25, 45)  # Bearish RSI


class VolatileMarket(BaseMarketCondition):
    """High volatility market condition"""
    
    def __init__(self):
        super().__init__("Volatile", "High volatility with rapid price swings")
        self.volatility_factor = 2.5
    
    def update_price(self):
        """High volatility price movement"""
        # Large random movements
        price_change = random.uniform(
            -SIMULATION_CONFIG['price_volatility'] * self.volatility_factor,
            SIMULATION_CONFIG['price_volatility'] * self.volatility_factor
        )
        
        self.current_price += price_change
        
        # Keep within reasonable bounds
        self.current_price = max(self.base_price * 0.90, min(self.base_price * 1.10, self.current_price))
        
        # Round to tick size
        self.current_price = round(self.current_price / self.tick_size) * self.tick_size
    
    def get_quantity_change(self, level: int, side: str) -> int:
        """Rapid and extreme quantity changes in volatile market"""
        if random.random() < 0.7:  # High probability of changes
            return random.randint(-3000, 3000)  # Extreme quantity swings
        return 0

    def get_order_change_probability(self, level: int, side: str) -> float:
        """Very high order activity in volatile market"""
        return 0.6  # 60% chance of order changes

    def get_order_count_change(self, level: int, side: str) -> int:
        """Large order count swings in volatile market"""
        return random.randint(-8, 10)  # Extreme order count changes

    def get_order_activity_level(self) -> float:
        """Extreme order activity in volatile market"""
        return 0.9  # 90% activity level - very high
    
    def get_volatility_label(self) -> str:
        return "High"
    
    def get_rsi(self) -> float:
        return random.uniform(20, 80)  # Wide RSI range


class BreakoutMarket(BaseMarketCondition):
    """Breakout market condition"""
    
    def __init__(self):
        super().__init__("Breakout", "Price breaking out of consolidation")
        self.breakout_direction = random.choice([1, -1])  # 1 for up, -1 for down
        self.breakout_strength = 0.5
        self.consolidation_phase = True
        self.breakout_triggered = False
    
    def update_price(self):
        """Breakout price movement"""
        if not self.breakout_triggered and self.time_step > 30:
            # Trigger breakout after some consolidation
            self.breakout_triggered = True
            self.consolidation_phase = False
        
        if self.consolidation_phase:
            # Tight range before breakout
            price_change = random.uniform(-0.1, 0.1)
        else:
            # Strong directional movement after breakout
            price_change = self.breakout_direction * random.uniform(0.2, self.breakout_strength)
        
        self.current_price += price_change
        
        # Keep within reasonable bounds
        self.current_price = max(self.base_price * 0.88, min(self.base_price * 1.12, self.current_price))
        
        # Round to tick size
        self.current_price = round(self.current_price / self.tick_size) * self.tick_size
    
    def get_quantity_change(self, level: int, side: str) -> int:
        """Enhanced quantity changes based on breakout phase"""
        if self.consolidation_phase:
            # Building pressure during consolidation
            if random.random() < 0.3:  # Increased activity
                return random.randint(-800, 800)
        else:
            # Explosive activity during breakout
            if random.random() < 0.8:  # High probability
                return random.randint(-3000, 3000)  # Large quantities
        return 0

    def get_order_change_probability(self, level: int, side: str) -> float:
        """Dynamic order activity based on breakout phase"""
        if self.consolidation_phase:
            return 0.3  # Moderate activity during consolidation
        else:
            return 0.8  # Very high activity during breakout

    def get_order_count_change(self, level: int, side: str) -> int:
        """Large order count changes during breakout"""
        if self.consolidation_phase:
            return random.randint(-3, 5)  # Moderate changes
        else:
            return random.randint(-5, 15)  # Massive order additions

    def get_order_activity_level(self) -> float:
        """Extreme activity during breakout, moderate during consolidation"""
        if self.consolidation_phase:
            return 0.4   # 40% activity during consolidation
        else:
            return 0.95  # 95% activity during breakout
    
    def get_volatility_label(self) -> str:
        return "High" if not self.consolidation_phase else "Low"
    
    def get_rsi(self) -> float:
        if self.consolidation_phase:
            return random.uniform(45, 55)
        else:
            return random.uniform(65, 85) if self.breakout_direction > 0 else random.uniform(15, 35)


# Market condition registry
MARKET_CONDITIONS = {
    "sideways": SidewaysMarket,
    "trending_up": TrendingUpMarket,
    "trending_down": TrendingDownMarket,
    "volatile": VolatileMarket,
    "breakout": BreakoutMarket
}

# UI display names
MARKET_CONDITION_NAMES = {
    "sideways": "📊 Sideways Market",
    "trending_up": "📈 Trending Up",
    "trending_down": "📉 Trending Down", 
    "volatile": "⚡ Volatile Market",
    "breakout": "🚀 Breakout"
}
