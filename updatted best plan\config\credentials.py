"""
Smart API Credentials Configuration
"""

import os
from typing import Optional, Dict


class SmartAPICredentials:
    """Smart API credentials manager"""
    
    def __init__(self):
        self.api_key: Optional[str] = None
        self.client_code: Optional[str] = None
        self.password: Optional[str] = None
        self.totp_token: Optional[str] = None
        
    def load_from_environment(self) -> bool:
        """Load credentials from environment variables"""
        try:
            self.api_key = os.getenv('SMART_API_KEY')
            self.client_code = os.getenv('SMART_CLIENT_CODE')
            self.password = os.getenv('SMART_PASSWORD')
            self.totp_token = os.getenv('SMART_TOTP_TOKEN')
            
            return all([self.api_key, self.client_code, self.password, self.totp_token])
        except Exception as e:
            print(f"Error loading credentials from environment: {e}")
            return False
    
    def load_from_file(self, filepath: str) -> bool:
        """Load credentials from file"""
        try:
            if not os.path.exists(filepath):
                return False
                
            with open(filepath, 'r') as f:
                lines = f.readlines()
                
            for line in lines:
                line = line.strip()
                if '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"\'')
                    
                    if key == 'API_KEY':
                        self.api_key = value
                    elif key == 'CLIENT_CODE':
                        self.client_code = value
                    elif key == 'PASSWORD':
                        self.password = value
                    elif key == 'TOTP_TOKEN':
                        self.totp_token = value
            
            return all([self.api_key, self.client_code, self.password, self.totp_token])
        except Exception as e:
            print(f"Error loading credentials from file: {e}")
            return False
    
    def is_valid(self) -> bool:
        """Check if all required credentials are present"""
        return all([self.api_key, self.client_code, self.password, self.totp_token])
    
    def get_credentials(self) -> Dict[str, str]:
        """Get credentials as dictionary"""
        return {
            'api_key': self.api_key or '',
            'client_code': self.client_code or '',
            'password': self.password or '',
            'totp_token': self.totp_token or ''
        }


# Global credentials instance
credentials = SmartAPICredentials()

# Try to load credentials from various sources
def initialize_credentials() -> bool:
    """Initialize credentials from available sources"""
    # Try credentials file first (your real credentials)
    cred_file = os.path.join(os.path.dirname(__file__), '..', 'credentials.txt')
    if os.path.exists(cred_file):
        if credentials.load_from_file(cred_file):
            print("✅ Smart API credentials loaded from credentials.txt")
            return True

    # Try environment variables
    if credentials.load_from_environment():
        print("✅ Smart API credentials loaded from environment variables")
        return True

    # Last resort - direct assignment (your credentials)
    print("🔑 Loading Smart API credentials...")
    credentials.api_key = "xuyTns9P"
    credentials.client_code = "AAAN362675"
    credentials.password = "4180"
    credentials.totp_token = "TU6ZEIE7ROJBSES7MYJ5YVRJE4"

    if credentials.is_valid():
        print("✅ Smart API credentials initialized successfully")
        return True

    print("❌ Failed to initialize Smart API credentials")
    return False
