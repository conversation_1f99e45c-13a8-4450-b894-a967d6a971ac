[metadata]
name = customtkinter
version = 5.2.2
description = Create modern looking GUIs with Python
long_description = A modern and customizable python UI-library based on Tkinter: https://customtkinter.tomschimansky.com
long_description_content_type = text/markdown
url = https://customtkinter.tomschimansky.com
author = <PERSON>
license = Creative Commons Zero v1.0 Universal
license_file = LICENSE
classifiers =
    License :: OSI Approved :: MIT License
    Operating System :: OS Independent
    Programming Language :: Python :: 3 :: Only

[project.urls]
homepage = https://customtkinter.tomschimansky.com
documentation = https://customtkinter.tomschimansky.com/documentation
repository = https://github.com/tomschimansky/customtkinter

[options]
python_requires = >=3.7
packages =
    customtkinter
    customtkinter.windows
    customtkinter.windows.widgets
    customtkinter.windows.widgets.appearance_mode
    customtkinter.windows.widgets.core_rendering
    customtkinter.windows.widgets.core_widget_classes
    customtkinter.windows.widgets.font
    customtkinter.windows.widgets.image
    customtkinter.windows.widgets.scaling
    customtkinter.windows.widgets.theme
    customtkinter.windows.widgets.utility
install_requires =
    darkdetect
    typing_extensions; python_version<="3.7"
    packaging
include_package_data = True
