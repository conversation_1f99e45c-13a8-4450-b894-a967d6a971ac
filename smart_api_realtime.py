"""
Smart API Real-Time Integration
==============================

Real Smart API WebSocket integration for every-second updates
with order book depth and tick data.
"""

import json
import time
import struct
import threading
from datetime import datetime
import logging
import websocket

from order_flow_engine import Tick, OrderBook, OrderBookLevel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SmartAPIRealTimeConnector:
    """Real-time Smart API connector with every-second updates"""
    
    def __init__(self, credentials_file="smart_api_credentials.json"):
        # Load credentials
        try:
            with open(credentials_file, 'r') as f:
                self.credentials = json.load(f)
        except FileNotFoundError:
            logger.error("Credentials file not found. Run test_smart_api_auth.py first.")
            raise
        
        # WebSocket connection
        self.ws = None
        self.is_connected = False
        self.connection_start_time = None
        
        # Callbacks
        self.on_tick_callback = None
        self.on_order_book_callback = None
        
        # Data tracking
        self.tick_count = 0
        self.order_book_count = 0
        self.last_heartbeat = time.time()
        
        # Symbol tokens for NSE (real tokens needed)
        self.symbol_tokens = {
            "RELIANCE": "2885",
            "TCS": "11536", 
            "INFY": "1594",
            "HDFCBANK": "1333",
            "ICICIBANK": "4963"
        }
        
        # Reverse mapping
        self.token_symbols = {v: k for k, v in self.symbol_tokens.items()}
        
        # Subscription tracking
        self.subscribed_symbols = []
        
    def set_callbacks(self, on_tick=None, on_order_book=None):
        """Set callback functions"""
        self.on_tick_callback = on_tick
        self.on_order_book_callback = on_order_book
    
    def connect(self, symbols=None):
        """Connect to Smart API WebSocket"""
        if symbols is None:
            symbols = list(self.symbol_tokens.keys())
        
        self.subscribed_symbols = symbols
        
        logger.info("🔌 Connecting to Smart API WebSocket...")
        
        try:
            ws_url = "wss://smartapisocket.angelone.in/smart-stream"
            
            self.connection_start_time = time.time()
            
            self.ws = websocket.WebSocketApp(
                ws_url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )
            
            # Start connection in thread
            connection_thread = threading.Thread(target=self.ws.run_forever)
            connection_thread.daemon = True
            connection_thread.start()
            
            return True
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    def _on_open(self, ws):
        """WebSocket opened"""
        connection_time = time.time() - self.connection_start_time
        logger.info(f"✅ WebSocket connected in {connection_time:.2f}s")
        self.is_connected = True
        self.last_heartbeat = time.time()
        
        # Send authentication
        auth_message = {
            "a": "auth",
            "user": self.credentials['client_code'],
            "token": self.credentials['feed_token']
        }
        
        try:
            ws.send(json.dumps(auth_message))
            logger.info("🔐 Authentication sent")
            
            # Wait then subscribe
            time.sleep(1)
            self._subscribe_symbols()
            
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
    
    def _on_message(self, ws, message):
        """Process WebSocket message"""
        try:
            self.last_heartbeat = time.time()
            
            if isinstance(message, str):
                # JSON message
                try:
                    data = json.loads(message)
                    self._process_json_message(data)
                except json.JSONDecodeError:
                    if message == "pong":
                        logger.debug("Received pong")
                    else:
                        logger.debug(f"Non-JSON message: {message}")
            else:
                # Binary message
                self._process_binary_message(message)
                
        except Exception as e:
            logger.error(f"Message processing error: {e}")
    
    def _on_error(self, ws, error):
        """WebSocket error"""
        logger.error(f"❌ WebSocket error: {error}")
    
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket closed"""
        logger.warning(f"🔌 WebSocket closed: {close_status_code} - {close_msg}")
        self.is_connected = False
        
        # Auto-reconnect after delay
        time.sleep(5)
        if self.subscribed_symbols:
            self.connect(self.subscribed_symbols)
    
    def _subscribe_symbols(self):
        """Subscribe to symbols with proper Smart API format"""
        logger.info("📡 Subscribing to symbols...")
        
        try:
            for symbol in self.subscribed_symbols:
                if symbol in self.symbol_tokens:
                    token = self.symbol_tokens[symbol]
                    
                    # Subscribe to LTP (Mode 1)
                    ltp_sub = {
                        "a": "subscribe",
                        "v": [[1, token]]
                    }
                    self.ws.send(json.dumps(ltp_sub))
                    
                    # Subscribe to Quote (Mode 2) for more data
                    quote_sub = {
                        "a": "subscribe", 
                        "v": [[2, token]]
                    }
                    self.ws.send(json.dumps(quote_sub))
                    
                    # Subscribe to Depth (Mode 4) for order book
                    depth_sub = {
                        "a": "subscribe",
                        "v": [[4, token]]
                    }
                    self.ws.send(json.dumps(depth_sub))
                    
                    logger.info(f"📊 Subscribed to {symbol} (token: {token})")
                    
                    # Small delay between subscriptions
                    time.sleep(0.1)
                    
        except Exception as e:
            logger.error(f"Subscription failed: {e}")
    
    def _process_json_message(self, data):
        """Process JSON message"""
        message_type = data.get('t', '')
        
        if message_type == 'ck':
            logger.info("✅ Connection acknowledged")
        elif message_type == 'tk':
            self._process_tick_data(data)
        elif message_type == 'dp':
            self._process_depth_data(data)
        elif message_type == 'sf':
            self._process_snapquote_data(data)
        else:
            logger.debug(f"Unknown message type: {message_type}")
    
    def _process_binary_message(self, data):
        """Process binary message"""
        try:
            if len(data) < 8:
                return
            
            # Basic binary parsing (Smart API specific format)
            self.tick_count += 1
            
            # This is simplified - real Smart API binary format is more complex
            logger.debug(f"📊 Binary message #{self.tick_count}: {len(data)} bytes")
            
        except Exception as e:
            logger.error(f"Binary processing error: {e}")
    
    def _process_tick_data(self, data):
        """Process tick data"""
        try:
            token = str(data.get('tk', ''))
            symbol = self.token_symbols.get(token, f"TOKEN_{token}")
            
            # Extract tick data
            ltp = float(data.get('lp', 0))  # Last traded price
            volume = int(data.get('v', 0))  # Volume
            
            if ltp > 0:
                self.tick_count += 1
                
                # Create tick object
                tick = Tick(
                    timestamp=datetime.now(),
                    price=ltp,
                    volume=volume if volume > 0 else 1,
                    buyer_initiated=True  # Simplified
                )
                
                # Call callback
                if self.on_tick_callback:
                    self.on_tick_callback(symbol, tick)
                
                # Log every 20th tick
                if self.tick_count % 20 == 0:
                    logger.info(f"📈 Tick #{self.tick_count}: {symbol} ₹{ltp:.2f}")
                
        except Exception as e:
            logger.error(f"Tick processing error: {e}")
    
    def _process_depth_data(self, data):
        """Process order book depth data"""
        try:
            token = str(data.get('tk', ''))
            symbol = self.token_symbols.get(token, f"TOKEN_{token}")
            
            # Extract bid/ask levels
            bids = []
            asks = []
            
            # Parse bid levels (bp1, bq1, etc.)
            for i in range(1, 6):  # Top 5 levels
                bid_price = data.get(f'bp{i}', 0)
                bid_qty = data.get(f'bq{i}', 0)
                if bid_price > 0 and bid_qty > 0:
                    bids.append(OrderBookLevel(
                        price=float(bid_price),
                        quantity=int(bid_qty),
                        orders=1  # Smart API doesn't provide order count
                    ))
                
                ask_price = data.get(f'sp{i}', 0)
                ask_qty = data.get(f'sq{i}', 0)
                if ask_price > 0 and ask_qty > 0:
                    asks.append(OrderBookLevel(
                        price=float(ask_price),
                        quantity=int(ask_qty),
                        orders=1
                    ))
            
            if bids and asks:
                self.order_book_count += 1
                
                # Create order book
                order_book = OrderBook(
                    timestamp=datetime.now(),
                    bids=bids,
                    asks=asks
                )
                
                # Call callback
                if self.on_order_book_callback:
                    self.on_order_book_callback(symbol, order_book)
                
                # Log every 10th order book
                if self.order_book_count % 10 == 0:
                    logger.info(f"📚 Order Book #{self.order_book_count}: {symbol}")
                
        except Exception as e:
            logger.error(f"Depth processing error: {e}")
    
    def _process_snapquote_data(self, data):
        """Process snap quote data"""
        try:
            token = str(data.get('tk', ''))
            symbol = self.token_symbols.get(token, f"TOKEN_{token}")
            
            # Extract comprehensive data
            ltp = float(data.get('lp', 0))
            open_price = float(data.get('o', 0))
            high = float(data.get('h', 0))
            low = float(data.get('l', 0))
            close = float(data.get('c', 0))
            volume = int(data.get('v', 0))
            
            logger.debug(f"📊 Snap Quote: {symbol} LTP: ₹{ltp:.2f}, Vol: {volume:,}")
            
        except Exception as e:
            logger.error(f"Snap quote processing error: {e}")
    
    def send_heartbeat(self):
        """Send heartbeat to keep connection alive"""
        try:
            if self.ws and self.is_connected:
                self.ws.send("ping")
                logger.debug("💓 Heartbeat sent")
        except Exception as e:
            logger.error(f"Heartbeat failed: {e}")
    
    def start_heartbeat(self):
        """Start heartbeat thread"""
        def heartbeat_loop():
            while self.is_connected:
                self.send_heartbeat()
                time.sleep(30)  # Send heartbeat every 30 seconds
        
        heartbeat_thread = threading.Thread(target=heartbeat_loop, daemon=True)
        heartbeat_thread.start()
    
    def get_status(self):
        """Get connection status"""
        return {
            'connected': self.is_connected,
            'ticks_processed': self.tick_count,
            'order_books_processed': self.order_book_count,
            'subscribed_symbols': len(self.subscribed_symbols),
            'uptime_seconds': time.time() - self.connection_start_time if self.connection_start_time else 0,
            'last_heartbeat': self.last_heartbeat
        }
    
    def disconnect(self):
        """Disconnect WebSocket"""
        if self.ws:
            self.ws.close()
        self.is_connected = False
        logger.info("🔌 WebSocket disconnected")


# Test function
def test_realtime_connector():
    """Test the real-time connector"""
    print("🚀 Testing Smart API Real-Time Connector")
    print("=" * 50)
    
    def on_tick(symbol, tick):
        print(f"📈 TICK: {symbol} ₹{tick.price:.2f} (Vol: {tick.volume:,}) at {tick.timestamp.strftime('%H:%M:%S')}")
    
    def on_order_book(symbol, order_book):
        if order_book.bids and order_book.asks:
            best_bid = order_book.bids[0].price
            best_ask = order_book.asks[0].price
            spread = best_ask - best_bid
            print(f"📚 ORDER BOOK: {symbol} Bid: ₹{best_bid:.2f}, Ask: ₹{best_ask:.2f}, Spread: ₹{spread:.2f}")
    
    try:
        # Create connector
        connector = SmartAPIRealTimeConnector()
        connector.set_callbacks(on_tick=on_tick, on_order_book=on_order_book)
        
        # Connect
        symbols = ["RELIANCE", "TCS", "INFY"]
        if connector.connect(symbols):
            print("✅ Connected! Monitoring real-time data...")
            
            # Start heartbeat
            connector.start_heartbeat()
            
            # Monitor for 2 minutes
            start_time = time.time()
            while (time.time() - start_time) < 120:
                time.sleep(10)
                status = connector.get_status()
                print(f"\n⏱️  Status: Connected: {status['connected']}, "
                      f"Ticks: {status['ticks_processed']}, "
                      f"Order Books: {status['order_books_processed']}")
            
            # Disconnect
            connector.disconnect()
        else:
            print("❌ Connection failed")
            
    except KeyboardInterrupt:
        print("\n⏹️  Test stopped by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")


if __name__ == "__main__":
    test_realtime_connector()
