#!/usr/bin/env python3
"""
Configuration file for Ultimate Trading Analysis v2
"""

# Executive styling colors
EXECUTIVE_COLORS = {
    'bg_primary': '#0a0a0a',      # Deep black
    'bg_secondary': '#151515',    # Dark charcoal
    'bg_card': '#1a1a1a',         # Card background
    'text_primary': '#ffffff',    # White text
    'text_secondary': '#b0b0b0',  # Light gray text
    'text_muted': '#666666',      # Muted text
    'accent_blue': '#00d4ff',     # Cyan blue
    'success': '#00ff41',         # Bright green
    'danger': '#ff0844',          # Bright red
    'warning': '#ffaa00',         # Bright orange
    'info': '#0099ff',            # Bright blue
    'hft_green': '#00ff41',       # HFT green
    'hft_red': '#ff0844',         # HFT red
    'hft_cyan': '#00ffff',        # HFT cyan
    'hft_yellow': '#ffff00',      # HFT yellow
    'hft_border': '#333333'       # HFT border
}

# Application settings
APP_CONFIG = {
    'window_title': 'Ultimate Trading Analysis v2 - CGCL Live Market',
    'window_size': '1200x800',  # Reasonable size that fits most screens
    'symbol': 'CGCL',
    'base_price': 850.0,
    'update_interval': 1.0,  # seconds
    'max_order_book_levels': 10
}

# Market simulation settings
SIMULATION_CONFIG = {
    'price_volatility': 2.0,  # max price change per update
    'tick_size': 0.05,
    'spread': 0.10,
    'min_quantity': 100,
    'max_quantity': 50000,
    'min_orders': 1,
    'max_orders': 25
}
