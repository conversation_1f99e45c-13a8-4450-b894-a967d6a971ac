# 🔑 Smart API Setup Guide for LIVE CGCL Trading

## ⚠️ **CRITICAL: Smart API Required for Live Trading**

This CGCL WebSocket system **REQUIRES** Smart API credentials for live trading. Demo mode is **NOT suitable** for real money trading.

## 📋 **Step 1: Get Angel One Smart API Credentials**

### **1.1 Create Angel One Account:**
- Visit: https://www.angelone.in/
- Open a trading account
- Complete KYC verification

### **1.2 Apply for Smart API:**
- Login to Angel One
- Go to: **My Profile > API**
- Apply for Smart API access
- Wait for approval (usually 1-2 business days)

### **1.3 Get API Credentials:**
After approval, you'll receive:
- **API Key**: Your unique API identifier
- **Username**: Your Angel One username
- **Password**: Your Angel One password
- **TOTP Secret**: For 2FA authentication

## 🔧 **Step 2: Configure Credentials**

### **2.1 Create Credentials File:**
1. Copy `smart_api_credentials_template.json` to `smart_api_credentials.json`
2. Fill in your actual credentials:

```json
{
    "api_key": "your_actual_api_key",
    "username": "your_actual_username",
    "password": "your_actual_password",
    "totp": "your_actual_totp_secret"
}
```

### **2.2 Example (with dummy data):**
```json
{
    "api_key": "AbCdEfGh",
    "username": "AB1234",
    "password": "MyPassword123",
    "totp": "JBSWY3DPEHPK3PXP"
}
```

## 🔐 **Step 3: Security Notes**

### **⚠️ Important Security:**
- **Never share** your credentials
- **Never commit** credentials to version control
- **Keep credentials file secure**
- **Use strong passwords**

### **📁 File Security:**
```bash
# Make credentials file read-only
chmod 600 smart_api_credentials.json

# Add to .gitignore
echo "smart_api_credentials.json" >> .gitignore
```

## 🚀 **Step 4: Test Connection**

### **4.1 Run the System:**
```bash
python cgcl_advanced_depth.py
```

### **4.2 Expected Output:**
```
🔑 Initializing Smart API for LIVE trading...
✅ Smart API package imported successfully
🔐 Connecting to Smart API with API key: AbCdEfGh...
🔑 Generating Smart API session...
✅ Smart API login successful!
🔍 Searching for CGCL token...
✅ CGCL token found: 12345
🚀 Smart API ready for LIVE WebSocket trading!
🔌 Starting Smart API WebSocket for LIVE CGCL data...
✅ Smart API WebSocket connected successfully!
📡 Subscribing to CGCL token: 12345
✅ CGCL subscription sent to Smart API
🚀 Smart API WebSocket ready for LIVE trading!
```

## ❌ **Troubleshooting**

### **Common Errors:**

**1. "smart_api_credentials.json not found"**
- Create the credentials file with your actual data

**2. "Missing required fields"**
- Ensure all 4 fields are present: api_key, username, password, totp

**3. "Smart API login failed"**
- Check username/password
- Verify TOTP secret is correct
- Ensure API access is approved

**4. "CGCL token not found"**
- CGCL might not be available for trading
- Check if symbol is correct
- Try during market hours

**5. "WebSocket connection timeout"**
- Check internet connection
- Verify firewall settings
- Try during market hours

### **Debug Mode:**
Enable detailed logging by setting:
```python
websocket.enableTrace(True)
```

## 📊 **Step 5: Verify Live Data**

### **5.1 Check Performance Metrics:**
- **Status**: Should show "CONNECTED (Smart API LIVE)"
- **Latency**: Should be 10-50ms
- **Updates/sec**: Should be > 0
- **Source**: Should show "WebSocket (Live)"

### **5.2 Verify Real Data:**
- Price should match Angel One app
- Volume should be realistic
- OHLC should update in real-time

## 🎯 **Ready for Live Trading!**

Once Smart API WebSocket is connected:
- ✅ **Real CGCL prices** streaming live
- ✅ **Instant order flow** analysis
- ✅ **Live 30-minute predictions**
- ✅ **True real-time** trading data
- ✅ **10-50ms latency** for quick decisions

**Your CGCL system is now ready for professional live trading!** 🚀

## 📞 **Support**

If you need help:
1. Check Angel One Smart API documentation
2. Contact Angel One support for API issues
3. Verify credentials are correct
4. Test during market hours (9:15 AM - 3:30 PM IST)

**Remember: Only use this system with live Smart API data for real trading!**
