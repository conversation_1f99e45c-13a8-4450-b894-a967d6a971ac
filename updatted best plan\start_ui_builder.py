"""
Quick Start Script for CGCL UI Builder Tool
Fixed version with all dependencies
"""

import os
import sys

def main():
    print("🎨 CGCL Trading Dashboard - UI Builder Tool (Vertical Scroll)")
    print("=" * 60)
    print("🔧 Enhanced Drag & Drop Dashboard Designer")
    print("🎯 Full 1920 width view with vertical scrolling!")
    print()
    print("✅ Features:")
    print("   📦 Component Tray with 7 trading components")
    print("   🎨 Full 1920 width proportional view")
    print("   ↕️ Vertical scroll only (no horizontal scroll)")
    print("   👁️ True width preview of final dashboard")
    print("   🖱️ Drag, drop, resize (width/height individually)")
    print("   🗑️ Right-click to delete components")
    print("   💾 Save/Load layouts as JSON")
    print("   🚀 Generate complete dashboard code")
    print()
    print("🎯 Component Sizes (Recommended):")
    print("   📊 Order Book: 350×400px")
    print("   📈 CVD Chart: 600×300px")
    print("   📊 Volume Profile: 400×350px")
    print("   🦶 Footprint Chart: 500×300px")
    print("   🌊 Order Flow: 600×250px")
    print("   ⚡ Trading Signals: 300×350px")
    print("   📈 Market Analytics: 400×200px")
    print()
    print("🖱️ Controls:")
    print("   • Drag components from tray to canvas")
    print("   • ↕️ Mouse wheel: Scroll vertically")
    print("   • 🟠 Orange Handle: Resize width only")
    print("   • 🟣 Purple Handle: Resize height only")
    print("   • 🔵 Blue Handle: Resize both dimensions")
    print("   • Right-click: Delete component")
    print("=" * 60)
    print()
    
    try:
        # Import and run the UI builder
        from ui_builder_tool import UIBuilderTool
        
        print("🚀 Starting UI Builder Tool...")
        builder = UIBuilderTool()
        builder.run()
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install tkinter matplotlib numpy")
        input("Press Enter to exit...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
