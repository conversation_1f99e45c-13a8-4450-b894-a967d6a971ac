"""
Ultimate Trading Analysis v1
===========================

Complete trading dashboard integrating all components from YouTube video analysis:
- Order Book (Real-time bid/ask data)
- Footprint Chart (Volume at each price level with bid/ask columns)
- Volume Profile (Horizontal histogram showing volume at price levels)
- CVD Chart (Cumulative Volume Delta for tracking buying/selling pressure)
- Order Flow Analysis (Market microstructure analysis)
- Market Analytics (Volume, spread, volatility, trend, RSI)
- Trading Signals (Buy/sell signals with entry, target, stop levels)

Connected to Smart API WebSocket for real-time market data.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import numpy as np
from datetime import datetime
import sys
import os

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import core components
from config.settings import SYSTEM_CONFIG, COLORS
from config.professional_theme import EXECUTIVE_COLORS
from core.data_source_manager import DataSourceManager, DataSourceType
from core.order_book import OrderBookStateManager, OrderBookEventSystem
from core.data_structures import MarketData, OrderBookSnapshot
from analytics.flow_analysis import OrderFlowImbalanceDetector
from analytics.price_prediction import PricePredictionEngine
from analytics.trading_signals import TradingSignalGenerator
from analytics.market_depth import MarketDepthAnalytics
from storage.historical_data import HistoricalOrderBookStorage

# Import GUI components
from gui.professional_components import (
    ProfessionalCard, MetricDisplay, ProfessionalChart,
    OrderBookWidget, TradingSignalWidget
)
from gui.order_book_display import OrderBookDisplay

class UltimateTradingAnalysisV1:
    """Ultimate Trading Analysis Dashboard v1"""
    
    def __init__(self):
        print("🚀 Initializing Ultimate Trading Analysis v1...")
        
        # Initialize GUI
        self.root = tk.Tk()
        self.root.title("Ultimate Trading Analysis v1 - CGCL Live Market")
        self.root.geometry("1920x1080")
        self.root.configure(bg=EXECUTIVE_COLORS['bg_primary'])
        self.root.state('zoomed')
        
        # Core trading components
        self.data_source_manager = DataSourceManager()
        self.order_book_manager = OrderBookStateManager()
        self.event_system = OrderBookEventSystem()
        self.flow_detector = OrderFlowImbalanceDetector()
        self.price_predictor = PricePredictionEngine()
        self.signal_generator = TradingSignalGenerator()
        self.market_analytics = MarketDepthAnalytics()
        self.historical_storage = HistoricalOrderBookStorage()
        
        # GUI components
        self.order_book_display = None
        self.trading_signals_widget = None
        self.volume_profile_chart = None
        self.footprint_chart = None
        self.cvd_chart = None
        self.order_flow_chart = None
        self.market_analytics_panel = None
        
        # Data storage
        self.current_market_data = None
        self.current_order_book = None
        self.price_history = []
        self.volume_history = []
        self.cvd_history = []
        
        # Connection status
        self.is_connected = False
        
        # Setup callbacks
        self.setup_callbacks()
        
        # Create UI
        self.create_ui()
        
        print("✅ Ultimate Trading Analysis v1 initialized successfully")
    
    def setup_callbacks(self):
        """Setup data source and event callbacks"""
        self.data_source_manager.on_market_data = self.on_market_data_received
        self.data_source_manager.on_order_book_data = self.on_order_book_received
        self.data_source_manager.on_connection_status = self.on_connection_status_changed

        # Setup event system callbacks
        self.event_system.subscribe("order_book_updated", self.on_order_book_updated)
        self.event_system.subscribe("flow_imbalance_detected", self.on_flow_imbalance)
        self.event_system.subscribe("trading_signal_generated", self.on_trading_signal)
    
    def create_ui(self):
        """Create the main UI layout"""
        print("🎨 Creating Ultimate Trading Analysis UI...")
        
        # Main container
        main_container = tk.Frame(self.root, bg=EXECUTIVE_COLORS['bg_primary'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Header
        self.create_header(main_container)
        
        # Create scrollable content area
        self.create_scrollable_content(main_container)
        
        print("✅ UI created successfully")
    
    def create_header(self, parent):
        """Create header with connection controls"""
        header_frame = tk.Frame(parent, bg=EXECUTIVE_COLORS['bg_secondary'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        header_frame.pack_propagate(False)

        # Title
        title_label = tk.Label(
            header_frame,
            text="🚀 Ultimate Trading Analysis v1 - CGCL Live Market",
            bg=EXECUTIVE_COLORS['bg_secondary'],
            fg=EXECUTIVE_COLORS['accent_primary'],
            font=('Arial', 20, 'bold')
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=20)

        # Connection controls
        controls_frame = tk.Frame(header_frame, bg=EXECUTIVE_COLORS['bg_secondary'])
        controls_frame.pack(side=tk.RIGHT, padx=20, pady=20)

        # Data source selection
        data_source_frame = tk.Frame(controls_frame, bg=EXECUTIVE_COLORS['bg_secondary'])
        data_source_frame.pack(side=tk.LEFT, padx=(0, 20))

        tk.Label(
            data_source_frame,
            text="Data Source:",
            bg=EXECUTIVE_COLORS['bg_secondary'],
            fg=EXECUTIVE_COLORS['text_primary'],
            font=('Arial', 10, 'bold')
        ).pack(side=tk.TOP)

        source_buttons_frame = tk.Frame(data_source_frame, bg=EXECUTIVE_COLORS['bg_secondary'])
        source_buttons_frame.pack(side=tk.TOP, pady=(5, 0))

        # Live data button
        self.live_data_btn = tk.Button(
            source_buttons_frame,
            text="📡 Live",
            command=lambda: self.set_data_source(DataSourceType.LIVE),
            bg=EXECUTIVE_COLORS['bg_primary'],
            fg=EXECUTIVE_COLORS['text_primary'],
            font=('Arial', 9),
            padx=10,
            pady=5
        )
        self.live_data_btn.pack(side=tk.LEFT, padx=(0, 2))

        # Simulated data button
        self.sim_data_btn = tk.Button(
            source_buttons_frame,
            text="🎯 Sim",
            command=lambda: self.set_data_source(DataSourceType.SIMULATED),
            bg=EXECUTIVE_COLORS['success'],  # Start with simulation active
            fg='white',
            font=('Arial', 9, 'bold'),
            padx=10,
            pady=5
        )
        self.sim_data_btn.pack(side=tk.LEFT)

        # Connect button
        self.connect_btn = tk.Button(
            controls_frame,
            text="🔌 Connect",
            bg=EXECUTIVE_COLORS['success'],
            fg='white',
            font=('Arial', 12, 'bold'),
            command=self.toggle_connection,
            padx=20,
            pady=10
        )
        self.connect_btn.pack(side=tk.LEFT, padx=10)

        # Status indicator
        self.status_label = tk.Label(
            controls_frame,
            text="⚫ Disconnected",
            bg=EXECUTIVE_COLORS['bg_secondary'],
            fg=EXECUTIVE_COLORS['text_muted'],
            font=('Arial', 12, 'bold')
        )
        self.status_label.pack(side=tk.LEFT, padx=10)

        # Clock
        self.clock_label = tk.Label(
            controls_frame,
            text="",
            bg=EXECUTIVE_COLORS['bg_secondary'],
            fg=EXECUTIVE_COLORS['text_primary'],
            font=('Arial', 12)
        )
        self.clock_label.pack(side=tk.LEFT, padx=10)
        
        # Start clock update
        self.update_clock()
    
    def create_scrollable_content(self, parent):
        """Create scrollable content area with all trading components"""
        # Create canvas and scrollbar
        canvas = tk.Canvas(parent, bg=EXECUTIVE_COLORS['bg_primary'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=EXECUTIVE_COLORS['bg_primary'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Bind mouse wheel
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        # Create all trading components
        self.create_trading_components(scrollable_frame)
    
    def create_trading_components(self, parent):
        """Create all trading analysis components"""
        print("📊 Creating trading components...")
        
        # Configure grid
        parent.grid_columnconfigure(0, weight=1)
        parent.grid_columnconfigure(1, weight=1)
        
        # Row 1: Order Book + Trading Signals
        self.create_order_book_component(parent, row=0, col=0)
        self.create_trading_signals_component(parent, row=0, col=1)
        
        # Row 2: Market Analytics (full width)
        self.create_market_analytics_component(parent, row=1, col=0, colspan=2)
        
        # Row 3: Volume Profile + Footprint Chart
        self.create_volume_profile_component(parent, row=2, col=0)
        self.create_footprint_chart_component(parent, row=2, col=1)
        
        # Row 4: CVD Chart + Order Flow Analysis
        self.create_cvd_chart_component(parent, row=3, col=0)
        self.create_order_flow_component(parent, row=3, col=1)
        
        print("✅ All trading components created")

    def populate_test_order_book(self):
        """Populate order book with test data to verify UI is working"""
        try:
            if self.order_book_display and hasattr(self.order_book_display, 'update_order_book'):
                # Create test order book data around CGCL current price range
                test_bids = [
                    (500, 3, 184.50),   # (qty, orders, price)
                    (750, 5, 184.45),
                    (300, 2, 184.40),
                    (1200, 8, 184.35),
                    (600, 4, 184.30),
                    (900, 6, 184.25),
                    (400, 3, 184.20),
                    (800, 5, 184.15),
                    (350, 2, 184.10),
                    (650, 4, 184.05)
                ]

                test_asks = [
                    (184.55, 2, 400),   # (price, orders, qty)
                    (184.60, 4, 600),
                    (184.65, 3, 350),
                    (184.70, 6, 800),
                    (184.75, 5, 550),
                    (184.80, 7, 900),
                    (184.85, 4, 450),
                    (184.90, 8, 1100),
                    (184.95, 3, 300),
                    (185.00, 5, 700)
                ]

                self.order_book_display.update_order_book(test_bids, test_asks)
                print("✅ Initial order book data loaded")
            else:
                print("❌ Cannot populate test data - order book display not available")
        except Exception as e:
            print(f"❌ Error populating test order book: {e}")
    
    def create_order_book_component(self, parent, row, col):
        """Create Order Book component"""
        self.order_book_display = OrderBookWidget(
            parent,
            width=600,
            height=500
        )
        self.order_book_display.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

        # Add some initial test data to verify the UI is working
        self.populate_test_order_book()
    
    def create_trading_signals_component(self, parent, row, col):
        """Create Trading Signals component"""
        self.trading_signals_widget = TradingSignalWidget(
            parent,
            width=600,
            height=500
        )
        self.trading_signals_widget.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")
    
    def create_market_analytics_component(self, parent, row, col, colspan=1):
        """Create Market Analytics component"""
        self.market_analytics_panel = ProfessionalCard(
            parent,
            title="📈 Market Analytics",
            width=1220,
            height=200
        )
        self.market_analytics_panel.grid(row=row, column=col, columnspan=colspan, 
                                       padx=10, pady=10, sticky="nsew")
        
        # Add analytics metrics
        metrics_frame = tk.Frame(self.market_analytics_panel.content_frame,
                               bg=EXECUTIVE_COLORS['bg_secondary'])
        metrics_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create metric displays
        self.volume_metric = MetricDisplay(metrics_frame, "Volume", "1.2M", "", None, "#00ff88")
        self.spread_metric = MetricDisplay(metrics_frame, "Spread", "₹0.05", "", None, "#ffaa00")
        self.volatility_metric = MetricDisplay(metrics_frame, "Volatility", "Low", "", None, "#00aaff")
        self.trend_metric = MetricDisplay(metrics_frame, "Trend", "Bullish", "", None, "#00ff88")
        self.rsi_metric = MetricDisplay(metrics_frame, "RSI", "65.2", "", None, "#ffaa00")
        
        # Pack metrics
        for metric in [self.volume_metric, self.spread_metric, self.volatility_metric,
                      self.trend_metric, self.rsi_metric]:
            metric.pack(side=tk.LEFT, padx=20, pady=10)

    def create_volume_profile_component(self, parent, row, col):
        """Create Volume Profile component with enhanced visualization"""
        self.volume_profile_chart = ProfessionalChart(
            parent,
            title="📊 Volume Profile (Fair Value Analysis)",
            chart_type="bar",
            width=600,
            height=400
        )
        self.volume_profile_chart.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

        # Initialize with realistic volume profile data
        prices = np.arange(184.0, 185.0, 0.02)  # More granular price levels
        # Create realistic volume distribution with POC (Point of Control)
        poc_price = 184.5  # Point of Control
        volumes = []
        for price in prices:
            distance = abs(price - poc_price)
            # Higher volume near POC, lower at extremes
            volume = 5000 * np.exp(-distance * 20) + np.random.normal(500, 100)
            volumes.append(max(volume, 0))

        # Create horizontal bar chart (volume profile style)
        self.volume_profile_chart.axes.clear()
        bars = self.volume_profile_chart.axes.barh(prices, volumes, height=0.015,
                                                  color=EXECUTIVE_COLORS['accent_primary'],
                                                  alpha=0.7, edgecolor=EXECUTIVE_COLORS['border_light'])

        # Highlight POC (highest volume)
        max_vol_idx = np.argmax(volumes)
        bars[max_vol_idx].set_color(EXECUTIVE_COLORS['warning'])
        bars[max_vol_idx].set_alpha(0.9)

        # Style the chart
        self.volume_profile_chart.axes.set_xlabel('Volume', color=EXECUTIVE_COLORS['text_primary'])
        self.volume_profile_chart.axes.set_ylabel('Price (₹)', color=EXECUTIVE_COLORS['text_primary'])
        self.volume_profile_chart.axes.grid(True, alpha=0.3, color=EXECUTIVE_COLORS['border_light'])
        self.volume_profile_chart.axes.set_facecolor(EXECUTIVE_COLORS['bg_tertiary'])

        # Add POC line
        self.volume_profile_chart.axes.axhline(y=poc_price, color=EXECUTIVE_COLORS['warning'],
                                              linestyle='--', alpha=0.8, linewidth=2, label='POC')
        self.volume_profile_chart.axes.legend()

        self.volume_profile_chart.canvas.draw()

    def create_footprint_chart_component(self, parent, row, col):
        """Create Footprint Chart component with professional visualization"""
        self.footprint_chart = ProfessionalChart(
            parent,
            title="🦶 Footprint Analysis (Bid/Ask Imbalances)",
            chart_type="scatter",
            width=600,
            height=400
        )
        self.footprint_chart.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

        # Create realistic footprint data showing bid/ask imbalances
        price_levels = np.arange(184.0, 185.0, 0.05)
        time_periods = np.arange(0, 20, 1)  # 20 time periods

        self.footprint_chart.axes.clear()

        # Create heatmap-style footprint chart
        for i, time_period in enumerate(time_periods):
            for j, price in enumerate(price_levels):
                # Generate bid and ask volumes
                bid_vol = np.random.exponential(1000) + 200
                ask_vol = np.random.exponential(1000) + 200

                # Calculate imbalance
                total_vol = bid_vol + ask_vol
                bid_ratio = bid_vol / total_vol

                # Color based on imbalance
                if bid_ratio > 0.6:  # Bid dominant
                    color = EXECUTIVE_COLORS['bid_green']
                    alpha = min(bid_ratio, 0.9)
                elif bid_ratio < 0.4:  # Ask dominant
                    color = EXECUTIVE_COLORS['ask_red']
                    alpha = min(1 - bid_ratio, 0.9)
                else:  # Balanced
                    color = EXECUTIVE_COLORS['text_muted']
                    alpha = 0.5

                # Plot the footprint cell
                self.footprint_chart.axes.scatter(time_period, price,
                                                 s=total_vol/50,  # Size based on total volume
                                                 c=color, alpha=alpha,
                                                 edgecolors=EXECUTIVE_COLORS['border_light'],
                                                 linewidth=0.5)

        # Style the chart
        self.footprint_chart.axes.set_xlabel('Time Periods', color=EXECUTIVE_COLORS['text_primary'])
        self.footprint_chart.axes.set_ylabel('Price (₹)', color=EXECUTIVE_COLORS['text_primary'])
        self.footprint_chart.axes.grid(True, alpha=0.3, color=EXECUTIVE_COLORS['border_light'])
        self.footprint_chart.axes.set_facecolor(EXECUTIVE_COLORS['bg_tertiary'])

        # Add legend
        bid_legend = self.footprint_chart.axes.scatter([], [], c=EXECUTIVE_COLORS['bid_green'],
                                                      alpha=0.7, label='Bid Dominant')
        ask_legend = self.footprint_chart.axes.scatter([], [], c=EXECUTIVE_COLORS['ask_red'],
                                                      alpha=0.7, label='Ask Dominant')
        self.footprint_chart.axes.legend()

        self.footprint_chart.canvas.draw()

    def create_cvd_chart_component(self, parent, row, col):
        """Create CVD Chart component with enhanced visualization"""
        self.cvd_chart = ProfessionalChart(
            parent,
            title="📈 Cumulative Volume Delta (CVD) Analysis",
            chart_type="line",
            width=600,
            height=400
        )
        self.cvd_chart.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

        # Initialize with realistic CVD data showing divergences
        time_data = np.arange(0, 100, 1)

        # Create price data with trend
        price_base = 184.5
        price_trend = np.cumsum(np.random.randn(100) * 0.02) + price_base

        # Create CVD data with potential divergences
        cvd_deltas = []
        for i in range(100):
            if i < 30:  # Early accumulation
                delta = np.random.normal(50, 20)
            elif i < 60:  # Distribution phase
                delta = np.random.normal(-20, 30)
            else:  # Late accumulation
                delta = np.random.normal(30, 25)
            cvd_deltas.append(delta)

        cvd_data = np.cumsum(cvd_deltas)

        self.cvd_chart.axes.clear()

        # Plot CVD line
        cvd_line = self.cvd_chart.axes.plot(time_data, cvd_data,
                                           color=EXECUTIVE_COLORS['success'],
                                           linewidth=2, label='CVD')[0]

        # Add zero line
        self.cvd_chart.axes.axhline(y=0, color=EXECUTIVE_COLORS['text_muted'],
                                   linestyle='-', alpha=0.5, linewidth=1)

        # Color areas above/below zero
        self.cvd_chart.axes.fill_between(time_data, cvd_data, 0,
                                        where=(cvd_data >= 0),
                                        color=EXECUTIVE_COLORS['success'],
                                        alpha=0.3, label='Buying Pressure')
        self.cvd_chart.axes.fill_between(time_data, cvd_data, 0,
                                        where=(cvd_data < 0),
                                        color=EXECUTIVE_COLORS['danger'],
                                        alpha=0.3, label='Selling Pressure')

        # Style the chart
        self.cvd_chart.axes.set_xlabel('Time', color=EXECUTIVE_COLORS['text_primary'])
        self.cvd_chart.axes.set_ylabel('Cumulative Volume Delta', color=EXECUTIVE_COLORS['text_primary'])
        self.cvd_chart.axes.grid(True, alpha=0.3, color=EXECUTIVE_COLORS['border_light'])
        self.cvd_chart.axes.set_facecolor(EXECUTIVE_COLORS['bg_tertiary'])
        self.cvd_chart.axes.legend()

        # Store data for updates
        self.cvd_history = cvd_deltas

        self.cvd_chart.canvas.draw()

    def create_order_flow_component(self, parent, row, col):
        """Create Order Flow component with exhaustion pattern detection"""
        self.order_flow_chart = ProfessionalChart(
            parent,
            title="🌊 Order Flow Exhaustion Patterns",
            chart_type="line",
            width=600,
            height=400
        )
        self.order_flow_chart.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

        # Create realistic order flow data with exhaustion patterns
        time_data = np.arange(0, 100, 1)

        # Generate order flow momentum with exhaustion patterns
        flow_momentum = []
        for i in range(100):
            if i < 20:  # Initial momentum
                momentum = np.random.normal(30, 10)
            elif i < 40:  # Building momentum
                momentum = np.random.normal(50, 15)
            elif i < 60:  # Exhaustion phase
                momentum = np.random.normal(20, 20)
            elif i < 80:  # Reversal
                momentum = np.random.normal(-10, 15)
            else:  # New momentum
                momentum = np.random.normal(25, 12)
            flow_momentum.append(momentum)

        self.order_flow_chart.axes.clear()

        # Plot order flow momentum
        self.order_flow_chart.axes.plot(time_data, flow_momentum,
                                       color=EXECUTIVE_COLORS['warning'],
                                       linewidth=2, label='Order Flow Momentum')

        # Add moving average for trend
        window = 10
        if len(flow_momentum) >= window:
            ma_data = np.convolve(flow_momentum, np.ones(window)/window, mode='valid')
            ma_time = time_data[window-1:]
            self.order_flow_chart.axes.plot(ma_time, ma_data,
                                           color=EXECUTIVE_COLORS['accent_primary'],
                                           linewidth=1.5, alpha=0.8,
                                           linestyle='--', label='Trend (MA10)')

        # Mark exhaustion zones
        exhaustion_zones = [(35, 65)]  # Example exhaustion zone
        for start, end in exhaustion_zones:
            self.order_flow_chart.axes.axvspan(start, end,
                                              color=EXECUTIVE_COLORS['danger'],
                                              alpha=0.2, label='Exhaustion Zone')

        # Add zero line
        self.order_flow_chart.axes.axhline(y=0, color=EXECUTIVE_COLORS['text_muted'],
                                          linestyle='-', alpha=0.5, linewidth=1)

        # Style the chart
        self.order_flow_chart.axes.set_xlabel('Time', color=EXECUTIVE_COLORS['text_primary'])
        self.order_flow_chart.axes.set_ylabel('Flow Momentum', color=EXECUTIVE_COLORS['text_primary'])
        self.order_flow_chart.axes.grid(True, alpha=0.3, color=EXECUTIVE_COLORS['border_light'])
        self.order_flow_chart.axes.set_facecolor(EXECUTIVE_COLORS['bg_tertiary'])
        self.order_flow_chart.axes.legend()

        self.order_flow_chart.canvas.draw()

    def toggle_connection(self):
        """Toggle data source connection"""
        try:
            if self.is_connected:
                self.data_source_manager.disconnect()
            else:
                self.connect_btn.config(text="🔄 Connecting...", state='disabled')
                success = self.data_source_manager.connect()
                if success:
                    # Connection successful - status callback should update UI
                    print("✅ Connection established")
                else:
                    print("❌ Failed to establish connection")
                    self.connect_btn.config(text="🔌 Connect", state='normal')
        except Exception as e:
            print(f"Error toggling connection: {e}")
            self.connect_btn.config(text="🔌 Connect", state='normal')

    def set_data_source(self, source_type: DataSourceType):
        """Set the data source type"""
        try:
            success = self.data_source_manager.set_data_source(source_type)
            if success:
                self.update_data_source_buttons()
            else:
                print(f"❌ Failed to switch to {source_type.value} data source")
        except Exception as e:
            print(f"Error setting data source: {e}")

    def update_data_source_buttons(self):
        """Update data source button appearance"""
        current_source = self.data_source_manager.get_current_source()

        if current_source == DataSourceType.LIVE:
            # Live active
            self.live_data_btn.config(
                bg=EXECUTIVE_COLORS['success'],
                fg='white',
                font=('Arial', 9, 'bold')
            )
            self.sim_data_btn.config(
                bg=EXECUTIVE_COLORS['bg_primary'],
                fg=EXECUTIVE_COLORS['text_primary'],
                font=('Arial', 9)
            )
        else:
            # Simulation active
            self.live_data_btn.config(
                bg=EXECUTIVE_COLORS['bg_primary'],
                fg=EXECUTIVE_COLORS['text_primary'],
                font=('Arial', 9)
            )
            self.sim_data_btn.config(
                bg=EXECUTIVE_COLORS['success'],
                fg='white',
                font=('Arial', 9, 'bold')
            )

    def disconnect_from_market(self):
        """Disconnect from market data"""
        print("🔌 Disconnecting from data source...")
        self.data_source_manager.disconnect()
        self.is_connected = False



    def update_ui_with_data(self, market_data, order_book_data):
        """Update UI with real market data"""
        try:
            # Update order book with real data - ensure it runs in main thread
            if self.order_book_display and hasattr(self.order_book_display, 'update_order_book'):
                # Use after_idle to ensure UI updates happen in main thread
                self.root.after_idle(
                    lambda: self.order_book_display.update_order_book(
                        order_book_data['bids'],
                        order_book_data['asks']
                    )
                )

            # Update market analytics
            self.update_market_analytics(market_data)

            # Update charts with new data
            self.update_charts(market_data)

            # Generate trading signals
            self.update_trading_signals(market_data)

        except Exception as e:
            print(f"❌ UI update error: {e}")

    def update_market_analytics(self, market_data):
        """Update market analytics metrics"""
        try:
            if hasattr(self, 'volume_metric'):
                volume = market_data.get('volume', 0)
                if volume >= 1000000:
                    volume_str = f"{volume/1000000:.1f}M"
                elif volume >= 1000:
                    volume_str = f"{volume/1000:.1f}K"
                else:
                    volume_str = str(volume)
                self.volume_metric.update_value(volume_str)

            if hasattr(self, 'spread_metric'):
                spread = np.random.uniform(0.05, 0.15)
                self.spread_metric.update_value(f"₹{spread:.2f}")

            if hasattr(self, 'rsi_metric'):
                rsi = np.random.uniform(30, 70)
                self.rsi_metric.update_value(f"{rsi:.1f}")

        except Exception as e:
            print(f"❌ Analytics update error: {e}")

    def update_charts(self, market_data):
        """Update all charts with new data"""
        try:
            current_price = market_data.get('price', 184.75)

            # Store price history
            self.price_history.append(current_price)
            if len(self.price_history) > 100:
                self.price_history.pop(0)

            # Update CVD chart
            if self.cvd_chart and len(self.price_history) > 1:
                cvd_value = np.random.randn() * 100
                self.cvd_history.append(cvd_value)
                if len(self.cvd_history) > 100:
                    self.cvd_history.pop(0)

                time_data = np.arange(len(self.cvd_history))
                cumulative_cvd = np.cumsum(self.cvd_history)
                self.cvd_chart.update_data(time_data, cumulative_cvd,
                                         color=EXECUTIVE_COLORS['success'], linewidth=2)

        except Exception as e:
            print(f"❌ Chart update error: {e}")

    def update_trading_signals(self, market_data):
        """Update trading signals"""
        try:
            if self.trading_signals_widget:
                # Generate random signal for demo
                signal_type = np.random.choice(['BUY', 'SELL'])
                current_price = market_data.get('price', 184.75)

                if signal_type == 'BUY':
                    entry_price = current_price
                    target_price = current_price + 1.0
                    stop_price = current_price - 0.5
                else:
                    entry_price = current_price
                    target_price = current_price - 1.0
                    stop_price = current_price + 0.5

                strength = np.random.randint(60, 95)

                self.trading_signals_widget.update_signal(
                    signal_type, entry_price, target_price, stop_price, strength
                )

        except Exception as e:
            print(f"❌ Signal update error: {e}")

    def update_clock(self):
        """Update clock display"""
        try:
            current_time = datetime.now().strftime("%H:%M:%S")
            self.clock_label.config(text=f"🕐 {current_time}")
            self.root.after(1000, self.update_clock)
        except:
            pass

    # Event handlers
    def on_market_data_received(self, symbol, market_data):
        """Handle market data from data source"""
        try:
            self.current_market_data = market_data
            # Process market data here - update charts, analytics, etc.
            if hasattr(market_data, 'ltp'):
                # Update any price displays or charts with market_data.ltp
                pass
        except Exception as e:
            print(f"❌ Error processing market data: {e}")

    def on_order_book_received(self, symbol, order_book):
        """Handle order book data from data source"""
        try:
            print(f"🔍 DEBUG: on_order_book_received called for {symbol}")
            print(f"🔍 DEBUG: order_book type: {type(order_book)}")
            print(f"🔍 DEBUG: order_book_display exists: {self.order_book_display is not None}")

            # Store the order book snapshot
            self.current_order_book = order_book

            # Convert OrderBookSnapshot to the format expected by OrderBookWidget
            if hasattr(order_book, 'bids') and hasattr(order_book, 'asks'):
                print(f"🔍 DEBUG: order_book has {len(order_book.bids)} bids and {len(order_book.asks)} asks")

                # Convert to tuples: bids = (qty, orders, price), asks = (price, orders, qty)
                bids = [(level.quantity, level.orders, level.price) for level in order_book.bids]
                asks = [(level.price, level.orders, level.quantity) for level in order_book.asks]

                print(f"🔍 DEBUG: Converted bids sample: {bids[:3] if bids else 'None'}")
                print(f"🔍 DEBUG: Converted asks sample: {asks[:3] if asks else 'None'}")

                # Update UI with order book data using the correct method
                if self.order_book_display and hasattr(self.order_book_display, 'update_order_book'):
                    print(f"🔍 DEBUG: Calling update_order_book directly")
                    try:
                        print(f"🔍 DEBUG: About to call update_order_book with {len(bids)} bids, {len(asks)} asks")
                        self.order_book_display.update_order_book(bids, asks)
                        print(f"🔍 DEBUG: update_order_book call completed")
                    except Exception as e:
                        print(f"❌ ERROR in update_order_book: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    print(f"🔍 DEBUG: order_book_display missing or no update_order_book method")
                    print(f"🔍 DEBUG: order_book_display type: {type(self.order_book_display)}")
                    if self.order_book_display:
                        print(f"🔍 DEBUG: Available methods: {[m for m in dir(self.order_book_display) if not m.startswith('_')]}")

                # Show best bid/ask prices for verification (only occasionally)
                if order_book.bids and order_book.asks:
                    best_bid = order_book.bids[0].price
                    best_ask = order_book.asks[0].price
                    # Only print every 10th update to reduce console spam
                    if not hasattr(self, '_order_book_update_count'):
                        self._order_book_update_count = 0
                    self._order_book_update_count += 1
                    if self._order_book_update_count % 10 == 0:
                        print(f"📊 {symbol} order book - Best Bid: ₹{best_bid:.2f}, Best Ask: ₹{best_ask:.2f}, Spread: ₹{(best_ask - best_bid):.2f}")
            else:
                print(f"❌ Order book missing bids/asks: {type(order_book)}")
                print(f"🔍 DEBUG: order_book attributes: {dir(order_book)}")

        except Exception as e:
            print(f"❌ Error processing order book: {e}")
            import traceback
            traceback.print_exc()

    def on_connection_status_changed(self, status):
        """Handle connection status changes"""
        print(f"📡 Connection status: {status}")

        # Update UI based on status - ensure it runs in main thread
        def update_ui():
            if "CONNECTED_LIVE" in status:
                self.is_connected = True
                self.connect_btn.config(text="🔌 Disconnect", bg=EXECUTIVE_COLORS['danger'], state='normal')
                self.status_label.config(text="🟢 Live Data", fg=EXECUTIVE_COLORS['success'])
            elif "CONNECTED_SIMULATED" in status:
                self.is_connected = True
                self.connect_btn.config(text="🔌 Disconnect", bg=EXECUTIVE_COLORS['danger'], state='normal')
                self.status_label.config(text="🟢 Simulated", fg=EXECUTIVE_COLORS['success'])
            elif status == "DISCONNECTED":
                self.is_connected = False
                self.connect_btn.config(text="🔌 Connect", bg=EXECUTIVE_COLORS['success'], state='normal')
                self.status_label.config(text="⚫ Disconnected", fg=EXECUTIVE_COLORS['text_muted'])

        # Ensure UI update happens in main thread
        self.root.after_idle(update_ui)

    def on_order_book_updated(self, event_data):
        """Handle order book update events"""
        pass

    def on_flow_imbalance(self, event_data):
        """Handle flow imbalance detection"""
        pass

    def on_trading_signal(self, event_data):
        """Handle trading signal generation"""
        pass

    def run(self):
        """Start the application"""
        print("🚀 Starting Ultimate Trading Analysis v1...")
        print("📊 All components from YouTube analysis integrated:")
        print("   ✅ Order Book (Real-time bid/ask)")
        print("   ✅ Footprint Chart (Volume at price levels)")
        print("   ✅ Volume Profile (Fair value analysis)")
        print("   ✅ CVD Chart (Cumulative Volume Delta)")
        print("   ✅ Order Flow Analysis (Market microstructure)")
        print("   ✅ Market Analytics (Volume, spread, volatility, RSI)")
        print("   ✅ Trading Signals (Entry, target, stop levels)")
        print("🎯 Starting with CGCL simulation...")

        # Auto-start simulation
        self.start_simulation()

        print("🔌 Use Live/Sim buttons to switch data sources!")
        print("🔄 Use Connect/Disconnect to control data flow!")

        self.root.mainloop()

    def start_simulation(self):
        """Start simulation automatically"""
        try:
            print("🎯 Starting CGCL simulation...")
            success = self.data_source_manager.connect()
            if success:
                self.update_data_source_buttons()
                # Manually trigger connection status update since auto-start might not trigger callback
                self.on_connection_status_changed("CONNECTED_SIMULATED")
                print("✅ CGCL simulation started successfully")
            else:
                print("❌ Failed to start simulation")
        except Exception as e:
            print(f"Error starting simulation: {e}")

def main():
    """Main function"""
    try:
        print("🚀 Starting Ultimate Trading Analysis v1...")
        app = UltimateTradingAnalysisV1()
        app.run()
    except Exception as e:
        print(f"❌ Application error: {e}")
        import traceback
        traceback.print_exc()
        messagebox.showerror("Error", f"Application failed to start: {e}")

if __name__ == "__main__":
    main()
