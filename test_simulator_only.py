"""
Test script for CGCL Market Simulator only (no GUI)
"""

import sys
import os
import time

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'updatted best plan'))

# Now import from the correct path
from core.market_simulator import CGCLMarketSimulator


def test_simulator():
    """Test the market simulator independently"""
    print("🎯 Testing CGCL Market Simulator")
    print("="*40)
    
    # Create simulator
    simulator = CGCLMarketSimulator()
    
    # Set up callbacks to display data
    def on_order_book_update(symbol, order_book):
        print(f"\n📊 Order Book Update for {symbol}")
        print(f"Timestamp: {order_book.timestamp}")
        print(f"Current Price: ₹{order_book.market_data.ltp:.2f}")
        print(f"Spread: ₹{order_book.spread:.2f}")

        print("\nTop 3 Bids:")
        for i, bid in enumerate(order_book.bids[:3]):
            print(f"  {i+1}. ₹{bid.price:.2f} x {bid.quantity:,} ({bid.orders} orders)")

        print("Top 3 Asks:")
        for i, ask in enumerate(order_book.asks[:3]):
            print(f"  {i+1}. ₹{ask.price:.2f} x {ask.quantity:,} ({ask.orders} orders)")

    def on_market_data_update(symbol, market_data):
        change_sign = "+" if market_data.price_change >= 0 else ""
        print(f"💹 {symbol}: ₹{market_data.ltp:.2f} ({change_sign}{market_data.price_change:.2f}, {market_data.price_change_pct:+.2f}%)")
    
    # Set callbacks
    simulator.on_order_book_update = on_order_book_update
    simulator.on_market_data_update = on_market_data_update
    
    # Start simulation
    print("🚀 Starting simulation...")
    simulator.start_simulation()
    
    try:
        # Let it run for 30 seconds
        print("⏱️ Running for 30 seconds... (Press Ctrl+C to stop early)")
        time.sleep(30)
        
    except KeyboardInterrupt:
        print("\n⏹️ Stopped by user")
    
    finally:
        # Stop simulation
        simulator.stop_simulation()
        
        # Show final statistics
        stats = simulator.get_statistics()
        print("\n📈 Final Statistics:")
        print(f"  Base Price: ₹{stats['base_price']:.2f}")
        print(f"  Final Price: ₹{stats['current_price']:.2f}")
        print(f"  Total Change: ₹{stats['price_change']:.2f} ({stats['price_change_percent']:+.2f}%)")
        print(f"  Trend Strength: {stats['trend_strength']:.2f}")
        print(f"  History Length: {stats['history_length']} data points")
        
        print("\n✅ Simulator test completed")


if __name__ == "__main__":
    test_simulator()
