"""
Drag & Drop UI Builder Tool for CGCL Trading Dashboard
Create custom layouts by dragging components from tray to canvas
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np

# Colors
COLORS = {
    'bg_primary': '#0a0a0a',
    'bg_secondary': '#1a1a1a',
    'bg_tertiary': '#2a2a2a',
    'bg_accent': '#3a3a3a',
    'text_primary': '#ffffff',
    'text_secondary': '#b0b0b0',
    'text_muted': '#808080',
    'accent_blue': '#00d4ff',
    'accent_green': '#00ff88',
    'accent_red': '#ff4757',
    'accent_orange': '#ffa726',
    'accent_purple': '#9c27b0',
    'border': '#404040',
}

class DraggableComponent:
    """Represents a draggable UI component with individual width/height resizers"""

    def __init__(self, canvas, comp_type, x, y, width=None, height=None, parent_tool=None):
        self.canvas = canvas
        self.comp_type = comp_type
        self.x = x
        self.y = y
        self.parent_tool = parent_tool

        # Set recommended sizes based on component type
        if width is None or height is None:
            self.width, self.height = self.get_recommended_size()
        else:
            self.width = width
            self.height = height

        self.selected = False
        self.resizing = False
        self.resize_mode = None  # 'width', 'height', 'both'

        # Create visual representation
        self.rect = canvas.create_rectangle(
            x, y, x + self.width, y + self.height,
            fill=COLORS['bg_secondary'],
            outline=COLORS['accent_blue'],
            width=2
        )

        # Add component preview
        self.preview_items = []
        self.create_preview()

        # Bind events
        canvas.tag_bind(self.rect, "<Button-1>", self.on_click)
        canvas.tag_bind(self.rect, "<B1-Motion>", self.on_drag)
        canvas.tag_bind(self.rect, "<ButtonRelease-1>", self.on_release)
        canvas.tag_bind(self.rect, "<Button-3>", self.on_right_click)  # Right-click for delete

        # Resize handles
        self.resize_handles = {}
        self.create_resize_handles()

    def get_recommended_size(self):
        """Get recommended size for each component type"""
        sizes = {
            "Order Book": (350, 400),
            "CVD Chart": (600, 300),
            "Volume Profile": (400, 350),
            "Footprint Chart": (500, 300),
            "Order Flow": (600, 250),
            "Trading Signals": (300, 350),
            "Market Analytics": (400, 200)
        }
        return sizes.get(self.comp_type, (300, 200))
    
    def create_preview(self):
        """Create an improved preview of the component"""
        self.clear_preview()

        center_x = self.x + self.width // 2
        center_y = self.y + self.height // 2

        # Component title with better styling
        self.title_text = self.canvas.create_text(
            center_x, self.y + 15,
            text=self.comp_type,
            fill=COLORS['accent_blue'],
            font=('Arial', 11, 'bold')
        )
        self.preview_items.append(self.title_text)

        # Size indicator (show actual size that will be used in final dashboard)
        if hasattr(self.parent_tool, 'scale_factor'):
            actual_width = int(self.width / self.parent_tool.scale_factor)
            actual_height = int(self.height / self.parent_tool.scale_factor)
            size_text = f"{actual_width}×{actual_height}"
        else:
            size_text = f"{self.width}×{self.height}"

        self.size_text = self.canvas.create_text(
            self.x + self.width - 5, self.y + 5,
            text=size_text,
            fill=COLORS['text_muted'],
            font=('Arial', 8),
            anchor='ne'
        )
        self.preview_items.append(self.size_text)

        # Component-specific improved preview
        if self.comp_type == "Order Book":
            self.create_improved_order_book_preview()
        elif self.comp_type == "CVD Chart":
            self.create_improved_chart_preview("CVD", COLORS['accent_green'])
        elif self.comp_type == "Volume Profile":
            self.create_improved_chart_preview("Volume", COLORS['accent_orange'])
        elif self.comp_type == "Footprint Chart":
            self.create_improved_chart_preview("Footprint", COLORS['accent_purple'])
        elif self.comp_type == "Order Flow":
            self.create_improved_chart_preview("Flow", COLORS['accent_red'])
        elif self.comp_type == "Trading Signals":
            self.create_improved_signals_preview()
        elif self.comp_type == "Market Analytics":
            self.create_improved_analytics_preview()
    
    def create_improved_order_book_preview(self):
        """Create improved order book preview"""
        if self.width < 200 or self.height < 150:
            return

        # Headers with background
        y_start = self.y + 35
        header_rect = self.canvas.create_rectangle(
            self.x + 10, y_start - 5, self.x + self.width - 10, y_start + 15,
            fill=COLORS['bg_accent'], outline=COLORS['border']
        )
        self.preview_items.append(header_rect)

        bid_header = self.canvas.create_text(self.x + self.width//4, y_start + 5,
                                           text="Bid", fill=COLORS['accent_green'], font=('Arial', 9, 'bold'))
        ask_header = self.canvas.create_text(self.x + 3*self.width//4, y_start + 5,
                                           text="Ask", fill=COLORS['accent_red'], font=('Arial', 9, 'bold'))
        self.preview_items.extend([bid_header, ask_header])

        # Sample rows with better formatting
        rows_to_show = min(8, (self.height - 80) // 20)
        for i in range(rows_to_show):
            y_pos = y_start + 25 + (i * 18)

            # Bid side
            bid_price = self.canvas.create_text(self.x + self.width//4 - 20, y_pos,
                                              text=f"₹{184.70-i*0.05:.2f}",
                                              fill=COLORS['accent_green'], font=('Arial', 8))
            bid_qty = self.canvas.create_text(self.x + self.width//4 + 20, y_pos,
                                            text=f"{1250-i*50}",
                                            fill=COLORS['text_secondary'], font=('Arial', 7))

            # Ask side
            ask_price = self.canvas.create_text(self.x + 3*self.width//4 - 20, y_pos,
                                              text=f"₹{184.75+i*0.05:.2f}",
                                              fill=COLORS['accent_red'], font=('Arial', 8))
            ask_qty = self.canvas.create_text(self.x + 3*self.width//4 + 20, y_pos,
                                            text=f"{980+i*30}",
                                            fill=COLORS['text_secondary'], font=('Arial', 7))

            self.preview_items.extend([bid_price, bid_qty, ask_price, ask_qty])
    
    def create_improved_chart_preview(self, chart_name, color):
        """Create improved chart preview with realistic data visualization"""
        if self.width < 150 or self.height < 100:
            return

        # Chart area background
        chart_bg = self.canvas.create_rectangle(
            self.x + 15, self.y + 30, self.x + self.width - 15, self.y + self.height - 15,
            fill=COLORS['bg_tertiary'], outline=COLORS['border']
        )
        self.preview_items.append(chart_bg)

        # Chart type specific visualization
        if chart_name == "CVD":
            self.create_cvd_mini_chart(color)
        elif chart_name == "Volume":
            self.create_volume_profile_mini_chart(color)
        elif chart_name == "Footprint":
            self.create_footprint_mini_chart(color)
        elif chart_name == "Flow":
            self.create_flow_mini_chart(color)

    def create_cvd_mini_chart(self, color):
        """Create mini CVD chart"""
        # Price line
        price_points = []
        cvd_points = []
        chart_width = self.width - 40
        chart_height = (self.height - 60) // 2

        for i in range(10):
            x_pos = self.x + 20 + (i * chart_width // 9)
            price_y = self.y + 40 + chart_height//2 + int(10 * np.sin(i * 0.5))
            cvd_y = self.y + 40 + chart_height + chart_height//2 + int(8 * np.cos(i * 0.3))

            price_points.extend([x_pos, price_y])
            cvd_points.extend([x_pos, cvd_y])

        if len(price_points) >= 4:
            price_line = self.canvas.create_line(price_points, fill=COLORS['accent_blue'], width=2, smooth=True)
            cvd_line = self.canvas.create_line(cvd_points, fill=color, width=2, smooth=True)
            self.preview_items.extend([price_line, cvd_line])

    def create_volume_profile_mini_chart(self, color):
        """Create mini volume profile chart"""
        chart_height = self.height - 60
        bar_count = min(8, chart_height // 15)

        for i in range(bar_count):
            y_pos = self.y + 35 + (i * (chart_height // bar_count))
            bar_width = 20 + int(30 * np.random.random())

            bar = self.canvas.create_rectangle(
                self.x + 20, y_pos, self.x + 20 + bar_width, y_pos + 10,
                fill=color, outline=color
            )
            self.preview_items.append(bar)

    def create_footprint_mini_chart(self, color):
        """Create mini footprint chart"""
        chart_height = self.height - 60
        levels = min(6, chart_height // 20)

        for i in range(levels):
            y_pos = self.y + 35 + (i * (chart_height // levels))
            center_x = self.x + self.width // 2

            # Bid bar (left)
            bid_width = 15 + int(20 * np.random.random())
            bid_bar = self.canvas.create_rectangle(
                center_x - bid_width, y_pos, center_x, y_pos + 12,
                fill=COLORS['accent_green'], outline=COLORS['accent_green']
            )

            # Ask bar (right)
            ask_width = 15 + int(20 * np.random.random())
            ask_bar = self.canvas.create_rectangle(
                center_x, y_pos, center_x + ask_width, y_pos + 12,
                fill=COLORS['accent_red'], outline=COLORS['accent_red']
            )

            self.preview_items.extend([bid_bar, ask_bar])

    def create_flow_mini_chart(self, color):
        """Create mini order flow chart"""
        # Flow strength line
        points = []
        chart_width = self.width - 40
        chart_height = self.height - 60

        for i in range(12):
            x_pos = self.x + 20 + (i * chart_width // 11)
            # Create wave pattern
            y_pos = self.y + 35 + chart_height//2 + int(15 * np.sin(i * 0.8) * np.exp(-i * 0.1))
            points.extend([x_pos, y_pos])

        if len(points) >= 4:
            flow_line = self.canvas.create_line(points, fill=color, width=2, smooth=True)
            self.preview_items.append(flow_line)
    
    def create_improved_signals_preview(self):
        """Create improved trading signals preview"""
        if self.width < 200 or self.height < 150:
            return

        # Signal button
        button_width = min(120, self.width - 40)
        button_height = 35
        y_start = self.y + 45

        signal_rect = self.canvas.create_rectangle(
            self.x + (self.width - button_width)//2, y_start,
            self.x + (self.width + button_width)//2, y_start + button_height,
            fill=COLORS['accent_green'], outline=COLORS['accent_green']
        )
        signal_text = self.canvas.create_text(
            self.x + self.width//2, y_start + button_height//2,
            text="BUY", fill='white', font=('Arial', 12, 'bold')
        )
        self.preview_items.extend([signal_rect, signal_text])

        # Signal details
        details = [
            ("Entry: ₹184.75", COLORS['text_secondary']),
            ("Target: ₹186.25", COLORS['accent_green']),
            ("Stop: ₹183.50", COLORS['accent_red']),
            ("R/R: 1:3.5", COLORS['accent_blue'])
        ]

        detail_start_y = y_start + button_height + 15
        for i, (detail, color) in enumerate(details):
            if detail_start_y + (i * 18) < self.y + self.height - 10:
                detail_text = self.canvas.create_text(
                    self.x + self.width//2, detail_start_y + (i * 18),
                    text=detail, fill=color, font=('Arial', 9)
                )
                self.preview_items.append(detail_text)

    def create_improved_analytics_preview(self):
        """Create improved analytics preview"""
        if self.width < 150 or self.height < 100:
            return

        # Analytics grid
        metrics = [
            ("Volume", "1.2M", COLORS['accent_blue']),
            ("Spread", "₹0.05", COLORS['accent_orange']),
            ("Volatility", "Low", COLORS['accent_green']),
            ("Trend", "Bullish", COLORS['accent_green']),
            ("RSI", "65.2", COLORS['accent_orange']),
            ("MACD", "Bullish", COLORS['accent_green'])
        ]

        y_start = self.y + 40
        rows_to_show = min(len(metrics), (self.height - 60) // 20)

        for i in range(rows_to_show):
            metric, value, color = metrics[i]
            y_pos = y_start + (i * 20)

            # Metric name
            metric_text = self.canvas.create_text(
                self.x + 15, y_pos, text=metric + ":",
                fill=COLORS['text_secondary'], font=('Arial', 8), anchor='w'
            )

            # Metric value
            value_text = self.canvas.create_text(
                self.x + self.width - 15, y_pos, text=value,
                fill=color, font=('Arial', 8, 'bold'), anchor='e'
            )

            self.preview_items.extend([metric_text, value_text])
    
    def create_resize_handles(self):
        """Create individual width and height resize handles"""
        handle_size = 8

        # Right edge handle (width resize)
        self.resize_handles['width'] = self.canvas.create_rectangle(
            self.x + self.width - handle_size//2, self.y + self.height//2 - handle_size,
            self.x + self.width + handle_size//2, self.y + self.height//2 + handle_size,
            fill=COLORS['accent_orange'], outline=COLORS['accent_orange']
        )

        # Bottom edge handle (height resize)
        self.resize_handles['height'] = self.canvas.create_rectangle(
            self.x + self.width//2 - handle_size, self.y + self.height - handle_size//2,
            self.x + self.width//2 + handle_size, self.y + self.height + handle_size//2,
            fill=COLORS['accent_purple'], outline=COLORS['accent_purple']
        )

        # Bottom-right corner handle (both width and height)
        self.resize_handles['both'] = self.canvas.create_rectangle(
            self.x + self.width - handle_size, self.y + self.height - handle_size,
            self.x + self.width, self.y + self.height,
            fill=COLORS['accent_blue'], outline=COLORS['accent_blue']
        )

        # Bind resize events
        for mode, handle in self.resize_handles.items():
            self.canvas.tag_bind(handle, "<Button-1>", lambda e, m=mode: self.start_resize(e, m))
            self.canvas.tag_bind(handle, "<B1-Motion>", self.on_resize)
            self.canvas.tag_bind(handle, "<ButtonRelease-1>", self.end_resize)

        # Change cursor on hover
        for handle in self.resize_handles.values():
            self.canvas.tag_bind(handle, "<Enter>", self.on_resize_enter)
            self.canvas.tag_bind(handle, "<Leave>", self.on_resize_leave)
    
    def on_click(self, event):
        """Handle click event"""
        self.selected = True
        self.canvas.itemconfig(self.rect, outline=COLORS['accent_orange'], width=3)
        self.start_x = event.x
        self.start_y = event.y

    def on_drag(self, event):
        """Handle drag event"""
        if self.selected and not self.resizing:
            dx = event.x - self.start_x
            dy = event.y - self.start_y

            # Move main rectangle
            self.canvas.move(self.rect, dx, dy)

            # Move all preview items
            for item in self.preview_items:
                self.canvas.move(item, dx, dy)

            # Move resize handles
            for handle in self.resize_handles.values():
                self.canvas.move(handle, dx, dy)

            self.x += dx
            self.y += dy
            self.start_x = event.x
            self.start_y = event.y

    def on_release(self, event):
        """Handle release event"""
        self.selected = False
        self.canvas.itemconfig(self.rect, outline=COLORS['accent_blue'], width=2)

    def on_right_click(self, event):
        """Handle right-click for delete"""
        if messagebox.askyesno("Delete Component", f"Delete {self.comp_type} component?"):
            if self.parent_tool:
                self.parent_tool.remove_component(self)
            self.delete()

    def start_resize(self, event, mode):
        """Start resize operation"""
        self.resizing = True
        self.resize_mode = mode
        self.resize_start_x = event.x
        self.resize_start_y = event.y

    def on_resize(self, event):
        """Handle resize operation with individual width/height control"""
        if self.resizing and self.resize_mode:
            dx = event.x - self.resize_start_x
            dy = event.y - self.resize_start_y

            new_width = self.width
            new_height = self.height

            # Apply resize based on mode
            if self.resize_mode in ['width', 'both']:
                new_width = max(100, self.width + dx)
            if self.resize_mode in ['height', 'both']:
                new_height = max(80, self.height + dy)

            # Update component
            self.update_size(new_width, new_height)

            self.resize_start_x = event.x
            self.resize_start_y = event.y

    def end_resize(self, event):
        """End resize operation"""
        self.resizing = False
        self.resize_mode = None

    def on_resize_enter(self, event):
        """Change cursor on resize handle hover"""
        self.canvas.config(cursor="sizing")

    def on_resize_leave(self, event):
        """Reset cursor when leaving resize handle"""
        self.canvas.config(cursor="")

    def update_size(self, new_width, new_height):
        """Update component size and recreate elements"""
        self.width = new_width
        self.height = new_height

        # Update main rectangle
        self.canvas.coords(self.rect, self.x, self.y, self.x + self.width, self.y + self.height)

        # Update resize handles
        self.update_resize_handles()

        # Recreate preview
        self.create_preview()

    def update_resize_handles(self):
        """Update resize handle positions"""
        handle_size = 8

        # Width handle (right edge)
        self.canvas.coords(self.resize_handles['width'],
            self.x + self.width - handle_size//2, self.y + self.height//2 - handle_size,
            self.x + self.width + handle_size//2, self.y + self.height//2 + handle_size)

        # Height handle (bottom edge)
        self.canvas.coords(self.resize_handles['height'],
            self.x + self.width//2 - handle_size, self.y + self.height - handle_size//2,
            self.x + self.width//2 + handle_size, self.y + self.height + handle_size//2)

        # Both handle (corner)
        self.canvas.coords(self.resize_handles['both'],
            self.x + self.width - handle_size, self.y + self.height - handle_size,
            self.x + self.width, self.y + self.height)

    def clear_preview(self):
        """Clear existing preview elements"""
        for item in self.preview_items:
            self.canvas.delete(item)
        self.preview_items.clear()

    def delete(self):
        """Delete this component"""
        # Delete all canvas items
        self.canvas.delete(self.rect)
        for item in self.preview_items:
            self.canvas.delete(item)
        for handle in self.resize_handles.values():
            self.canvas.delete(handle)

        # Remove from parent's component list
        # This will be called by the parent UIBuilderTool
    
    def get_config(self):
        """Get component configuration (in actual 1920x1080 coordinates)"""
        if hasattr(self.parent_tool, 'scale_factor'):
            # Convert back to actual coordinates for saving
            actual_x = int(self.x / self.parent_tool.scale_factor)
            actual_y = int(self.y / self.parent_tool.scale_factor)
            actual_width = int(self.width / self.parent_tool.scale_factor)
            actual_height = int(self.height / self.parent_tool.scale_factor)
        else:
            actual_x, actual_y = self.x, self.y
            actual_width, actual_height = self.width, self.height

        return {
            'type': self.comp_type,
            'x': actual_x,
            'y': actual_y,
            'width': actual_width,
            'height': actual_height
        }

class UIBuilderTool:
    """Main UI Builder Tool"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("CGCL Trading Dashboard - UI Builder Tool (WYSIWYG)")
        self.root.geometry("1600x1000")
        self.root.configure(bg=COLORS['bg_primary'])
        self.root.state('zoomed')  # Maximize for best view

        self.components = []
        self.scale_factor = 1.0  # Will be set in create_design_canvas
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the main UI"""
        # Main container
        main_frame = tk.Frame(self.root, bg=COLORS['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Component Tray
        self.create_component_tray(main_frame)
        
        # Right panel - Design Canvas
        self.create_design_canvas(main_frame)
        
        # Bottom panel - Controls
        self.create_controls(main_frame)
    
    def create_component_tray(self, parent):
        """Create component tray on the left"""
        tray_frame = tk.Frame(parent, bg=COLORS['bg_secondary'], width=250)
        tray_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        tray_frame.pack_propagate(False)
        
        # Title
        tk.Label(tray_frame, text="📦 Component Tray", 
                bg=COLORS['bg_secondary'], fg=COLORS['accent_blue'],
                font=('Arial', 14, 'bold')).pack(pady=10)
        
        # Component buttons
        components = [
            ("📊 Order Book", "Order Book"),
            ("📈 CVD Chart", "CVD Chart"),
            ("📊 Volume Profile", "Volume Profile"),
            ("🦶 Footprint Chart", "Footprint Chart"),
            ("🌊 Order Flow", "Order Flow"),
            ("⚡ Trading Signals", "Trading Signals"),
            ("📈 Market Analytics", "Market Analytics")
        ]
        
        for display_name, comp_type in components:
            btn = tk.Button(tray_frame, text=display_name,
                          bg=COLORS['bg_tertiary'], fg=COLORS['text_primary'],
                          font=('Arial', 10), relief=tk.FLAT,
                          command=lambda ct=comp_type: self.add_component(ct))
            btn.pack(fill=tk.X, padx=10, pady=2)
    
    def create_design_canvas(self, parent):
        """Create design canvas with vertical scroll only"""
        canvas_frame = tk.Frame(parent, bg=COLORS['bg_primary'])
        canvas_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Title with canvas info
        title_frame = tk.Frame(canvas_frame, bg=COLORS['bg_primary'])
        title_frame.pack(fill=tk.X, pady=5)

        tk.Label(title_frame, text="🎨 Design Canvas (1920×1080 - Vertical Scroll Only)",
                bg=COLORS['bg_primary'], fg=COLORS['accent_blue'],
                font=('Arial', 14, 'bold')).pack()

        tk.Label(title_frame, text="Full width view • Scroll vertically for tall layouts",
                bg=COLORS['bg_primary'], fg=COLORS['text_secondary'],
                font=('Arial', 9)).pack()

        # Calculate scale to fit width only (height can scroll)
        available_width = 1000  # Available width after component tray

        # Scale to fit width, height will be scrollable
        self.scale_factor = available_width / 1920
        self.canvas_width = int(1920 * self.scale_factor)
        self.canvas_height = int(1080 * self.scale_factor)  # Minimum height

        print(f"📐 Canvas scale: {self.scale_factor:.3f} (Width: {self.canvas_width}px)")

        # Canvas container with vertical scrollbar
        canvas_container = tk.Frame(canvas_frame, bg=COLORS['bg_primary'])
        canvas_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create scrollable canvas with vertical scrollbar only
        self.canvas = tk.Canvas(canvas_container,
                               bg=COLORS['bg_tertiary'],
                               highlightthickness=2,
                               highlightbackground=COLORS['accent_blue'],
                               scrollregion=(0, 0, self.canvas_width, self.canvas_height * 2))  # Allow 2x height for scrolling

        # Vertical scrollbar only
        v_scrollbar = ttk.Scrollbar(canvas_container, orient="vertical", command=self.canvas.yview)
        self.canvas.configure(yscrollcommand=v_scrollbar.set)

        # Pack canvas and scrollbar
        self.canvas.pack(side="left", fill="both", expand=True)
        v_scrollbar.pack(side="right", fill="y")

        # Bind mousewheel for vertical scrolling
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        self.canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # Grid lines for alignment (scaled)
        self.draw_proportional_grid()

        # Canvas boundary indicator (1920x1080 area)
        self.canvas.create_rectangle(1, 1, self.canvas_width-2, self.canvas_height-2,
                                   outline=COLORS['accent_blue'], width=2)

        # Add scale indicator
        scale_text = f"Scale: {self.scale_factor:.1%} (1920 width → {self.canvas_width}px)"
        self.canvas.create_text(10, 20, text=scale_text,
                               fill=COLORS['text_muted'], font=('Arial', 8), anchor='w')

        # Add scroll hint
        scroll_hint = "↕️ Scroll vertically for tall layouts"
        self.canvas.create_text(self.canvas_width-10, 20, text=scroll_hint,
                               fill=COLORS['text_muted'], font=('Arial', 8), anchor='e')
    
    def create_controls(self, parent):
        """Create control buttons at bottom"""
        controls_frame = tk.Frame(parent, bg=COLORS['bg_secondary'], height=80)
        controls_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))
        controls_frame.pack_propagate(False)

        # Top row of buttons
        top_buttons = tk.Frame(controls_frame, bg=COLORS['bg_secondary'])
        top_buttons.pack(fill=tk.X, pady=5)

        tk.Button(top_buttons, text="💾 Save Layout",
                 bg=COLORS['accent_green'], fg='white', font=('Arial', 10, 'bold'),
                 command=self.save_layout).pack(side=tk.LEFT, padx=5)

        tk.Button(top_buttons, text="📁 Load Layout",
                 bg=COLORS['accent_blue'], fg='white', font=('Arial', 10, 'bold'),
                 command=self.load_layout).pack(side=tk.LEFT, padx=5)

        tk.Button(top_buttons, text="🗑️ Clear All",
                 bg=COLORS['accent_red'], fg='white', font=('Arial', 10, 'bold'),
                 command=self.clear_all).pack(side=tk.LEFT, padx=5)

        tk.Button(top_buttons, text="🚀 Generate Dashboard",
                 bg=COLORS['accent_purple'], fg='white', font=('Arial', 10, 'bold'),
                 command=self.generate_code).pack(side=tk.LEFT, padx=5)

        # Component count
        self.component_count_label = tk.Label(top_buttons,
                text=f"Components: {len(self.components)}",
                bg=COLORS['bg_secondary'], fg=COLORS['text_primary'],
                font=('Arial', 10, 'bold'))
        self.component_count_label.pack(side=tk.RIGHT, padx=10)

        # Bottom row - Instructions
        instructions = tk.Frame(controls_frame, bg=COLORS['bg_secondary'])
        instructions.pack(fill=tk.X, pady=2)

        tk.Label(instructions,
                text="🎯 Full 1920 width view • ↕️ Vertical scroll only • 🖱️ Drag: Move • 🟠 Width • 🟣 Height • 🔵 Both • Right-click: Delete",
                bg=COLORS['bg_secondary'], fg=COLORS['text_secondary'],
                font=('Arial', 9)).pack()
    
    def draw_proportional_grid(self):
        """Draw proportional grid lines for alignment (extended for scrolling)"""
        # Calculate grid spacing based on scale
        minor_grid = int(50 * self.scale_factor)
        major_grid = int(200 * self.scale_factor)

        # Extended height for scrolling (2x the base height)
        extended_height = self.canvas_height * 2

        # Minor grid lines
        for x in range(0, self.canvas_width, minor_grid):
            self.canvas.create_line(x, 0, x, extended_height,
                                  fill=COLORS['border'], width=1, stipple="gray25")

        for y in range(0, extended_height, minor_grid):
            self.canvas.create_line(0, y, self.canvas_width, y,
                                  fill=COLORS['border'], width=1, stipple="gray25")

        # Major grid lines
        for x in range(0, self.canvas_width, major_grid):
            self.canvas.create_line(x, 0, x, extended_height,
                                  fill=COLORS['border'], width=2, stipple="gray50")

        for y in range(0, extended_height, major_grid):
            self.canvas.create_line(0, y, self.canvas_width, y,
                                  fill=COLORS['border'], width=2, stipple="gray50")

    def add_component(self, comp_type):
        """Add a new component to the canvas with smart positioning (scaled)"""
        # Get recommended size and scale it
        recommended_width, recommended_height = self.get_component_recommended_size(comp_type)
        scaled_width = int(recommended_width * self.scale_factor)
        scaled_height = int(recommended_height * self.scale_factor)

        # Smart positioning to avoid overlap (scaled)
        base_x = int(50 * self.scale_factor)
        base_y = int(50 * self.scale_factor)
        spacing_x = int(400 * self.scale_factor)
        spacing_y = int(300 * self.scale_factor)

        x = base_x + (len(self.components) % 3) * spacing_x
        y = base_y + (len(self.components) // 3) * spacing_y

        # Ensure component fits within canvas
        if x + scaled_width > self.canvas_width - 20:
            x = base_x
            y += spacing_y

        component = DraggableComponent(self.canvas, comp_type, x, y,
                                     scaled_width, scaled_height, parent_tool=self)
        self.components.append(component)

        # Update component count
        self.update_component_count()

        actual_width = int(component.width / self.scale_factor)
        actual_height = int(component.height / self.scale_factor)
        print(f"✅ Added {comp_type} at ({x}, {y}) - Display: {component.width}×{component.height} - Actual: {actual_width}×{actual_height}")

    def get_component_recommended_size(self, comp_type):
        """Get recommended size for each component type"""
        sizes = {
            "Order Book": (350, 400),
            "CVD Chart": (600, 300),
            "Volume Profile": (400, 350),
            "Footprint Chart": (500, 300),
            "Order Flow": (600, 250),
            "Trading Signals": (300, 350),
            "Market Analytics": (400, 200)
        }
        return sizes.get(comp_type, (300, 200))

    def update_component_count(self):
        """Update component count display"""
        if hasattr(self, 'component_count_label'):
            self.component_count_label.config(text=f"Components: {len(self.components)}")

    def remove_component(self, component):
        """Remove a component from the list"""
        if component in self.components:
            self.components.remove(component)
            self.update_component_count()
            print(f"🗑️ Removed {component.comp_type} component")
    
    def save_layout(self):
        """Save current layout to JSON file"""
        layout_data = {
            'components': [comp.get_config() for comp in self.components]
        }
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="Save Layout"
        )
        
        if filename:
            with open(filename, 'w') as f:
                json.dump(layout_data, f, indent=2)
            messagebox.showinfo("Success", f"Layout saved to {filename}")
    
    def load_layout(self):
        """Load layout from JSON file"""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="Load Layout"
        )
        
        if filename:
            try:
                with open(filename, 'r') as f:
                    layout_data = json.load(f)
                
                self.clear_all()
                
                for comp_data in layout_data['components']:
                    # Scale the loaded coordinates to current canvas size
                    scaled_x = int(comp_data['x'] * self.scale_factor)
                    scaled_y = int(comp_data['y'] * self.scale_factor)
                    scaled_width = int(comp_data['width'] * self.scale_factor)
                    scaled_height = int(comp_data['height'] * self.scale_factor)

                    component = DraggableComponent(
                        self.canvas, comp_data['type'],
                        scaled_x, scaled_y,
                        scaled_width, scaled_height,
                        parent_tool=self
                    )
                    self.components.append(component)

                self.update_component_count()
                
                messagebox.showinfo("Success", f"Layout loaded from {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load layout: {e}")
    
    def clear_all(self):
        """Clear all components"""
        self.canvas.delete("all")
        self.components.clear()

        # Redraw proportional grid
        self.draw_proportional_grid()

        # Redraw canvas boundary (1920x1080 area)
        self.canvas.create_rectangle(1, 1, self.canvas_width-2, self.canvas_height-2,
                                   outline=COLORS['accent_blue'], width=2)

        # Re-add scale indicator
        scale_text = f"Scale: {self.scale_factor:.1%} (1920 width → {self.canvas_width}px)"
        self.canvas.create_text(10, 20, text=scale_text,
                               fill=COLORS['text_muted'], font=('Arial', 8), anchor='w')

        # Re-add scroll hint
        scroll_hint = "↕️ Scroll vertically for tall layouts"
        self.canvas.create_text(self.canvas_width-10, 20, text=scroll_hint,
                               fill=COLORS['text_muted'], font=('Arial', 8), anchor='e')

        self.update_component_count()
        print("🗑️ Cleared all components")
    
    def generate_code(self):
        """Generate Python code for the current layout"""
        if not self.components:
            messagebox.showwarning("Warning", "No components to generate code for!")
            return

        # Generate actual dashboard code
        self.create_dashboard_from_layout()

        layout_data = {
            'components': [comp.get_config() for comp in self.components]
        }

        code_window = tk.Toplevel(self.root)
        code_window.title("Generated Dashboard Code")
        code_window.geometry("800x600")
        code_window.configure(bg=COLORS['bg_primary'])

        text_widget = tk.Text(code_window, bg=COLORS['bg_secondary'],
                             fg=COLORS['text_primary'], font=('Consolas', 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        code = f"""# Generated CGCL Dashboard Layout
# Components: {len(self.components)}
# File saved as: generated_dashboard.py

layout_config = {json.dumps(layout_data, indent=2)}

# Dashboard has been generated and saved!
# Run: python generated_dashboard.py
"""
        text_widget.insert(tk.END, code)

        messagebox.showinfo("Success", "Dashboard code generated and saved as 'generated_dashboard.py'!")
        print("🚀 Generated dashboard code and saved to file")

    def create_dashboard_from_layout(self):
        """Create actual dashboard file from current layout"""
        dashboard_code = self.generate_dashboard_code()

        with open("generated_dashboard.py", "w") as f:
            f.write(dashboard_code)

    def generate_dashboard_code(self):
        """Generate the complete dashboard code"""
        code = '''"""
Generated CGCL Trading Dashboard
Created with UI Builder Tool
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np

# Colors
COLORS = {
    'bg_primary': '#0a0a0a',
    'bg_secondary': '#1a1a1a',
    'bg_tertiary': '#2a2a2a',
    'text_primary': '#ffffff',
    'text_secondary': '#b0b0b0',
    'accent_blue': '#00d4ff',
    'accent_green': '#00ff88',
    'accent_red': '#ff4757',
    'accent_orange': '#ffa726',
    'accent_purple': '#9c27b0',
}

class GeneratedDashboard:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("CGCL Trading Dashboard - Generated Layout")
        self.root.geometry("1400x900")
        self.root.configure(bg=COLORS['bg_primary'])
        self.root.state('zoomed')

        self.setup_ui()

    def setup_ui(self):
        """Setup the generated UI"""
        # Create scrollable main frame
        canvas = tk.Canvas(self.root, bg=COLORS['bg_primary'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=canvas.yview)
        self.scrollable_frame = tk.Frame(canvas, bg=COLORS['bg_primary'])

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # Create components based on layout
        self.create_components()

    def create_components(self):
        """Create all components based on saved layout"""
'''

        # Add component creation code
        for i, comp in enumerate(self.components):
            config = comp.get_config()
            code += f'''
        # Component {i+1}: {config['type']}
        self.create_{config['type'].lower().replace(' ', '_')}({config['x']}, {config['y']}, {config['width']}, {config['height']})
'''

        # Add component creation methods
        code += '''
    def create_order_book(self, x, y, width, height):
        """Create Order Book component"""
        frame = tk.Frame(self.scrollable_frame, bg=COLORS['bg_secondary'],
                        width=width, height=height)
        frame.place(x=x, y=y)
        frame.pack_propagate(False)

        tk.Label(frame, text="📊 Order Book", bg=COLORS['bg_secondary'],
                fg=COLORS['accent_blue'], font=('Arial', 12, 'bold')).pack(pady=5)

        # Sample order book data
        headers_frame = tk.Frame(frame, bg=COLORS['bg_tertiary'])
        headers_frame.pack(fill=tk.X, padx=5, pady=2)

        tk.Label(headers_frame, text="Bid", bg=COLORS['bg_tertiary'],
                fg=COLORS['accent_green'], font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=20)
        tk.Label(headers_frame, text="Ask", bg=COLORS['bg_tertiary'],
                fg=COLORS['accent_red'], font=('Arial', 10, 'bold')).pack(side=tk.RIGHT, padx=20)

        # Sample rows
        for i in range(5):
            row_frame = tk.Frame(frame, bg=COLORS['bg_secondary'])
            row_frame.pack(fill=tk.X, padx=5, pady=1)

            tk.Label(row_frame, text=f"₹{184.70-i*0.05:.2f}", bg=COLORS['bg_secondary'],
                    fg=COLORS['accent_green'], font=('Arial', 9)).pack(side=tk.LEFT, padx=20)
            tk.Label(row_frame, text=f"₹{184.75+i*0.05:.2f}", bg=COLORS['bg_secondary'],
                    fg=COLORS['accent_red'], font=('Arial', 9)).pack(side=tk.RIGHT, padx=20)

    def create_cvd_chart(self, x, y, width, height):
        """Create CVD Chart component"""
        frame = tk.Frame(self.scrollable_frame, bg=COLORS['bg_secondary'],
                        width=width, height=height)
        frame.place(x=x, y=y)
        frame.pack_propagate(False)

        tk.Label(frame, text="📈 CVD Analysis", bg=COLORS['bg_secondary'],
                fg=COLORS['accent_blue'], font=('Arial', 12, 'bold')).pack(pady=5)

        # Create matplotlib chart
        fig = Figure(figsize=(width/100, (height-40)/100), facecolor=COLORS['bg_secondary'])
        ax = fig.add_subplot(111)
        ax.set_facecolor(COLORS['bg_tertiary'])

        # Sample data
        x_data = np.arange(0, 60, 1)
        price_data = 184.75 + np.cumsum(np.random.randn(60) * 0.02)
        cvd_data = np.cumsum(np.random.randn(60) * 100)

        ax_twin = ax.twinx()
        ax.plot(x_data, price_data, color=COLORS['accent_blue'], linewidth=2, label='Price')
        ax_twin.plot(x_data, cvd_data, color=COLORS['accent_orange'], linewidth=2, label='CVD')

        ax.set_ylabel('Price (₹)', color=COLORS['text_secondary'])
        ax_twin.set_ylabel('CVD', color=COLORS['text_secondary'])
        ax.tick_params(colors=COLORS['text_secondary'], labelsize=8)
        ax_twin.tick_params(colors=COLORS['text_secondary'], labelsize=8)
        ax.grid(True, alpha=0.3)

        fig.tight_layout()

        canvas = FigureCanvasTkAgg(fig, frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_volume_profile(self, x, y, width, height):
        """Create Volume Profile component"""
        frame = tk.Frame(self.scrollable_frame, bg=COLORS['bg_secondary'],
                        width=width, height=height)
        frame.place(x=x, y=y)
        frame.pack_propagate(False)

        tk.Label(frame, text="📊 Volume Profile", bg=COLORS['bg_secondary'],
                fg=COLORS['accent_blue'], font=('Arial', 12, 'bold')).pack(pady=5)

        # Create matplotlib chart
        fig = Figure(figsize=(width/100, (height-40)/100), facecolor=COLORS['bg_secondary'])
        ax = fig.add_subplot(111)
        ax.set_facecolor(COLORS['bg_tertiary'])

        # Sample data
        prices = np.linspace(184.00, 185.50, 20)
        volumes = np.random.gamma(2, 500, 20)

        bars = ax.barh(prices, volumes, color=COLORS['accent_orange'], alpha=0.7)
        hvn_index = np.argmax(volumes)
        bars[hvn_index].set_color(COLORS['accent_green'])

        ax.set_xlabel('Volume', color=COLORS['text_secondary'])
        ax.set_ylabel('Price (₹)', color=COLORS['text_secondary'])
        ax.tick_params(colors=COLORS['text_secondary'], labelsize=8)
        ax.grid(True, alpha=0.3)

        fig.tight_layout()

        canvas = FigureCanvasTkAgg(fig, frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_footprint_chart(self, x, y, width, height):
        """Create Footprint Chart component"""
        frame = tk.Frame(self.scrollable_frame, bg=COLORS['bg_secondary'],
                        width=width, height=height)
        frame.place(x=x, y=y)
        frame.pack_propagate(False)

        tk.Label(frame, text="🦶 Footprint Analysis", bg=COLORS['bg_secondary'],
                fg=COLORS['accent_blue'], font=('Arial', 12, 'bold')).pack(pady=5)

        # Create matplotlib chart
        fig = Figure(figsize=(width/100, (height-40)/100), facecolor=COLORS['bg_secondary'])
        ax = fig.add_subplot(111)
        ax.set_facecolor(COLORS['bg_tertiary'])

        # Sample data
        prices = np.linspace(184.50, 185.00, 8)
        bid_vols = np.random.randint(100, 1000, 8)
        ask_vols = np.random.randint(100, 1000, 8)

        ax.barh(prices, -bid_vols, color=COLORS['accent_green'], alpha=0.7, label='Bid')
        ax.barh(prices, ask_vols, color=COLORS['accent_red'], alpha=0.7, label='Ask')

        ax.axvline(x=0, color=COLORS['text_secondary'], alpha=0.5)
        ax.set_xlabel('Volume (Bid ← | → Ask)', color=COLORS['text_secondary'])
        ax.set_ylabel('Price (₹)', color=COLORS['text_secondary'])
        ax.tick_params(colors=COLORS['text_secondary'], labelsize=8)
        ax.legend(fontsize=8)
        ax.grid(True, alpha=0.3)

        fig.tight_layout()

        canvas = FigureCanvasTkAgg(fig, frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_order_flow(self, x, y, width, height):
        """Create Order Flow component"""
        frame = tk.Frame(self.scrollable_frame, bg=COLORS['bg_secondary'],
                        width=width, height=height)
        frame.place(x=x, y=y)
        frame.pack_propagate(False)

        tk.Label(frame, text="🌊 Order Flow", bg=COLORS['bg_secondary'],
                fg=COLORS['accent_blue'], font=('Arial', 12, 'bold')).pack(pady=5)

        # Create matplotlib chart
        fig = Figure(figsize=(width/100, (height-40)/100), facecolor=COLORS['bg_secondary'])
        ax = fig.add_subplot(111)
        ax.set_facecolor(COLORS['bg_tertiary'])

        # Sample data
        times = np.arange(0, 20, 1)
        flow_data = np.array([1, 1.2, 1.5, 1.8, 2.0, 1.9, 1.7, 1.5, 1.2, 1.0,
                             0.8, 0.6, 0.4, 0.2, 0.1, -0.1, -0.3, -0.5, -0.7, -0.9])

        ax.plot(times, flow_data, color=COLORS['accent_purple'], linewidth=3)
        ax.axhline(y=0, color=COLORS['text_secondary'], alpha=0.5)
        ax.axvspan(8, 12, alpha=0.2, color=COLORS['accent_orange'])

        ax.set_xlabel('Time', color=COLORS['text_secondary'])
        ax.set_ylabel('Flow Strength', color=COLORS['text_secondary'])
        ax.tick_params(colors=COLORS['text_secondary'], labelsize=8)
        ax.grid(True, alpha=0.3)

        fig.tight_layout()

        canvas = FigureCanvasTkAgg(fig, frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_trading_signals(self, x, y, width, height):
        """Create Trading Signals component"""
        frame = tk.Frame(self.scrollable_frame, bg=COLORS['bg_secondary'],
                        width=width, height=height)
        frame.place(x=x, y=y)
        frame.pack_propagate(False)

        tk.Label(frame, text="⚡ Trading Signals", bg=COLORS['bg_secondary'],
                fg=COLORS['accent_blue'], font=('Arial', 12, 'bold')).pack(pady=5)

        # Signal display
        signal_frame = tk.Frame(frame, bg=COLORS['accent_green'])
        signal_frame.pack(pady=10)

        tk.Label(signal_frame, text="BUY", bg=COLORS['accent_green'],
                fg='white', font=('Arial', 16, 'bold'), padx=30, pady=10).pack()

        # Details
        details = [
            ("Entry", "₹184.75", COLORS['text_secondary']),
            ("Target", "₹186.50", COLORS['accent_green']),
            ("Stop Loss", "₹183.50", COLORS['accent_red']),
            ("Risk/Reward", "1:3.5", COLORS['accent_blue'])
        ]

        for label, value, color in details:
            detail_frame = tk.Frame(frame, bg=COLORS['bg_secondary'])
            detail_frame.pack(fill=tk.X, padx=10, pady=2)

            tk.Label(detail_frame, text=label, bg=COLORS['bg_secondary'],
                    fg=COLORS['text_secondary'], font=('Arial', 9)).pack(side=tk.LEFT)
            tk.Label(detail_frame, text=value, bg=COLORS['bg_secondary'],
                    fg=color, font=('Arial', 9, 'bold')).pack(side=tk.RIGHT)

    def create_market_analytics(self, x, y, width, height):
        """Create Market Analytics component"""
        frame = tk.Frame(self.scrollable_frame, bg=COLORS['bg_secondary'],
                        width=width, height=height)
        frame.place(x=x, y=y)
        frame.pack_propagate(False)

        tk.Label(frame, text="📈 Market Analytics", bg=COLORS['bg_secondary'],
                fg=COLORS['accent_blue'], font=('Arial', 12, 'bold')).pack(pady=5)

        # Analytics data
        analytics = [
            ("Volume", "1.2M", COLORS['accent_blue']),
            ("Spread", "₹0.05", COLORS['accent_orange']),
            ("Volatility", "Low", COLORS['accent_green']),
            ("Trend", "Bullish", COLORS['accent_green']),
            ("RSI", "65.2", COLORS['accent_orange']),
            ("MACD", "Bullish", COLORS['accent_green'])
        ]

        for label, value, color in analytics:
            metric_frame = tk.Frame(frame, bg=COLORS['bg_secondary'])
            metric_frame.pack(fill=tk.X, padx=10, pady=3)

            tk.Label(metric_frame, text=label, bg=COLORS['bg_secondary'],
                    fg=COLORS['text_secondary'], font=('Arial', 9)).pack(side=tk.LEFT)
            tk.Label(metric_frame, text=value, bg=COLORS['bg_secondary'],
                    fg=color, font=('Arial', 9, 'bold')).pack(side=tk.RIGHT)

    def run(self):
        """Run the generated dashboard"""
        print("🚀 Generated CGCL Dashboard Started")
        self.root.mainloop()

def main():
    dashboard = GeneratedDashboard()
    dashboard.run()

if __name__ == "__main__":
    main()
'''

        return code
    
    def run(self):
        """Run the UI builder tool"""
        print("🎨 CGCL UI Builder Tool Started")
        print("📦 Drag components from tray to canvas")
        print("🖱️ Drag to move, resize with corner handle")
        print("💾 Save/Load layouts as JSON files")
        self.root.mainloop()

def main():
    """Main function"""
    builder = UIBuilderTool()
    builder.run()

if __name__ == "__main__":
    main()
