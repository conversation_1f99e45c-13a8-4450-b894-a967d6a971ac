#!/usr/bin/env python3
"""
Test script for the new order book table widget
"""

import tkinter as tk
from unified_order_book import UnifiedOrderBookWidget
from config import EXECUTIVE_COLORS

def main():
    # Create main window
    root = tk.Tk()
    root.title("Order Book Table Test")
    root.geometry("1000x700")
    root.configure(bg=EXECUTIVE_COLORS['bg_primary'])
    
    # Create order book widget
    order_book = UnifiedOrderBookWidget(root)
    order_book.pack(fill='both', expand=True, padx=20, pady=20)
    
    # Start the application
    root.mainloop()

if __name__ == "__main__":
    main()
