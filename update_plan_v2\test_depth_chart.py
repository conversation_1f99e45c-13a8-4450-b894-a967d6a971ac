"""
Test script to verify the order book UI with top 5 highlighting 
and the new depth chart UI working together
"""

import tkinter as tk
import random
import time
from order_book_ui import OrderBookUI
from depth_chart_ui import DepthChartUI

def test_combined_ui():
    """Test both OrderBookUI and DepthChartUI together"""
    print("🧪 Testing Combined Order Book + Depth Chart UI...")
    
    # Create test window
    root = tk.Tk()
    root.title("Order Book + Depth Chart Test")
    root.geometry("1000x800")
    root.configure(bg='#0a0a0a')
    
    # Create main container with scrolling
    main_frame = tk.Frame(root, bg='#0a0a0a')
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # Title
    title_label = tk.Label(main_frame, text="📊 ORDER BOOK + DEPTH CHART TEST", 
                          font=('Consolas', 16, 'bold'),
                          bg='#0a0a0a', fg='#ffffff')
    title_label.pack(pady=(0, 10))
    
    # Order Book Container
    book_container = tk.Frame(main_frame, bg='#0a0a0a')
    book_container.pack(fill=tk.X, pady=(0, 5))
    
    book_title = tk.Label(book_container, text="ORDER BOOK (Top 5 Highlighted)", 
                         font=('Consolas', 12, 'bold'),
                         bg='#0a0a0a', fg='#00ff88')
    book_title.pack(pady=(0, 5))
    
    # Initialize OrderBookUI
    order_book_ui = OrderBookUI(book_container)
    
    # Depth Chart Container
    chart_container = tk.Frame(main_frame, bg='#0a0a0a')
    chart_container.pack(fill=tk.X, pady=(5, 5))
    
    chart_title = tk.Label(chart_container, text="DEPTH CHART (Real-time Visualization)", 
                          font=('Consolas', 12, 'bold'),
                          bg='#0a0a0a', fg='#ff0844')
    chart_title.pack(pady=(0, 5))
    
    # Initialize DepthChartUI
    depth_chart_ui = DepthChartUI(chart_container)
    
    # Test data generation
    base_price = 186.50
    
    def generate_test_data():
        """Generate realistic test data"""
        # Generate 20 bid levels (decreasing prices)
        bids = []
        for i in range(20):
            price = base_price - (0.05 * (i + 1))
            qty = random.randint(500, 3000)
            orders = random.randint(1, 10)
            
            # Mark top 5 as different source for highlighting test
            source = 'DEPTH' if i < 5 else 'LTP_GENERATED'
            bids.append([qty, orders, price, source])
        
        # Generate 20 ask levels (increasing prices)
        asks = []
        for i in range(20):
            price = base_price + (0.05 * (i + 1))
            qty = random.randint(500, 3000)
            orders = random.randint(1, 10)
            
            # Mark top 5 as different source for highlighting test
            source = 'DEPTH' if i < 5 else 'LTP_GENERATED'
            asks.append([price, orders, qty, source])
        
        return bids, asks
    
    # Update function
    def update_test_data():
        """Update both components with new test data"""
        try:
            bids, asks = generate_test_data()
            
            # Update order book
            order_book_ui.update_display(bids, asks)
            
            # Update depth chart
            depth_chart_ui.update_chart(bids, asks)
            
            print(f"✅ Updated both components - Bids: {len(bids)}, Asks: {len(asks)}")
            print(f"📊 Top 5 bids highlighted, Mid price: ₹{(bids[0][2] + asks[0][0])/2:.2f}")
            
        except Exception as e:
            print(f"❌ Error updating test data: {e}")
        
        # Schedule next update
        root.after(3000, update_test_data)  # Update every 3 seconds
    
    # Control buttons
    control_frame = tk.Frame(main_frame, bg='#0a0a0a')
    control_frame.pack(fill=tk.X, pady=(10, 0))
    
    def clear_displays():
        """Clear both displays"""
        order_book_ui.clear_display()
        depth_chart_ui.clear_chart()
        print("🧹 Cleared both displays")
    
    def manual_update():
        """Manually trigger update"""
        update_test_data()
        print("🔄 Manual update triggered")
    
    clear_btn = tk.Button(control_frame, text="Clear Displays", 
                         command=clear_displays,
                         bg='#ff0844', fg='white', font=('Consolas', 10))
    clear_btn.pack(side=tk.LEFT, padx=(0, 10))
    
    update_btn = tk.Button(control_frame, text="Manual Update", 
                          command=manual_update,
                          bg='#00ff88', fg='black', font=('Consolas', 10))
    update_btn.pack(side=tk.LEFT, padx=(0, 10))
    
    # Status label
    status_label = tk.Label(control_frame, 
                           text="🔄 Auto-updating every 3 seconds | Top 5 levels highlighted in green", 
                           font=('Consolas', 10),
                           bg='#0a0a0a', fg='#888888')
    status_label.pack(side=tk.RIGHT)
    
    # Start test updates
    root.after(1000, update_test_data)  # First update after 1 second
    
    print("✅ Combined UI test started")
    print("📊 Features being tested:")
    print("   - Order book with top 5 level highlighting")
    print("   - Real-time depth chart visualization")
    print("   - Synchronized updates between components")
    print("   - Different colors for DEPTH vs LTP_GENERATED data")
    
    # Run the test
    root.mainloop()

if __name__ == "__main__":
    test_combined_ui()
