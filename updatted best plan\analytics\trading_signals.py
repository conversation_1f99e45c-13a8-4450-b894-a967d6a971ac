"""
Trading Signal Generation for 5-Minute Timeframe
"""

import statistics
from collections import deque
from datetime import datetime
from typing import Dict, List, Optional

from core.data_structures import TradingSignal, OrderBookSnapshot
from config.settings import SIGNAL_CONFIG


class TradingSignalGenerator:
    """Generate actionable trading signals for 5-minute timeframe trading"""
    
    def __init__(self):
        # Signal tracking
        self.active_signals = []
        self.signal_history = deque(maxlen=200)
        self.signal_performance = deque(maxlen=100)
        
        # Signal configuration
        self.signal_config = SIGNAL_CONFIG
        
        # Signal weights
        self.signal_weights = {
            'ORDER_FLOW_IMBALANCE': 0.25,
            'MOMENTUM_BREAKOUT': 0.20,
            'LEVEL_BREAKOUT': 0.20,
            'VOLUME_SURGE': 0.15,
            'CONFLUENCE_SIGNAL': 0.20
        }
        
        # Performance tracking
        self.signal_accuracy = {}
        self.total_signals_generated = 0
        self.successful_signals = 0
        
    def generate_trading_signals(self, current_price: float, snapshot: OrderBookSnapshot,
                                flow_analysis: Dict, levels_analysis: Dict, 
                                price_prediction: Dict, analytics: Dict) -> Dict:
        """Generate comprehensive trading signals for 5-minute timeframe"""
        try:
            timestamp = datetime.now()
            
            signal_analysis = {
                'timestamp': timestamp,
                'current_price': current_price,
                'signals': [],
                'signal_summary': {},
                'risk_assessment': {},
                'execution_plan': {}
            }
            
            # Generate different types of signals
            signals = []
            
            # 1. Order Flow Imbalance Signals
            flow_signals = self._generate_flow_imbalance_signals(flow_analysis, current_price)
            signals.extend(flow_signals)
            
            # 2. Momentum Breakout Signals
            momentum_signals = self._generate_momentum_signals(flow_analysis, analytics, current_price)
            signals.extend(momentum_signals)
            
            # 3. Volume Surge Signals
            volume_signals = self._generate_volume_signals(snapshot, analytics, current_price)
            signals.extend(volume_signals)
            
            # 4. Confluence Signals (multiple factors aligned)
            confluence_signals = self._generate_confluence_signals(
                flow_analysis, levels_analysis, price_prediction, current_price
            )
            signals.extend(confluence_signals)
            
            # Filter and rank signals
            filtered_signals = self._filter_and_rank_signals(signals)
            signal_analysis['signals'] = filtered_signals
            
            # Create signal summary
            signal_analysis['signal_summary'] = self._create_signal_summary(filtered_signals)
            
            # Assess overall risk
            signal_analysis['risk_assessment'] = self._assess_signal_risk(filtered_signals, snapshot)
            
            # Create execution plan
            signal_analysis['execution_plan'] = self._create_execution_plan(filtered_signals, current_price)
            
            # Update tracking
            self._update_signal_tracking(signal_analysis)
            
            return signal_analysis
            
        except Exception as e:
            print(f"Error generating trading signals: {e}")
            return {}
    
    def _generate_flow_imbalance_signals(self, flow_analysis: Dict, current_price: float) -> List[TradingSignal]:
        """Generate signals based on order flow imbalance"""
        try:
            signals = []
            
            if not flow_analysis:
                return signals
            
            basic_imbalance = flow_analysis.get('basic_imbalance', {})
            institutional = flow_analysis.get('institutional_analysis', {})
            flow_momentum = flow_analysis.get('flow_momentum', {})
            
            quantity_imbalance = basic_imbalance.get('quantity_imbalance', 0)
            institutional_imbalance = institutional.get('institutional_imbalance', 0)
            momentum = flow_momentum.get('momentum', 0)
            
            # Strong imbalance signal
            if abs(quantity_imbalance) > self.signal_config['imbalance_threshold']:
                direction = 'BUY' if quantity_imbalance > 0 else 'SELL'
                confidence = min(95, abs(quantity_imbalance) * 3)
                
                signal = TradingSignal(
                    signal_id=f"FLOW_IMBALANCE_{datetime.now().strftime('%H%M%S')}",
                    timestamp=datetime.now(),
                    signal_type='ORDER_FLOW_IMBALANCE',
                    direction=direction,
                    confidence=confidence,
                    strength=abs(quantity_imbalance),
                    timeframe='5-15 minutes',
                    entry_price=current_price,
                    target_price=current_price * (1.005 if direction == 'BUY' else 0.995),
                    stop_loss_price=current_price * (0.997 if direction == 'BUY' else 1.003),
                    description=f"Strong {'buying' if direction == 'BUY' else 'selling'} pressure detected",
                    factors={'imbalance': quantity_imbalance, 'momentum': momentum}
                )
                signals.append(signal)
            
            # Institutional flow signal
            if abs(institutional_imbalance) > 20 and institutional.get('institutional_volume', 0) > 5000:
                direction = 'BUY' if institutional_imbalance > 0 else 'SELL'
                confidence = min(90, abs(institutional_imbalance) * 2)
                
                signal = TradingSignal(
                    signal_id=f"INSTITUTIONAL_{datetime.now().strftime('%H%M%S')}",
                    timestamp=datetime.now(),
                    signal_type='INSTITUTIONAL_FLOW',
                    direction=direction,
                    confidence=confidence,
                    strength=abs(institutional_imbalance),
                    timeframe='10-30 minutes',
                    entry_price=current_price,
                    target_price=current_price * (1.008 if direction == 'BUY' else 0.992),
                    stop_loss_price=current_price * (0.996 if direction == 'BUY' else 1.004),
                    description=f"Institutional {'buying' if direction == 'BUY' else 'selling'} detected",
                    factors={'institutional_imbalance': institutional_imbalance}
                )
                signals.append(signal)
            
            return signals
            
        except Exception as e:
            print(f"Error generating flow imbalance signals: {e}")
            return []
    
    def _generate_momentum_signals(self, flow_analysis: Dict, analytics: Dict, current_price: float) -> List[TradingSignal]:
        """Generate signals based on momentum analysis"""
        try:
            signals = []
            
            if not flow_analysis:
                return signals
            
            flow_momentum = flow_analysis.get('flow_momentum', {})
            momentum = flow_momentum.get('momentum', 0)
            trend = flow_momentum.get('trend', 'NEUTRAL')
            
            # Strong momentum signal
            if abs(momentum) > self.signal_config['momentum_threshold']:
                direction = 'BUY' if momentum > 0 else 'SELL'
                confidence = min(85, abs(momentum) * 25)
                
                signal = TradingSignal(
                    signal_id=f"MOMENTUM_{datetime.now().strftime('%H%M%S')}",
                    timestamp=datetime.now(),
                    signal_type='MOMENTUM_BREAKOUT',
                    direction=direction,
                    confidence=confidence,
                    strength=abs(momentum),
                    timeframe='5-20 minutes',
                    entry_price=current_price,
                    target_price=current_price * (1.006 if direction == 'BUY' else 0.994),
                    stop_loss_price=current_price * (0.996 if direction == 'BUY' else 1.004),
                    description=f"Strong momentum {'building' if direction == 'BUY' else 'declining'}",
                    factors={'momentum': momentum, 'trend': trend}
                )
                signals.append(signal)
            
            return signals
            
        except Exception as e:
            print(f"Error generating momentum signals: {e}")
            return []
    
    def _generate_volume_signals(self, snapshot: OrderBookSnapshot, analytics: Dict, current_price: float) -> List[TradingSignal]:
        """Generate signals based on volume analysis"""
        try:
            signals = []
            
            if not snapshot:
                return signals
            
            total_volume = snapshot.total_bid_quantity + snapshot.total_ask_quantity
            imbalance = snapshot.imbalance
            
            # Volume surge signal
            if total_volume > self.signal_config['volume_threshold'] * 5:  # 5x normal volume
                direction = 'BUY' if imbalance > 0 else 'SELL' if imbalance < 0 else 'NEUTRAL'
                
                if direction != 'NEUTRAL':
                    confidence = min(75, (total_volume / 1000) + abs(imbalance))
                    
                    signal = TradingSignal(
                        signal_id=f"VOLUME_SURGE_{datetime.now().strftime('%H%M%S')}",
                        timestamp=datetime.now(),
                        signal_type='VOLUME_SURGE',
                        direction=direction,
                        confidence=confidence,
                        strength=total_volume,
                        timeframe='5-20 minutes',
                        entry_price=current_price,
                        target_price=current_price * (1.006 if direction == 'BUY' else 0.994),
                        stop_loss_price=current_price * (0.997 if direction == 'BUY' else 1.003),
                        description=f"Volume surge with {'buying' if direction == 'BUY' else 'selling'} bias",
                        factors={'total_volume': total_volume, 'imbalance': imbalance}
                    )
                    signals.append(signal)
            
            return signals
            
        except Exception as e:
            print(f"Error generating volume signals: {e}")
            return []
    
    def _generate_confluence_signals(self, flow_analysis: Dict, levels_analysis: Dict, 
                                   price_prediction: Dict, current_price: float) -> List[TradingSignal]:
        """Generate signals when multiple factors align"""
        try:
            signals = []
            
            # Check for confluence of multiple bullish factors
            bullish_factors = []
            bearish_factors = []
            
            # Flow analysis factors
            if flow_analysis:
                basic_imbalance = flow_analysis.get('basic_imbalance', {})
                quantity_imbalance = basic_imbalance.get('quantity_imbalance', 0)
                
                if quantity_imbalance > 10:
                    bullish_factors.append(('flow_imbalance', quantity_imbalance))
                elif quantity_imbalance < -10:
                    bearish_factors.append(('flow_imbalance', abs(quantity_imbalance)))
            
            # Price prediction factors
            if price_prediction:
                ensemble = price_prediction.get('ensemble_prediction', {})
                predicted_change = ensemble.get('price_change_pct', 0)
                prediction_confidence = ensemble.get('confidence', 0)
                
                if predicted_change > 0.5 and prediction_confidence > 70:
                    bullish_factors.append(('price_prediction', predicted_change))
                elif predicted_change < -0.5 and prediction_confidence > 70:
                    bearish_factors.append(('price_prediction', abs(predicted_change)))
            
            # Generate confluence signals
            if len(bullish_factors) >= 2:
                total_strength = sum(factor[1] for factor in bullish_factors)
                confidence = min(90, total_strength / len(bullish_factors))
                
                signal = TradingSignal(
                    signal_id=f"CONFLUENCE_BULL_{datetime.now().strftime('%H%M%S')}",
                    timestamp=datetime.now(),
                    signal_type='CONFLUENCE_BULLISH',
                    direction='BUY',
                    confidence=confidence,
                    strength=total_strength,
                    timeframe='10-30 minutes',
                    entry_price=current_price,
                    target_price=current_price * 1.008,
                    stop_loss_price=current_price * 0.996,
                    description=f"Multiple bullish factors aligned ({len(bullish_factors)} factors)",
                    factors={'aligned_factors': bullish_factors, 'factor_count': len(bullish_factors)}
                )
                signals.append(signal)
            
            if len(bearish_factors) >= 2:
                total_strength = sum(factor[1] for factor in bearish_factors)
                confidence = min(90, total_strength / len(bearish_factors))
                
                signal = TradingSignal(
                    signal_id=f"CONFLUENCE_BEAR_{datetime.now().strftime('%H%M%S')}",
                    timestamp=datetime.now(),
                    signal_type='CONFLUENCE_BEARISH',
                    direction='SELL',
                    confidence=confidence,
                    strength=total_strength,
                    timeframe='10-30 minutes',
                    entry_price=current_price,
                    target_price=current_price * 0.992,
                    stop_loss_price=current_price * 1.004,
                    description=f"Multiple bearish factors aligned ({len(bearish_factors)} factors)",
                    factors={'aligned_factors': bearish_factors, 'factor_count': len(bearish_factors)}
                )
                signals.append(signal)
            
            return signals
            
        except Exception as e:
            print(f"Error generating confluence signals: {e}")
            return []
    
    def _filter_and_rank_signals(self, signals: List[TradingSignal]) -> List[TradingSignal]:
        """Filter and rank signals by confidence and strength"""
        try:
            # Filter by minimum confidence
            filtered = [s for s in signals if s.confidence >= self.signal_config['min_confidence']]
            
            # Remove duplicate signal types (keep highest confidence)
            signal_types = {}
            for signal in filtered:
                signal_type = signal.signal_type
                if signal_type not in signal_types or signal.confidence > signal_types[signal_type].confidence:
                    signal_types[signal_type] = signal
            
            # Convert back to list and sort by confidence
            ranked_signals = list(signal_types.values())
            ranked_signals.sort(key=lambda x: x.confidence, reverse=True)
            
            return ranked_signals[:5]  # Top 5 signals
            
        except Exception as e:
            print(f"Error filtering and ranking signals: {e}")
            return []
    
    def _create_signal_summary(self, signals: List[TradingSignal]) -> Dict:
        """Create summary of generated signals"""
        try:
            if not signals:
                return {'overall_bias': 'NEUTRAL', 'signal_count': 0, 'max_confidence': 0}
            
            buy_signals = [s for s in signals if s.direction == 'BUY']
            sell_signals = [s for s in signals if s.direction == 'SELL']
            
            buy_confidence = statistics.mean([s.confidence for s in buy_signals]) if buy_signals else 0
            sell_confidence = statistics.mean([s.confidence for s in sell_signals]) if sell_signals else 0
            
            # Determine overall bias
            if len(buy_signals) > len(sell_signals) and buy_confidence > sell_confidence:
                overall_bias = 'BULLISH'
            elif len(sell_signals) > len(buy_signals) and sell_confidence > buy_confidence:
                overall_bias = 'BEARISH'
            else:
                overall_bias = 'NEUTRAL'
            
            return {
                'overall_bias': overall_bias,
                'signal_count': len(signals),
                'buy_signals': len(buy_signals),
                'sell_signals': len(sell_signals),
                'max_confidence': max([s.confidence for s in signals]),
                'avg_confidence': statistics.mean([s.confidence for s in signals]),
                'strongest_signal': max(signals, key=lambda x: x.confidence) if signals else None
            }
            
        except Exception as e:
            print(f"Error creating signal summary: {e}")
            return {}
    
    def _assess_signal_risk(self, signals: List[TradingSignal], snapshot: OrderBookSnapshot) -> Dict:
        """Assess risk for the generated signals"""
        try:
            risk_factors = []
            risk_score = 0
            
            # High spread risk
            if snapshot and snapshot.spread > self.signal_config['spread_threshold']:
                risk_factors.append("High spread indicates volatility")
                risk_score += 15
            
            # Conflicting signals risk
            buy_count = len([s for s in signals if s.direction == 'BUY'])
            sell_count = len([s for s in signals if s.direction == 'SELL'])
            
            if buy_count > 0 and sell_count > 0:
                risk_factors.append("Conflicting buy and sell signals")
                risk_score += 10
            
            # Low confidence risk
            if signals:
                avg_confidence = statistics.mean([s.confidence for s in signals])
                if avg_confidence < 70:
                    risk_factors.append("Low average signal confidence")
                    risk_score += 10
            
            risk_level = 'HIGH' if risk_score > 25 else 'MEDIUM' if risk_score > 15 else 'LOW'
            
            return {
                'risk_score': risk_score,
                'risk_level': risk_level,
                'risk_factors': risk_factors,
                'recommendation': 'Reduce position size' if risk_level == 'HIGH' else 'Normal position size'
            }
            
        except Exception as e:
            print(f"Error assessing signal risk: {e}")
            return {}
    
    def _create_execution_plan(self, signals: List[TradingSignal], current_price: float) -> Dict:
        """Create execution plan for the signals"""
        try:
            if not signals:
                return {'action': 'NO_ACTION', 'reason': 'No signals generated'}
            
            strongest_signal = max(signals, key=lambda x: x.confidence)
            
            execution_plan = {
                'primary_action': strongest_signal.direction,
                'confidence': strongest_signal.confidence,
                'entry_price': strongest_signal.entry_price,
                'target_price': strongest_signal.target_price,
                'stop_loss_price': strongest_signal.stop_loss_price,
                'timeframe': strongest_signal.timeframe,
                'position_size': 'NORMAL' if strongest_signal.confidence > 80 else 'REDUCED',
                'signal_type': strongest_signal.signal_type,
                'description': strongest_signal.description,
                'risk_reward_ratio': strongest_signal.risk_reward_ratio
            }
            
            return execution_plan
            
        except Exception as e:
            print(f"Error creating execution plan: {e}")
            return {}
    
    def _update_signal_tracking(self, signal_analysis: Dict):
        """Update signal tracking and performance"""
        try:
            self.signal_history.append(signal_analysis)
            
            signals = signal_analysis.get('signals', [])
            self.total_signals_generated += len(signals)
            
            # Update active signals
            self.active_signals = signals
            
        except Exception as e:
            print(f"Error updating signal tracking: {e}")
    
    def get_signal_performance(self) -> Dict:
        """Get signal performance statistics"""
        try:
            if not self.signal_history:
                return {}
            
            total_signals = sum(len(analysis.get('signals', [])) for analysis in self.signal_history)
            
            return {
                'total_signals_generated': total_signals,
                'successful_signals': self.successful_signals,
                'success_rate': (self.successful_signals / max(1, total_signals)) * 100,
                'active_signals': len(self.active_signals),
                'signal_history_length': len(self.signal_history)
            }
            
        except Exception as e:
            print(f"Error getting signal performance: {e}")
            return {}
