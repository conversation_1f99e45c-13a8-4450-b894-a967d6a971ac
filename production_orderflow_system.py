"""
Production Real-Time Order Flow System
=====================================

Production-ready system with:
- Smart API WebSocket integration
- Every second tick updates
- Real-time order book GUI
- 5-minute order book history recording
- Live order flow analysis
- Signal generation and alerts
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import json
import queue
from datetime import datetime, timedelta
import pandas as pd
import logging

from smart_api_realtime import SmartAPIRealTimeConnector
from order_flow_engine import AdvancedOrderFlowAnalyzer
from working_realtime_demo import RealTimeOrderBookGUI

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'orderflow_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionOrderFlowSystem:
    """Production real-time order flow system"""
    
    def __init__(self, symbols=None, credentials_file="smart_api_credentials.json"):
        if symbols is None:
            symbols = ["RELIANCE", "TCS", "INFY", "HDFCBANK"]
        
        self.symbols = symbols
        self.credentials_file = credentials_file
        
        # Order flow analyzers
        self.analyzers = {symbol: AdvancedOrderFlowAnalyzer(symbol) for symbol in symbols}
        
        # Smart API connector
        self.api_connector = None
        
        # GUI
        self.gui = RealTimeOrderBookGUI()
        
        # Data tracking
        self.tick_count = 0
        self.signal_count = 0
        self.order_book_count = 0
        self.start_time = None
        
        # Order book history (5 minutes = 300 seconds)
        self.order_book_history = {symbol: [] for symbol in symbols}
        self.max_history_seconds = 300
        
        # Signal history
        self.signal_history = []
        
        # System state
        self.is_running = False
        self.is_market_hours = True
        
        # Performance metrics
        self.performance_metrics = {
            'session_start': None,
            'total_ticks': 0,
            'total_signals': 0,
            'total_order_books': 0,
            'ticks_per_second': 0,
            'signals_per_hour': 0,
            'connection_uptime': 0,
            'last_signal_time': None
        }
    
    def initialize_system(self):
        """Initialize all system components"""
        logger.info("🚀 Initializing Production Order Flow System...")
        
        try:
            # Check credentials file
            try:
                with open(self.credentials_file, 'r') as f:
                    credentials = json.load(f)
                logger.info("✅ Credentials loaded successfully")
            except FileNotFoundError:
                logger.error("❌ Credentials file not found. Run test_smart_api_auth.py first.")
                return False
            
            # Initialize Smart API connector
            self.api_connector = SmartAPIRealTimeConnector(self.credentials_file)
            self.api_connector.set_callbacks(
                on_tick=self.handle_tick,
                on_order_book=self.handle_order_book
            )
            
            logger.info("✅ System components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            return False
    
    def start_system(self):
        """Start the production system"""
        if not self.initialize_system():
            messagebox.showerror("Initialization Error", "Failed to initialize system components")
            return False
        
        logger.info("🚀 Starting Production Order Flow System...")
        self.is_running = True
        self.start_time = time.time()
        self.performance_metrics['session_start'] = datetime.now()
        
        try:
            # Start GUI in separate thread
            gui_thread = threading.Thread(target=self.run_gui, daemon=True)
            gui_thread.start()
            
            # Start performance monitoring
            perf_thread = threading.Thread(target=self.monitor_performance, daemon=True)
            perf_thread.start()
            
            # Start order book history cleanup
            cleanup_thread = threading.Thread(target=self.cleanup_history, daemon=True)
            cleanup_thread.start()
            
            # Connect to Smart API
            logger.info("📡 Connecting to Smart API...")
            if self.api_connector.connect(self.symbols):
                logger.info("✅ Smart API connected successfully")
                
                # Start heartbeat
                self.api_connector.start_heartbeat()
                
                # Update GUI status
                self.gui.update_status("🟢 Connected to Smart API - Monitoring live data")
                
                # Start main monitoring loop
                self.run_monitoring_loop()
                
            else:
                logger.error("❌ Smart API connection failed")
                self.gui.update_status("🔴 Smart API connection failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ System startup failed: {e}")
            self.gui.update_status(f"🔴 System startup failed: {e}")
            return False
    
    def run_gui(self):
        """Run the GUI"""
        try:
            self.gui.run()
        except Exception as e:
            logger.error(f"GUI error: {e}")
        finally:
            self.stop_system()
    
    def handle_tick(self, symbol, tick):
        """Handle incoming tick data from Smart API"""
        try:
            self.tick_count += 1
            self.performance_metrics['total_ticks'] = self.tick_count
            
            # Process through order flow engine
            if symbol in self.analyzers:
                signal = self.analyzers[symbol].flow_engine.add_tick(tick)
                
                if signal:
                    self.signal_count += 1
                    self.performance_metrics['total_signals'] = self.signal_count
                    self.performance_metrics['last_signal_time'] = datetime.now()
                    
                    self.handle_signal(symbol, signal, tick)
            
            # Log every 100th tick to avoid spam
            if self.tick_count % 100 == 0:
                logger.info(f"📊 Processed {self.tick_count} ticks, Generated {self.signal_count} signals")
                
        except Exception as e:
            logger.error(f"Tick handling error: {e}")
    
    def handle_order_book(self, symbol, order_book):
        """Handle incoming order book data from Smart API"""
        try:
            self.order_book_count += 1
            self.performance_metrics['total_order_books'] = self.order_book_count
            
            # Store in history
            self.store_order_book_history(symbol, order_book)
            
            # Update GUI
            self.gui.update_order_book(symbol, order_book)
            
            # Process through order flow engine
            if symbol in self.analyzers:
                self.analyzers[symbol].flow_engine.add_order_book(order_book)
            
            # Log every 50th order book
            if self.order_book_count % 50 == 0:
                logger.info(f"📚 Processed {self.order_book_count} order books")
                
        except Exception as e:
            logger.error(f"Order book handling error: {e}")
    
    def store_order_book_history(self, symbol, order_book):
        """Store order book in 5-minute history"""
        try:
            # Add to history
            self.order_book_history[symbol].append({
                'timestamp': order_book.timestamp,
                'bids': [(level.price, level.quantity, level.orders) for level in order_book.bids[:10]],
                'asks': [(level.price, level.quantity, level.orders) for level in order_book.asks[:10]]
            })
            
        except Exception as e:
            logger.error(f"Order book history storage error: {e}")
    
    def cleanup_history(self):
        """Clean up old order book history (keep only 5 minutes)"""
        while self.is_running:
            try:
                cutoff_time = datetime.now() - timedelta(seconds=self.max_history_seconds)
                
                for symbol in self.symbols:
                    # Remove old entries
                    self.order_book_history[symbol] = [
                        entry for entry in self.order_book_history[symbol]
                        if entry['timestamp'] >= cutoff_time
                    ]
                
                time.sleep(30)  # Cleanup every 30 seconds
                
            except Exception as e:
                logger.error(f"History cleanup error: {e}")
                time.sleep(30)
    
    def handle_signal(self, symbol, signal, tick):
        """Handle generated trading signal"""
        try:
            logger.info(f"🚨 SIGNAL #{self.signal_count}: {symbol} - {signal.signal_type} "
                       f"(Strength: {signal.strength:.2f}, Confidence: {signal.confidence:.2f})")
            
            # Update GUI
            self.gui.update_signal(symbol, signal, tick)
            
            # Store signal in history
            signal_data = {
                'timestamp': tick.timestamp,
                'symbol': symbol,
                'signal_type': signal.signal_type,
                'strength': signal.strength,
                'confidence': signal.confidence,
                'price': tick.price,
                'volume': tick.volume,
                'reasons': signal.reasons
            }
            
            self.signal_history.append(signal_data)
            
            # Get current metrics for detailed logging
            metrics = self.analyzers[symbol].flow_engine.get_current_metrics()
            
            # Print detailed signal to console
            print(f"\n{'='*60}")
            print(f"📊 LIVE ORDER FLOW SIGNAL - {symbol}")
            print(f"{'='*60}")
            print(f"Time: {tick.timestamp.strftime('%H:%M:%S.%f')[:-3]}")
            print(f"Signal: {signal.signal_type}")
            print(f"Strength: {signal.strength:.2f}")
            print(f"Confidence: {signal.confidence:.2f}")
            print(f"Price: ₹{tick.price:.2f}")
            print(f"Volume: {tick.volume:,}")
            print(f"Reasons: {', '.join(signal.reasons)}")
            print(f"\n📈 Flow Metrics:")
            print(f"  VWAP: ₹{metrics.get('current_vwap', 0):.2f}")
            print(f"  VWAP Deviation: {metrics.get('vwap_deviation', 0)*100:.2f}%")
            print(f"  Cumulative Delta: {metrics.get('cumulative_delta', 0):,}")
            print(f"{'='*60}\n")
            
            # Save to daily signals file
            self.save_signal_to_file(signal_data, metrics)
            
        except Exception as e:
            logger.error(f"Signal handling error: {e}")
    
    def save_signal_to_file(self, signal_data, metrics):
        """Save signal to daily file"""
        try:
            # Enhanced signal data with metrics
            enhanced_signal = {
                **signal_data,
                'timestamp': signal_data['timestamp'].isoformat(),
                'vwap': metrics.get('current_vwap', 0),
                'vwap_deviation': metrics.get('vwap_deviation', 0),
                'cumulative_delta': metrics.get('cumulative_delta', 0),
                'recent_volume_5min': metrics.get('recent_volume_5min', 0)
            }
            
            # Append to daily signals file
            filename = f"live_signals_{datetime.now().strftime('%Y%m%d')}.json"
            with open(filename, 'a') as f:
                f.write(json.dumps(enhanced_signal) + '\n')
                
        except Exception as e:
            logger.error(f"Signal file saving error: {e}")
    
    def monitor_performance(self):
        """Monitor system performance"""
        while self.is_running:
            try:
                time.sleep(60)  # Update every minute
                
                # Calculate performance metrics
                if self.start_time:
                    uptime = time.time() - self.start_time
                    self.performance_metrics['connection_uptime'] = uptime
                    self.performance_metrics['ticks_per_second'] = self.tick_count / uptime if uptime > 0 else 0
                    self.performance_metrics['signals_per_hour'] = (self.signal_count / uptime) * 3600 if uptime > 0 else 0
                
                # Update GUI with metrics
                self.gui.update_metrics(self.performance_metrics)
                
                # Log performance
                logger.info(f"📊 Performance: {self.performance_metrics['ticks_per_second']:.1f} ticks/sec, "
                           f"{self.performance_metrics['signals_per_hour']:.1f} signals/hour")
                
                # Check API connection
                if self.api_connector:
                    api_status = self.api_connector.get_status()
                    if not api_status['connected']:
                        logger.warning("⚠️ API connection lost")
                        self.gui.update_status("🟡 API connection lost - attempting reconnection")
                
            except Exception as e:
                logger.error(f"Performance monitoring error: {e}")
    
    def run_monitoring_loop(self):
        """Main monitoring loop"""
        logger.info("📡 Starting main monitoring loop...")
        
        try:
            while self.is_running:
                # Check connection status
                if self.api_connector and not self.api_connector.is_connected:
                    logger.warning("⚠️ API connection lost, attempting reconnection...")
                    self.gui.update_status("🟡 Reconnecting to Smart API...")
                    
                    if self.api_connector.connect(self.symbols):
                        logger.info("✅ Reconnected successfully")
                        self.gui.update_status("🟢 Reconnected to Smart API")
                    else:
                        logger.error("❌ Reconnection failed")
                        self.gui.update_status("🔴 Reconnection failed")
                
                # Check if market hours
                current_time = datetime.now().time()
                market_start = datetime.strptime("09:15", "%H:%M").time()
                market_end = datetime.strptime("15:30", "%H:%M").time()
                
                self.is_market_hours = market_start <= current_time <= market_end
                
                if not self.is_market_hours:
                    self.gui.update_status("🟡 Outside market hours (9:15 AM - 3:30 PM)")
                
                time.sleep(10)  # Check every 10 seconds
                
        except KeyboardInterrupt:
            logger.info("⏹️ System stopped by user")
        except Exception as e:
            logger.error(f"Monitoring loop error: {e}")
        finally:
            self.stop_system()
    
    def export_session_data(self):
        """Export session data"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # Export order book history
            for symbol in self.symbols:
                if self.order_book_history[symbol]:
                    df_data = []
                    for entry in self.order_book_history[symbol]:
                        for i, (price, qty, orders) in enumerate(entry['bids'][:5]):
                            df_data.append({
                                'timestamp': entry['timestamp'],
                                'symbol': symbol,
                                'side': 'BID',
                                'level': i + 1,
                                'price': price,
                                'quantity': qty,
                                'orders': orders
                            })
                        for i, (price, qty, orders) in enumerate(entry['asks'][:5]):
                            df_data.append({
                                'timestamp': entry['timestamp'],
                                'symbol': symbol,
                                'side': 'ASK',
                                'level': i + 1,
                                'price': price,
                                'quantity': qty,
                                'orders': orders
                            })
                    
                    if df_data:
                        df = pd.DataFrame(df_data)
                        filename = f"orderbook_history_{symbol}_{timestamp}.csv"
                        df.to_csv(filename, index=False)
                        logger.info(f"📁 Exported order book history: {filename}")
            
            # Export session report
            session_report = {
                'session_summary': self.performance_metrics,
                'total_signals': len(self.signal_history),
                'symbols_monitored': self.symbols,
                'session_duration_minutes': (time.time() - self.start_time) / 60 if self.start_time else 0
            }
            
            report_filename = f"session_report_{timestamp}.json"
            with open(report_filename, 'w') as f:
                json.dump(session_report, f, indent=2, default=str)
            
            logger.info(f"📊 Session report exported: {report_filename}")
            
        except Exception as e:
            logger.error(f"Data export error: {e}")
    
    def stop_system(self):
        """Stop the system gracefully"""
        logger.info("🛑 Stopping production system...")
        self.is_running = False
        
        # Disconnect API
        if self.api_connector:
            self.api_connector.disconnect()
        
        # Export session data
        self.export_session_data()
        
        # Final performance summary
        if self.start_time:
            session_duration = time.time() - self.start_time
            logger.info(f"📊 Session Summary:")
            logger.info(f"  Duration: {session_duration/60:.1f} minutes")
            logger.info(f"  Total Ticks: {self.tick_count:,}")
            logger.info(f"  Total Signals: {self.signal_count}")
            logger.info(f"  Total Order Books: {self.order_book_count:,}")
            logger.info(f"  Avg Ticks/Second: {self.tick_count/session_duration:.1f}")
            logger.info(f"  Avg Signals/Hour: {(self.signal_count/session_duration)*3600:.1f}")
        
        logger.info("✅ System stopped successfully")


def main():
    """Main function"""
    print("🚀 PRODUCTION REAL-TIME ORDER FLOW SYSTEM")
    print("=" * 60)
    print("Features:")
    print("✅ Smart API WebSocket integration")
    print("✅ Every second tick updates")
    print("✅ Real-time order book GUI")
    print("✅ 5-minute order book history recording")
    print("✅ Live order flow analysis")
    print("✅ Signal generation and alerts")
    print("✅ Performance monitoring")
    print("✅ Automatic data export")
    print("=" * 60)
    
    # Check market hours
    current_time = datetime.now().time()
    market_start = datetime.strptime("09:15", "%H:%M").time()
    market_end = datetime.strptime("15:30", "%H:%M").time()
    
    if not (market_start <= current_time <= market_end):
        print(f"\n⚠️  Note: Currently outside market hours (9:15 AM - 3:30 PM)")
        print(f"Current time: {current_time.strftime('%H:%M')}")
        print("System will still work but may receive limited data")
    
    # Symbols to monitor
    symbols = ["RELIANCE", "TCS", "INFY", "HDFCBANK"]
    
    try:
        # Create and start system
        system = ProductionOrderFlowSystem(symbols)
        
        print(f"\n📊 Monitoring symbols: {', '.join(symbols)}")
        print("🔄 Starting production system...")
        print("📱 GUI window will open for real-time monitoring")
        print("⏹️  Close GUI window to stop system")
        
        # Start the system
        system.start_system()
        
    except KeyboardInterrupt:
        print("\n⏹️ System stopped by user")
    except Exception as e:
        print(f"\n❌ System failed: {e}")
        logger.error(f"System failure: {e}")


if __name__ == "__main__":
    main()
