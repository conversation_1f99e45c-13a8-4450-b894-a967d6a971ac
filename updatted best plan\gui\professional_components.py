"""
Professional UI Components for CGCL Trading Dashboard
Executive-level widgets and visualizations
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

# Professional styling constants
EXECUTIVE_STYLE = {
    'card_padding': 15,
    'border_radius': 8,
    'shadow_offset': 2,
    'animation_duration': 300,
    'chart_dpi': 100,
    'font_family': 'Segoe UI',
    'title_size': 14,
    'subtitle_size': 11,
    'body_size': 10,
    'caption_size': 9
}

class ProfessionalCard(tk.Frame):
    """Professional card widget with shadow and styling"""
    
    def __init__(self, parent, title="", subtitle="", bg_color="#2a2a2a", **kwargs):
        super().__init__(parent, bg=bg_color, relief=tk.RAISED, bd=2, **kwargs)
        
        self.title = title
        self.subtitle = subtitle
        self.bg_color = bg_color
        
        self.create_card_structure()
    
    def create_card_structure(self):
        """Create card structure with header and content areas"""
        # Header section
        if self.title:
            self.header_frame = tk.Frame(self, bg=self.bg_color, height=40)
            self.header_frame.pack(fill=tk.X, padx=EXECUTIVE_STYLE['card_padding'], 
                                  pady=(EXECUTIVE_STYLE['card_padding'], 5))
            self.header_frame.pack_propagate(False)
            
            # Title
            self.title_label = tk.Label(
                self.header_frame,
                text=self.title,
                bg=self.bg_color,
                fg='#ffffff',
                font=(EXECUTIVE_STYLE['font_family'], EXECUTIVE_STYLE['title_size'], 'bold')
            )
            self.title_label.pack(side=tk.LEFT, anchor=tk.W)
            
            # Subtitle
            if self.subtitle:
                self.subtitle_label = tk.Label(
                    self.header_frame,
                    text=self.subtitle,
                    bg=self.bg_color,
                    fg='#b0b0b0',
                    font=(EXECUTIVE_STYLE['font_family'], EXECUTIVE_STYLE['caption_size'])
                )
                self.subtitle_label.pack(side=tk.RIGHT, anchor=tk.E)
        
        # Content area
        self.content_frame = tk.Frame(self, bg=self.bg_color)
        self.content_frame.pack(fill=tk.BOTH, expand=True, 
                               padx=EXECUTIVE_STYLE['card_padding'],
                               pady=(0, EXECUTIVE_STYLE['card_padding']))

class MetricDisplay(ProfessionalCard):
    """Professional metric display widget"""
    
    def __init__(self, parent, metric_name, value="--", unit="", 
                 trend=None, color="#00d4ff", **kwargs):
        super().__init__(parent, title=metric_name, **kwargs)
        
        self.metric_name = metric_name
        self.current_value = value
        self.unit = unit
        self.trend = trend
        self.color = color
        
        self.create_metric_display()
    
    def create_metric_display(self):
        """Create metric display elements"""
        # Main value
        self.value_label = tk.Label(
            self.content_frame,
            text=f"{self.current_value}{self.unit}",
            bg=self.bg_color,
            fg=self.color,
            font=(EXECUTIVE_STYLE['font_family'], 24, 'bold')
        )
        self.value_label.pack(pady=(10, 5))
        
        # Trend indicator
        if self.trend:
            trend_color = '#00ff88' if self.trend > 0 else '#ff4757'
            trend_symbol = '↗' if self.trend > 0 else '↘'
            trend_text = f"{trend_symbol} {abs(self.trend):.2f}%"
            
            self.trend_label = tk.Label(
                self.content_frame,
                text=trend_text,
                bg=self.bg_color,
                fg=trend_color,
                font=(EXECUTIVE_STYLE['font_family'], EXECUTIVE_STYLE['subtitle_size'], 'bold')
            )
            self.trend_label.pack()
    
    def update_value(self, new_value, new_trend=None):
        """Update metric value and trend"""
        self.current_value = new_value
        self.value_label.config(text=f"{new_value}{self.unit}")
        
        if new_trend is not None:
            self.trend = new_trend
            trend_color = '#00ff88' if new_trend > 0 else '#ff4757'
            trend_symbol = '↗' if new_trend > 0 else '↘'
            trend_text = f"{trend_symbol} {abs(new_trend):.2f}%"
            
            if hasattr(self, 'trend_label'):
                self.trend_label.config(text=trend_text, fg=trend_color)

class ProfessionalChart(ProfessionalCard):
    """Professional chart widget with matplotlib integration"""
    
    def __init__(self, parent, chart_type="line", **kwargs):
        super().__init__(parent, **kwargs)
        
        self.chart_type = chart_type
        self.figure = None
        self.canvas = None
        self.axes = None
        
        self.create_chart()
    
    def create_chart(self):
        """Create matplotlib chart"""
        # Create figure with professional styling
        self.figure = Figure(figsize=(8, 4), dpi=EXECUTIVE_STYLE['chart_dpi'],
                           facecolor=self.bg_color)
        self.figure.patch.set_facecolor(self.bg_color)
        
        # Create axis
        self.axes = self.figure.add_subplot(111)
        self.axes.set_facecolor('#1a1a1a')
        
        # Style the chart
        self.style_chart()
        
        # Create canvas
        self.canvas = FigureCanvasTkAgg(self.figure, self.content_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def style_chart(self):
        """Apply professional styling to chart"""
        # Grid
        self.axes.grid(True, alpha=0.3, color='#404040', linewidth=0.5)
        
        # Spines
        for spine in self.axes.spines.values():
            spine.set_color('#404040')
            spine.set_linewidth(1)
        
        # Ticks
        self.axes.tick_params(colors='#b0b0b0', labelsize=9)
        
        # Labels
        self.axes.set_xlabel('', color='#b0b0b0', fontsize=10)
        self.axes.set_ylabel('', color='#b0b0b0', fontsize=10)
    
    def update_data(self, x_data, y_data, **plot_kwargs):
        """Update chart with new data"""
        self.axes.clear()
        self.style_chart()
        
        if self.chart_type == "line":
            self.axes.plot(x_data, y_data, **plot_kwargs)
        elif self.chart_type == "bar":
            self.axes.bar(x_data, y_data, **plot_kwargs)
        elif self.chart_type == "scatter":
            self.axes.scatter(x_data, y_data, **plot_kwargs)
        
        self.canvas.draw()

class OrderBookWidget(ProfessionalCard):
    """Professional order book display widget"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, title="📊 Order Book Depth", **kwargs)
        
        self.bid_data = []
        self.ask_data = []
        self.max_levels = 10
        
        self.create_order_book()
    
    def create_order_book(self):
        """Create order book display"""
        # Headers
        header_frame = tk.Frame(self.content_frame, bg=self.bg_color, height=30)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        header_frame.pack_propagate(False)
        
        headers = ['TOTAL', 'SIZE', 'PRICE', 'PRICE', 'SIZE', 'TOTAL']
        colors = ['#00ff88', '#00ff88', '#00ff88', '#ff4757', '#ff4757', '#ff4757']
        
        for header, color in zip(headers, colors):
            label = tk.Label(header_frame, text=header,
                           bg=self.bg_color, fg=color,
                           font=(EXECUTIVE_STYLE['font_family'], 
                                EXECUTIVE_STYLE['body_size'], 'bold'))
            label.pack(side=tk.LEFT, expand=True)
        
        # Scrollable content
        self.create_scrollable_content()
    
    def create_scrollable_content(self):
        """Create scrollable order book content"""
        # Canvas for scrolling
        canvas = tk.Canvas(self.content_frame, bg=self.bg_color, 
                          highlightthickness=0, height=300)
        scrollbar = ttk.Scrollbar(self.content_frame, orient="vertical", 
                                 command=canvas.yview)
        
        self.scrollable_frame = tk.Frame(canvas, bg=self.bg_color)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Create initial rows
        self.order_rows = []
        self.create_order_rows()
    
    def create_order_rows(self):
        """Create professional order book rows like web trading platforms"""
        for i in range(self.max_levels):
            row_frame = tk.Frame(self.scrollable_frame, bg=self.bg_color, height=28)
            row_frame.pack(fill=tk.X, pady=1)
            row_frame.pack_propagate(False)

            # Create row widgets
            row_widgets = {}

            # Bid side (left) - TOTAL | SIZE | PRICE
            row_widgets['bid_total'] = tk.Label(row_frame, text="",
                                              bg=self.bg_color, fg='#00ff88',
                                              font=('Consolas', 9), width=12, anchor='e')
            row_widgets['bid_total'].pack(side=tk.LEFT, expand=True, padx=2)

            row_widgets['bid_size'] = tk.Label(row_frame, text="",
                                             bg=self.bg_color, fg='#00ff88',
                                             font=('Consolas', 9), width=10, anchor='e')
            row_widgets['bid_size'].pack(side=tk.LEFT, expand=True, padx=2)

            row_widgets['bid_price'] = tk.Label(row_frame, text="",
                                              bg=self.bg_color, fg='#00ff88',
                                              font=('Consolas', 9, 'bold'), width=12, anchor='e')
            row_widgets['bid_price'].pack(side=tk.LEFT, expand=True, padx=2)

            # Ask side (right) - PRICE | SIZE | TOTAL
            row_widgets['ask_price'] = tk.Label(row_frame, text="",
                                              bg=self.bg_color, fg='#ff4757',
                                              font=('Consolas', 9, 'bold'), width=12, anchor='w')
            row_widgets['ask_price'].pack(side=tk.LEFT, expand=True, padx=2)

            row_widgets['ask_size'] = tk.Label(row_frame, text="",
                                             bg=self.bg_color, fg='#ff4757',
                                             font=('Consolas', 9), width=10, anchor='w')
            row_widgets['ask_size'].pack(side=tk.LEFT, expand=True, padx=2)

            row_widgets['ask_total'] = tk.Label(row_frame, text="",
                                              bg=self.bg_color, fg='#ff4757',
                                              font=('Consolas', 9), width=12, anchor='w')
            row_widgets['ask_total'].pack(side=tk.LEFT, expand=True, padx=2)

            self.order_rows.append(row_widgets)
    
    def update_order_book(self, bids: List[Tuple], asks: List[Tuple]):
        """Update order book with new data - professional format"""
        print(f"🔍 DEBUG: OrderBookWidget.update_order_book called")
        print(f"🔍 DEBUG: Received {len(bids)} bids and {len(asks)} asks")
        print(f"🔍 DEBUG: Sample bid: {bids[0] if bids else 'None'}")
        print(f"🔍 DEBUG: Sample ask: {asks[0] if asks else 'None'}")
        print(f"🔍 DEBUG: order_rows count: {len(self.order_rows)}")

        self.bid_data = bids
        self.ask_data = asks

        # Calculate cumulative totals
        bid_cumulative = 0
        ask_cumulative = 0

        # Update rows
        for i, row_widgets in enumerate(self.order_rows):
            # Update bid side (TOTAL | SIZE | PRICE)
            if i < len(bids):
                qty, orders, price = bids[i]
                bid_cumulative += qty

                # Format numbers like professional platforms
                size_str = f"{qty:,}" if qty < 1000000 else f"{qty/1000000:.1f}M"
                total_str = f"{bid_cumulative:,}" if bid_cumulative < 1000000 else f"{bid_cumulative/1000000:.1f}M"

                print(f"🔍 DEBUG: Updating bid row {i}: total={total_str}, size={size_str}, price=₹{price:.2f}")

                row_widgets['bid_total'].config(text=total_str)
                row_widgets['bid_size'].config(text=size_str)
                row_widgets['bid_price'].config(text=f"₹{price:.2f}")

                # Use fixed background color for now to ensure visibility
                row_widgets['bid_total'].config(bg=self.bg_color, fg='#00ff88')
                row_widgets['bid_size'].config(bg=self.bg_color, fg='#00ff88')
                row_widgets['bid_price'].config(bg=self.bg_color, fg='#00ff88')

                print(f"🔍 DEBUG: Bid row {i} updated successfully")
            else:
                row_widgets['bid_total'].config(text="", bg=self.bg_color)
                row_widgets['bid_size'].config(text="", bg=self.bg_color)
                row_widgets['bid_price'].config(text="", bg=self.bg_color)

            # Update ask side (PRICE | SIZE | TOTAL)
            if i < len(asks):
                price, orders, qty = asks[i]
                ask_cumulative += qty

                # Format numbers like professional platforms
                size_str = f"{qty:,}" if qty < 1000000 else f"{qty/1000000:.1f}M"
                total_str = f"{ask_cumulative:,}" if ask_cumulative < 1000000 else f"{ask_cumulative/1000000:.1f}M"

                print(f"🔍 DEBUG: Updating ask row {i}: price=₹{price:.2f}, size={size_str}, total={total_str}")

                row_widgets['ask_price'].config(text=f"₹{price:.2f}")
                row_widgets['ask_size'].config(text=size_str)
                row_widgets['ask_total'].config(text=total_str)

                # Use fixed background color for now to ensure visibility
                row_widgets['ask_price'].config(bg=self.bg_color, fg='#ff4757')
                row_widgets['ask_size'].config(bg=self.bg_color, fg='#ff4757')
                row_widgets['ask_total'].config(bg=self.bg_color, fg='#ff4757')

                print(f"🔍 DEBUG: Ask row {i} updated successfully")
            else:
                row_widgets['ask_price'].config(text="", bg=self.bg_color)
                row_widgets['ask_size'].config(text="", bg=self.bg_color)
                row_widgets['ask_total'].config(text="", bg=self.bg_color)

        # Force canvas update to ensure changes are visible
        print(f"🔍 DEBUG: Forcing canvas update")
        self.scrollable_frame.update_idletasks()
        print(f"🔍 DEBUG: Canvas update completed")

class TradingSignalWidget(ProfessionalCard):
    """Professional trading signal display"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, title="⚡ Trading Signals", **kwargs)
        
        self.current_signal = None
        self.signal_strength = 0
        
        self.create_signal_display()
    
    def create_signal_display(self):
        """Create signal display elements"""
        # Current signal
        self.signal_frame = tk.Frame(self.content_frame, bg='#00ff88',
                                   relief=tk.RAISED, bd=2)
        self.signal_frame.pack(fill=tk.X, pady=(10, 15))
        
        self.signal_label = tk.Label(self.signal_frame, text="BUY",
                                   bg='#00ff88', fg='white',
                                   font=(EXECUTIVE_STYLE['font_family'], 18, 'bold'))
        self.signal_label.pack(pady=10)
        
        # Signal details
        details_frame = tk.Frame(self.content_frame, bg=self.bg_color)
        details_frame.pack(fill=tk.X)
        
        self.entry_label = tk.Label(details_frame, text="Entry: ₹184.75",
                                   bg=self.bg_color, fg='#b0b0b0',
                                   font=(EXECUTIVE_STYLE['font_family'], 
                                        EXECUTIVE_STYLE['body_size']))
        self.entry_label.pack()
        
        self.target_label = tk.Label(details_frame, text="Target: ₹186.50",
                                    bg=self.bg_color, fg='#00ff88',
                                    font=(EXECUTIVE_STYLE['font_family'], 
                                         EXECUTIVE_STYLE['body_size']))
        self.target_label.pack()
        
        self.stop_label = tk.Label(details_frame, text="Stop: ₹183.50",
                                  bg=self.bg_color, fg='#ff4757',
                                  font=(EXECUTIVE_STYLE['font_family'], 
                                       EXECUTIVE_STYLE['body_size']))
        self.stop_label.pack()
        
        # Strength indicator
        strength_frame = tk.Frame(self.content_frame, bg=self.bg_color)
        strength_frame.pack(fill=tk.X, pady=(15, 0))
        
        strength_title = tk.Label(strength_frame, text="Signal Strength:",
                                 bg=self.bg_color, fg='#b0b0b0',
                                 font=(EXECUTIVE_STYLE['font_family'], 
                                      EXECUTIVE_STYLE['caption_size']))
        strength_title.pack()
        
        self.strength_label = tk.Label(strength_frame, text="85%",
                                      bg=self.bg_color, fg='#00d4ff',
                                      font=(EXECUTIVE_STYLE['font_family'], 
                                           EXECUTIVE_STYLE['subtitle_size'], 'bold'))
        self.strength_label.pack()
    
    def update_signal(self, signal_type, entry_price, target_price, 
                     stop_price, strength):
        """Update trading signal"""
        self.current_signal = signal_type
        self.signal_strength = strength
        
        # Update signal display
        signal_color = '#00ff88' if signal_type == 'BUY' else '#ff4757'
        self.signal_frame.config(bg=signal_color)
        self.signal_label.config(text=signal_type, bg=signal_color)
        
        # Update prices
        self.entry_label.config(text=f"Entry: ₹{entry_price:.2f}")
        self.target_label.config(text=f"Target: ₹{target_price:.2f}")
        self.stop_label.config(text=f"Stop: ₹{stop_price:.2f}")
        
        # Update strength
        self.strength_label.config(text=f"{strength}%")
