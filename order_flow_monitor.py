"""
Real-Time Order Flow Monitor
============================

This module provides real-time monitoring and visualization of order flow
analysis results with live dashboard and alerting capabilities.
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd
from dataclasses import asdict
import logging

from order_flow_engine import OrderFlowEngine, AdvancedOrderFlowAnalyzer, Tick, OrderBook, OrderBookLevel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OrderFlowMonitor:
    """
    Real-time order flow monitoring system
    
    Features:
    - Live order flow analysis
    - Signal generation and alerting
    - Performance tracking
    - Data export and logging
    """
    
    def __init__(self, symbols: List[str]):
        self.symbols = symbols
        self.analyzers = {symbol: AdvancedOrderFlowAnalyzer(symbol) for symbol in symbols}
        
        # Monitoring state
        self.is_running = False
        self.start_time = None
        self.total_signals_generated = 0
        self.signals_by_symbol = {symbol: 0 for symbol in symbols}
        
        # Alert thresholds
        self.signal_strength_threshold = 0.7
        self.signal_confidence_threshold = 0.6
        
        # Data storage
        self.signal_history = []
        self.performance_metrics = {}
        
    async def start_monitoring(self):
        """Start real-time monitoring"""
        self.is_running = True
        self.start_time = datetime.now()
        logger.info(f"Starting order flow monitoring for symbols: {self.symbols}")
        
        # Start monitoring tasks
        tasks = []
        for symbol in self.symbols:
            task = asyncio.create_task(self._monitor_symbol(symbol))
            tasks.append(task)
        
        # Start dashboard update task
        dashboard_task = asyncio.create_task(self._update_dashboard())
        tasks.append(dashboard_task)
        
        try:
            await asyncio.gather(*tasks)
        except KeyboardInterrupt:
            logger.info("Monitoring stopped by user")
        finally:
            self.is_running = False
    
    async def _monitor_symbol(self, symbol: str):
        """Monitor order flow for a specific symbol"""
        analyzer = self.analyzers[symbol]
        
        while self.is_running:
            try:
                # Simulate receiving real-time data (replace with actual data feed)
                await self._process_market_data(symbol, analyzer)
                
                # Small delay to prevent overwhelming
                await asyncio.sleep(0.1)  # 100ms
                
            except Exception as e:
                logger.error(f"Error monitoring {symbol}: {e}")
                await asyncio.sleep(1)
    
    async def _process_market_data(self, symbol: str, analyzer: AdvancedOrderFlowAnalyzer):
        """Process incoming market data for a symbol"""
        # This would be replaced with actual Smart API data feed
        # For now, we'll simulate data processing
        
        # Get comprehensive analysis
        analysis = analyzer.get_comprehensive_analysis()
        
        # Check for signals
        if analysis['current_signal']:
            signal = analysis['current_signal']
            
            # Check if signal meets alert criteria
            if (signal['strength'] >= self.signal_strength_threshold and 
                signal['confidence'] >= self.signal_confidence_threshold):
                
                await self._handle_signal(symbol, signal, analysis)
    
    async def _handle_signal(self, symbol: str, signal: Dict, analysis: Dict):
        """Handle generated signal"""
        self.total_signals_generated += 1
        self.signals_by_symbol[symbol] += 1
        
        # Create signal record
        signal_record = {
            'timestamp': datetime.now(),
            'symbol': symbol,
            'signal': signal,
            'analysis': analysis,
            'signal_id': f"{symbol}_{int(time.time())}"
        }
        
        self.signal_history.append(signal_record)
        
        # Log signal
        logger.info(f"🚨 SIGNAL: {symbol} - {signal['signal_type']} "
                   f"(Strength: {signal['strength']:.2f}, "
                   f"Confidence: {signal['confidence']:.2f})")
        
        # Print detailed signal info
        self._print_signal_details(signal_record)
        
        # Could add webhook/email alerts here
        await self._send_alert(signal_record)
    
    def _print_signal_details(self, signal_record: Dict):
        """Print detailed signal information"""
        signal = signal_record['signal']
        analysis = signal_record['analysis']
        
        print("\n" + "="*60)
        print(f"📊 ORDER FLOW SIGNAL - {signal_record['symbol']}")
        print("="*60)
        print(f"Time: {signal_record['timestamp'].strftime('%H:%M:%S')}")
        print(f"Signal: {signal['signal_type']} ({signal['strength']:.2f} strength)")
        print(f"Confidence: {signal['confidence']:.2f}")
        print(f"Reasons: {', '.join(signal['reasons'])}")
        
        # Print basic metrics
        if analysis['basic_metrics']:
            metrics = analysis['basic_metrics']
            print(f"\n📈 Current Metrics:")
            print(f"  Price: ₹{metrics.get('current_price', 0):.2f}")
            print(f"  VWAP: ₹{metrics.get('current_vwap', 0):.2f}")
            print(f"  VWAP Deviation: {metrics.get('vwap_deviation', 0)*100:.2f}%")
            print(f"  Recent Volume (5min): {metrics.get('recent_volume_5min', 0):,}")
            print(f"  Cumulative Delta: {metrics.get('cumulative_delta', 0):,}")
        
        # Print detected patterns
        if analysis['detected_patterns']:
            print(f"\n🔍 Detected Patterns:")
            for pattern in analysis['detected_patterns']:
                print(f"  • {pattern['pattern']}: {pattern['direction']} "
                      f"(strength: {pattern['strength']:.2f})")
        
        print("="*60 + "\n")
    
    async def _send_alert(self, signal_record: Dict):
        """Send alert for signal (placeholder for webhook/email)"""
        # This could send to Telegram, Discord, email, etc.
        pass
    
    async def _update_dashboard(self):
        """Update monitoring dashboard"""
        while self.is_running:
            try:
                self._print_dashboard()
                await asyncio.sleep(10)  # Update every 10 seconds
            except Exception as e:
                logger.error(f"Dashboard update error: {e}")
                await asyncio.sleep(5)
    
    def _print_dashboard(self):
        """Print monitoring dashboard"""
        if not self.start_time:
            return
            
        runtime = datetime.now() - self.start_time
        
        print("\n" + "="*80)
        print("📊 ORDER FLOW MONITORING DASHBOARD")
        print("="*80)
        print(f"Runtime: {str(runtime).split('.')[0]}")
        print(f"Total Signals: {self.total_signals_generated}")
        print(f"Monitoring: {', '.join(self.symbols)}")
        
        # Signals by symbol
        print(f"\n📈 Signals by Symbol:")
        for symbol, count in self.signals_by_symbol.items():
            print(f"  {symbol}: {count} signals")
        
        # Recent signals
        if self.signal_history:
            print(f"\n🔔 Recent Signals (Last 5):")
            recent_signals = self.signal_history[-5:]
            for signal_record in recent_signals:
                signal = signal_record['signal']
                time_str = signal_record['timestamp'].strftime('%H:%M:%S')
                print(f"  {time_str} - {signal_record['symbol']}: "
                      f"{signal['signal_type']} ({signal['strength']:.2f})")
        
        # Performance summary
        if len(self.signal_history) > 0:
            signals_per_hour = len(self.signal_history) / (runtime.total_seconds() / 3600)
            print(f"\n📊 Performance:")
            print(f"  Signals/Hour: {signals_per_hour:.1f}")
            
            # Signal type distribution
            signal_types = [s['signal']['signal_type'] for s in self.signal_history]
            buy_signals = signal_types.count('BUY')
            sell_signals = signal_types.count('SELL')
            neutral_signals = signal_types.count('NEUTRAL')
            
            print(f"  BUY: {buy_signals}, SELL: {sell_signals}, NEUTRAL: {neutral_signals}")
        
        print("="*80)
    
    def add_tick_data(self, symbol: str, price: float, volume: int, 
                     buyer_initiated: bool, timestamp: Optional[datetime] = None):
        """Add tick data for analysis"""
        if symbol not in self.analyzers:
            return
            
        if timestamp is None:
            timestamp = datetime.now()
            
        tick = Tick(
            timestamp=timestamp,
            price=price,
            volume=volume,
            buyer_initiated=buyer_initiated
        )
        
        analyzer = self.analyzers[symbol]
        signal = analyzer.flow_engine.add_tick(tick)
        
        return signal
    
    def add_order_book_data(self, symbol: str, bids: List[Tuple], asks: List[Tuple],
                           timestamp: Optional[datetime] = None):
        """Add order book data for analysis"""
        if symbol not in self.analyzers:
            return
            
        if timestamp is None:
            timestamp = datetime.now()
        
        # Convert tuples to OrderBookLevel objects
        bid_levels = [OrderBookLevel(price=price, quantity=qty, orders=1) 
                     for price, qty in bids]
        ask_levels = [OrderBookLevel(price=price, quantity=qty, orders=1) 
                     for price, qty in asks]
        
        order_book = OrderBook(
            timestamp=timestamp,
            bids=bid_levels,
            asks=ask_levels
        )
        
        analyzer = self.analyzers[symbol]
        analyzer.flow_engine.add_order_book(order_book)
    
    def get_signal_history(self, symbol: Optional[str] = None, 
                          hours: int = 24) -> List[Dict]:
        """Get signal history"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        filtered_signals = [
            signal for signal in self.signal_history
            if signal['timestamp'] >= cutoff_time
        ]
        
        if symbol:
            filtered_signals = [
                signal for signal in filtered_signals
                if signal['symbol'] == symbol
            ]
        
        return filtered_signals
    
    def export_data(self, filename: str):
        """Export signal history to file"""
        try:
            # Convert to DataFrame for easy export
            export_data = []
            for signal_record in self.signal_history:
                row = {
                    'timestamp': signal_record['timestamp'],
                    'symbol': signal_record['symbol'],
                    'signal_type': signal_record['signal']['signal_type'],
                    'strength': signal_record['signal']['strength'],
                    'confidence': signal_record['signal']['confidence'],
                    'reasons': ', '.join(signal_record['signal']['reasons'])
                }
                export_data.append(row)
            
            df = pd.DataFrame(export_data)
            df.to_csv(filename, index=False)
            logger.info(f"Data exported to {filename}")
            
        except Exception as e:
            logger.error(f"Export failed: {e}")
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.is_running = False
        logger.info("Monitoring stopped")


# Example usage and testing
if __name__ == "__main__":
    async def test_monitor():
        """Test the order flow monitor"""
        symbols = ["RELIANCE", "TCS", "INFY"]
        monitor = OrderFlowMonitor(symbols)
        
        # Start monitoring in background
        monitor_task = asyncio.create_task(monitor.start_monitoring())
        
        # Simulate some data
        await asyncio.sleep(2)
        
        # Add some test tick data
        for i in range(10):
            monitor.add_tick_data("RELIANCE", 2500 + i, 1000, True)
            monitor.add_tick_data("TCS", 3500 + i, 500, False)
            await asyncio.sleep(1)
        
        # Let it run for a bit
        await asyncio.sleep(10)
        
        # Stop monitoring
        monitor.stop_monitoring()
        await monitor_task
    
    # Run test
    # asyncio.run(test_monitor())
