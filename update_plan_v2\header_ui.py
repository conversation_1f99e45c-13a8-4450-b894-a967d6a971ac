"""
Professional Trading Platform Header UI Module
Handles all header functionality including title, controls, and status display
Migrated to CustomTkinter for modern professional appearance
"""

import customtkinter as ctk
import tkinter as tk  # Keep for some constants if needed
from config import EXECUTIVE_COLORS, APP_CONFIG
from market_conditions import MARKET_CONDITION_NAMES


class HeaderUI:
    """Professional header UI component for trading platform"""

    def __init__(self, parent, callbacks=None):
        """
        Initialize header UI
        
        Args:
            parent: Parent frame where header will be created
            callbacks: Dict with callback functions:
                - 'market_condition_changed': function(condition_key)
                - 'data_source_changed': function(source)
                - 'refresh_clicked': function()
        """
        self.parent = parent
        self.callbacks = callbacks or {}

        # Set CustomTkinter appearance mode and theme
        ctk.set_appearance_mode("dark")  # Professional dark theme
        ctk.set_default_color_theme("blue")  # Professional blue accent

        # State variables
        self.current_market_condition = 'sideways'
        self.data_source = 'simulated'
        
        # UI component references
        self.header_frame = None
        self.connection_label = None
        self.market_condition_section = None
        self.market_condition_frame = None
        self.market_condition_buttons = {}
        
        # Data source button references
        self.live_btn = None
        self.sim_btn = None
        self.disc_btn = None
        self.refresh_btn = None
        
        # Create the header UI
        self.create_header()
        
    def create_header(self):
        """Create the complete header UI"""
        # Enhanced professional header with CustomTkinter modern styling
        self.header_frame = ctk.CTkFrame(self.parent, height=210, corner_radius=10)
        self.header_frame.pack(fill=tk.X, pady=(0, 10), padx=10)

        # Left side - Enhanced title section
        left_frame = ctk.CTkFrame(self.header_frame, fg_color="transparent")
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20, pady=20)
        
        # Main title with modern CustomTkinter styling
        title_label = ctk.CTkLabel(left_frame, text="🚀 Ultimate Trading Analysis v2",
                                  font=ctk.CTkFont(family="Segoe UI", size=18, weight="bold"))
        title_label.pack(side=tk.TOP, anchor='w')

        # Subtitle - will be updated with current stock
        self.subtitle_label = ctk.CTkLabel(left_frame, text="CGCL Live Market • Professional Trading Platform",
                                          font=ctk.CTkFont(family="Segoe UI", size=12),
                                          text_color=("gray70", "gray30"))
        self.subtitle_label.pack(side=tk.TOP, anchor='w', pady=(2, 0))
        
        # Right side - Enhanced controls section
        right_frame = ctk.CTkFrame(self.header_frame, fg_color="transparent")
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=20, pady=20)

        # Connection status with modern styling
        status_frame = ctk.CTkFrame(right_frame, fg_color="transparent")
        status_frame.pack(side=tk.TOP, anchor='e', pady=(0, 12))

        ctk.CTkLabel(status_frame, text="Data Source:",
                    font=ctk.CTkFont(family="Segoe UI", size=11, weight="bold"),
                    text_color=("gray60", "gray40")).pack(side=tk.LEFT, padx=(0, 8))

        self.connection_label = ctk.CTkLabel(status_frame, text="● SIMULATED DATA (📊 Sideways Market)",
                                           font=ctk.CTkFont(family="Segoe UI", size=12, weight="bold"),
                                           text_color="#0099ff")
        self.connection_label.pack(side=tk.LEFT)
        
        # Enhanced data source controls
        self.create_control_buttons(right_frame)
        
    def create_control_buttons(self, parent):
        """Create enhanced professional control buttons"""
        # Main container with modern styling
        controls_container = ctk.CTkFrame(parent, fg_color="transparent")
        controls_container.pack(side=tk.LEFT, pady=20, padx=20, fill=tk.Y)

        # Market Condition Section (only visible in simulated mode)
        self.market_condition_section = ctk.CTkFrame(controls_container, fg_color="transparent")
        self.market_condition_section.pack(side=tk.TOP, pady=(0, 12), fill=tk.X)

        # Market condition label
        market_label = ctk.CTkLabel(self.market_condition_section, text="Market Simulation",
                                   font=ctk.CTkFont(family="Segoe UI", size=12, weight="bold"))
        market_label.pack(side=tk.TOP, pady=(0, 8))

        # Market condition buttons frame
        self.market_condition_frame = ctk.CTkFrame(self.market_condition_section, fg_color="transparent")
        self.market_condition_frame.pack(side=tk.TOP, fill=tk.X)
        
        # Enhanced market condition buttons
        self.market_condition_buttons = {}
        market_conditions = [
            ('sideways', '📊 Sideways', '#4A90E2', '#3A7BC8'),
            ('trending_up', '📈 Trending Up', '#7ED321', '#5BA517'),
            ('trending_down', '📉 Trending Down', '#D0021B', '#A50115'),
            ('volatile', '⚡ Volatile', '#F5A623', '#D1891A'),
            ('breakout', '🚀 Breakout', '#9013FE', '#7A0FD4')
        ]
        
        for condition_key, button_text, normal_color, hover_color in market_conditions:
            btn = ctk.CTkButton(self.market_condition_frame, text=button_text,
                               fg_color=normal_color, hover_color=hover_color,
                               font=ctk.CTkFont(family="Segoe UI", size=11, weight="bold"),
                               width=140, height=35, corner_radius=8,
                               command=lambda k=condition_key: self.set_market_condition(k))
            btn.pack(side=tk.LEFT, padx=3, pady=2)
            self.market_condition_buttons[condition_key] = btn
        
        # Data Source Section
        data_source_section = ctk.CTkFrame(controls_container, fg_color="transparent")
        data_source_section.pack(side=tk.TOP, fill=tk.X)

        # Data source label
        source_label = ctk.CTkLabel(data_source_section, text="Data Source",
                                   font=ctk.CTkFont(family="Segoe UI", size=12, weight="bold"))
        source_label.pack(side=tk.TOP, pady=(0, 8))

        # Data source buttons frame
        button_frame = ctk.CTkFrame(data_source_section, fg_color="transparent")
        button_frame.pack(side=tk.TOP, fill=tk.X)
        
        # Enhanced data source buttons
        button_configs = [
            ('live_btn', '📡 Live', '#7ED321', '#5BA517', 'live'),
            ('sim_btn', '🎯 Simulated', '#4A90E2', '#3A7BC8', 'simulated'),
            ('disc_btn', '❌ Disconnected', '#D0021B', '#A50115', 'disconnected'),
            ('refresh_btn', '🔄 Refresh', '#F5A623', '#D1891A', 'refresh')
        ]
        
        for btn_name, btn_text, normal_color, hover_color, action in button_configs:
            if action == 'refresh':
                command = lambda: self._handle_refresh()
            else:
                command = lambda src=action: self.set_data_source(src)
                
            btn = ctk.CTkButton(button_frame, text=btn_text,
                               fg_color=normal_color, hover_color=hover_color,
                               font=ctk.CTkFont(family="Segoe UI", size=11, weight="bold"),
                               width=120, height=35, corner_radius=8,
                               command=command)
            btn.pack(side=tk.LEFT, padx=3, pady=2)
            setattr(self, btn_name, btn)
        
        # Stock search is now handled separately in main_app.py

        # Update initial button states
        self.update_market_condition_buttons()
        self.update_data_source_buttons()
        self.update_market_condition_visibility()

    # Note: CustomTkinter handles hover effects automatically through hover_color parameter
    # No need for manual hover effect implementation

    def set_market_condition(self, condition_key):
        """Handle market condition button click"""
        self.current_market_condition = condition_key
        condition_name = MARKET_CONDITION_NAMES[condition_key]
        print(f"🎯 [SIMULATOR] Market condition changed to: {condition_name}")

        # Update button highlighting
        self.update_market_condition_buttons()

        # Call callback if provided
        if 'market_condition_changed' in self.callbacks:
            self.callbacks['market_condition_changed'](condition_key)

    def set_data_source(self, source):
        """Handle data source button click"""
        self.data_source = source
        print(f"🎛️ [UI] Data source changed to: {source}")

        # Update button highlighting
        self.update_data_source_buttons()

        # Update market condition visibility
        self.update_market_condition_visibility()

        # Call callback if provided
        if 'data_source_changed' in self.callbacks:
            self.callbacks['data_source_changed'](source)

    def _handle_refresh(self):
        """Handle refresh button click"""
        print("🔄 [UI] Refresh button clicked")

        # Call callback if provided
        if 'refresh_clicked' in self.callbacks:
            self.callbacks['refresh_clicked']()

    def update_market_condition_buttons(self):
        """Update market condition button highlighting with CustomTkinter styling"""
        if not hasattr(self, 'market_condition_buttons'):
            return

        # Color mapping for normal states
        normal_colors = {
            'sideways': '#4A90E2',
            'trending_up': '#7ED321',
            'trending_down': '#D0021B',
            'volatile': '#F5A623',
            'breakout': '#9013FE'
        }

        # Reset all buttons to normal state
        for condition_key, btn in self.market_condition_buttons.items():
            btn.configure(fg_color=normal_colors[condition_key],
                         border_width=0)

        # Highlight current selection with modern border effect
        if self.current_market_condition in self.market_condition_buttons:
            current_btn = self.market_condition_buttons[self.current_market_condition]
            # Add bright border to indicate selection
            current_btn.configure(border_width=3, border_color="#00FFFF")

    def update_data_source_buttons(self):
        """Update data source button highlighting with CustomTkinter styling"""
        # Color mapping for normal states
        normal_colors = {
            'live_btn': '#7ED321',
            'sim_btn': '#4A90E2',
            'disc_btn': '#D0021B',
            'refresh_btn': '#F5A623'
        }

        # Reset all buttons to normal state
        for btn_name, color in normal_colors.items():
            if hasattr(self, btn_name):
                btn = getattr(self, btn_name)
                btn.configure(fg_color=color, border_width=0)

        # Highlight current data source with modern border effect
        if self.data_source == 'live' and hasattr(self, 'live_btn'):
            self.live_btn.configure(border_width=3, border_color="#00FFFF")
        elif self.data_source == 'simulated' and hasattr(self, 'sim_btn'):
            self.sim_btn.configure(border_width=3, border_color="#00FFFF")
        elif self.data_source == 'disconnected' and hasattr(self, 'disc_btn'):
            self.disc_btn.configure(border_width=3, border_color="#00FFFF")

    def update_market_condition_visibility(self, show=None):
        """Show/hide market condition section based on data source"""
        # If show parameter is provided, use it; otherwise use data source logic
        if show is not None:
            should_show = show
        else:
            should_show = (self.data_source == 'simulated')
        if should_show:
            self.market_condition_section.pack(side=tk.TOP, pady=(0, 12), fill=tk.X)
            print(f"🎛️ [UI] Market condition section visible (data source: {self.data_source})")
        else:
            self.market_condition_section.pack_forget()
            print(f"🎛️ [UI] Market condition section hidden (data source: {self.data_source})")

    def update_connection_status(self, status_text, color='#0099ff'):
        """Update the connection status display"""
        if self.connection_label:
            self.connection_label.configure(text=status_text, text_color=color)











    def update_current_stock(self, symbol):
        """Update current stock display in header subtitle"""
        try:
            if hasattr(self, 'subtitle_label') and self.subtitle_label:
                # Update subtitle to show current stock
                clean_symbol = symbol.replace('-EQ', '')
                self.subtitle_label.configure(text=f"{clean_symbol} Live Market • Professional Trading Platform")
                print(f"✅ [HEADER] Updated subtitle with stock: {clean_symbol}")
        except Exception as e:
            print(f"❌ [HEADER] Error updating current stock: {e}")

    def update_stock_loading_status(self, status_text, color="#FFA500"):
        """Update stock loading status - now handled by separate stock search widget"""
        pass

    def set_stocks_loaded(self, count):
        """Update status when stocks are loaded - now handled by separate stock search widget"""
        pass

    def enable_stock_search(self):
        """Enable stock search - now handled by separate stock search widget"""
        pass

    def disable_stock_search(self):
        """Disable stock search - now handled by separate stock search widget"""
        pass

    def get_current_market_condition(self):
        """Get current market condition"""
        return self.current_market_condition

    def get_current_data_source(self):
        """Get current data source"""
        return self.data_source
