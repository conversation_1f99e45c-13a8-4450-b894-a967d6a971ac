#!/usr/bin/env python3
"""
Trading Charts Widget - Comprehensive Chart Display
"""

import tkinter as tk
from tkinter import ttk
import numpy as np
import pandas as pd
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
from datetime import datetime, timedelta
from typing import List, Tuple
from config import EXECUTIVE_COLORS, APP_CONFIG


class TradingChartsWidget(tk.Frame):
    """Comprehensive trading charts widget"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, bg=EXECUTIVE_COLORS['bg_card'], **kwargs)
        
        # Chart data storage
        self.price_data = []
        self.volume_data = []
        self.timestamps = []
        self.max_data_points = 100

        # Order book data for depth chart
        self.current_bids = []
        self.current_asks = []
        
        # Initialize with sample data
        self.initialize_sample_data()
        
        self.create_charts()
    
    def initialize_sample_data(self):
        """Initialize with sample OHLC data"""
        base_price = 872.0
        base_time = datetime.now() - timedelta(minutes=100)
        
        for i in range(50):
            timestamp = base_time + timedelta(minutes=i*2)
            
            # Generate realistic OHLC data
            open_price = base_price + np.random.normal(0, 0.5)
            close_price = open_price + np.random.normal(0, 0.8)
            high_price = max(open_price, close_price) + abs(np.random.normal(0, 0.3))
            low_price = min(open_price, close_price) - abs(np.random.normal(0, 0.3))
            volume = np.random.randint(1000, 10000)
            
            self.timestamps.append(timestamp)
            self.price_data.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price
            })
            self.volume_data.append(volume)
            
            base_price = close_price
    
    def create_charts(self):
        """Create comprehensive chart display"""
        
        # Title
        title_label = tk.Label(
            self,
            text="📈 TRADING CHARTS",
            font=('Segoe UI', 14, 'bold'),
            bg=EXECUTIVE_COLORS['bg_card'],
            fg=EXECUTIVE_COLORS['text_primary']
        )
        title_label.pack(pady=(10, 5))
        
        # Chart container
        chart_container = tk.Frame(self, bg=EXECUTIVE_COLORS['bg_card'])
        chart_container.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Create matplotlib figure with subplots
        self.fig = Figure(figsize=(12, 8), facecolor=EXECUTIVE_COLORS['bg_card'])
        
        # Create subplots: Price chart, Volume chart, RSI chart
        self.ax_price = self.fig.add_subplot(3, 1, 1)    # Price chart (top, larger)
        self.ax_volume = self.fig.add_subplot(3, 1, 2)   # Volume chart (middle)
        self.ax_rsi = self.fig.add_subplot(3, 1, 3)      # RSI chart (bottom)
        
        # Adjust subplot spacing
        self.fig.subplots_adjust(hspace=0.3, left=0.1, right=0.95, top=0.95, bottom=0.1)
        
        # Style all subplots
        for ax in [self.ax_price, self.ax_volume, self.ax_rsi]:
            ax.set_facecolor(EXECUTIVE_COLORS['bg_secondary'])
            ax.tick_params(colors=EXECUTIVE_COLORS['text_secondary'], labelsize=8)
            ax.grid(True, alpha=0.3, color=EXECUTIVE_COLORS['text_secondary'])
            for spine in ax.spines.values():
                spine.set_color(EXECUTIVE_COLORS['text_secondary'])
        
        # Create canvas
        self.canvas = FigureCanvasTkAgg(self.fig, chart_container)
        self.canvas.get_tk_widget().pack(fill='both', expand=True)
        
        # Initial chart update
        self.update_charts()
    
    def update_charts(self):
        """Update all charts with current data"""
        if not self.price_data:
            return
            
        try:
            # Clear all axes
            self.ax_price.clear()
            self.ax_volume.clear()
            self.ax_rsi.clear()

            # Update price chart (candlestick)
            self.update_price_chart()

            # Update volume chart
            self.update_volume_chart()

            # Update RSI chart
            self.update_rsi_chart()
            
            # Refresh canvas
            self.canvas.draw()
            
        except Exception as e:
            print(f"❌ Error updating charts: {e}")
    
    def update_price_chart(self):
        """Update candlestick price chart"""
        if len(self.price_data) < 2:
            return
            
        # Prepare data
        x_pos = range(len(self.price_data))
        opens = [d['open'] for d in self.price_data]
        highs = [d['high'] for d in self.price_data]
        lows = [d['low'] for d in self.price_data]
        closes = [d['close'] for d in self.price_data]
        
        # Create candlestick chart
        for i, (o, h, l, c) in enumerate(zip(opens, highs, lows, closes)):
            color = EXECUTIVE_COLORS['success'] if c >= o else EXECUTIVE_COLORS['danger']
            
            # High-low line
            self.ax_price.plot([i, i], [l, h], color=color, linewidth=1)
            
            # Body rectangle
            body_height = abs(c - o)
            body_bottom = min(o, c)
            rect = Rectangle((i-0.3, body_bottom), 0.6, body_height, 
                           facecolor=color, alpha=0.8, edgecolor=color)
            self.ax_price.add_patch(rect)
        
        # Add moving averages
        if len(closes) >= 20:
            ma20 = pd.Series(closes).rolling(20).mean()
            self.ax_price.plot(x_pos, ma20, color=EXECUTIVE_COLORS['warning'], 
                             linewidth=1, alpha=0.8, label='MA20')
        
        if len(closes) >= 50:
            ma50 = pd.Series(closes).rolling(50).mean()
            self.ax_price.plot(x_pos, ma50, color=EXECUTIVE_COLORS['info'], 
                             linewidth=1, alpha=0.8, label='MA50')
        
        # Style price chart
        self.ax_price.set_title('CGCL Price Chart', color=EXECUTIVE_COLORS['text_primary'], fontsize=10)
        self.ax_price.set_ylabel('Price (₹)', color=EXECUTIVE_COLORS['text_secondary'], fontsize=8)
        self.ax_price.legend(loc='upper left', fontsize=8)
        
        # Set background and grid
        self.ax_price.set_facecolor(EXECUTIVE_COLORS['bg_secondary'])
        self.ax_price.grid(True, alpha=0.3, color=EXECUTIVE_COLORS['text_secondary'])
    
    def update_volume_chart(self):
        """Update volume bar chart"""
        if not self.volume_data:
            return
            
        x_pos = range(len(self.volume_data))
        colors = []
        
        # Color bars based on price movement
        for i, vol in enumerate(self.volume_data):
            if i > 0:
                prev_close = self.price_data[i-1]['close']
                curr_close = self.price_data[i]['close']
                color = EXECUTIVE_COLORS['success'] if curr_close >= prev_close else EXECUTIVE_COLORS['danger']
            else:
                color = EXECUTIVE_COLORS['text_secondary']
            colors.append(color)
        
        # Create volume bars
        self.ax_volume.bar(x_pos, self.volume_data, color=colors, alpha=0.7, width=0.8)
        
        # Style volume chart
        self.ax_volume.set_title('Volume', color=EXECUTIVE_COLORS['text_primary'], fontsize=10)
        self.ax_volume.set_ylabel('Volume', color=EXECUTIVE_COLORS['text_secondary'], fontsize=8)
        self.ax_volume.set_facecolor(EXECUTIVE_COLORS['bg_secondary'])
        self.ax_volume.grid(True, alpha=0.3, color=EXECUTIVE_COLORS['text_secondary'])
    
    def update_rsi_chart(self):
        """Update RSI indicator chart"""
        if len(self.price_data) < 14:
            return
            
        # Calculate RSI
        closes = [d['close'] for d in self.price_data]
        rsi_values = self.calculate_rsi(closes, period=14)
        
        if not rsi_values:
            return
            
        x_pos = range(len(rsi_values))
        
        # Plot RSI line
        self.ax_rsi.plot(x_pos, rsi_values, color=EXECUTIVE_COLORS['info'], linewidth=2)
        
        # Add overbought/oversold lines
        self.ax_rsi.axhline(y=70, color=EXECUTIVE_COLORS['danger'], linestyle='--', alpha=0.7, linewidth=1)
        self.ax_rsi.axhline(y=30, color=EXECUTIVE_COLORS['success'], linestyle='--', alpha=0.7, linewidth=1)
        self.ax_rsi.axhline(y=50, color=EXECUTIVE_COLORS['text_secondary'], linestyle='-', alpha=0.5, linewidth=1)
        
        # Fill overbought/oversold areas
        self.ax_rsi.fill_between(x_pos, 70, 100, alpha=0.1, color=EXECUTIVE_COLORS['danger'])
        self.ax_rsi.fill_between(x_pos, 0, 30, alpha=0.1, color=EXECUTIVE_COLORS['success'])
        
        # Style RSI chart
        self.ax_rsi.set_title('RSI (14)', color=EXECUTIVE_COLORS['text_primary'], fontsize=10)
        self.ax_rsi.set_ylabel('RSI', color=EXECUTIVE_COLORS['text_secondary'], fontsize=8)
        self.ax_rsi.set_ylim(0, 100)
        self.ax_rsi.set_facecolor(EXECUTIVE_COLORS['bg_secondary'])
        self.ax_rsi.grid(True, alpha=0.3, color=EXECUTIVE_COLORS['text_secondary'])
    
    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        if len(prices) < period + 1:
            return []
            
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gains = pd.Series(gains).rolling(period).mean()
        avg_losses = pd.Series(losses).rolling(period).mean()
        
        rs = avg_gains / avg_losses
        rsi = 100 - (100 / (1 + rs))
        
        return rsi.dropna().tolist()

    def add_new_data(self, price_data: dict, volume: int):
        """Add new price and volume data"""
        timestamp = datetime.now()

        # Add new data
        self.timestamps.append(timestamp)
        self.price_data.append(price_data)
        self.volume_data.append(volume)

        # Keep only recent data
        if len(self.price_data) > self.max_data_points:
            self.timestamps.pop(0)
            self.price_data.pop(0)
            self.volume_data.pop(0)

        # Update charts
        self.update_charts()

    def update_order_book_data(self, bids: List[Tuple], asks: List[Tuple]):
        """Update order book data for depth chart"""
        self.current_bids = bids
        self.current_asks = asks


if __name__ == "__main__":
    # Test the charts widget
    root = tk.Tk()
    root.title("Trading Charts Test")
    root.geometry("1200x800")
    root.configure(bg=EXECUTIVE_COLORS['bg_primary'])
    
    charts = TradingChartsWidget(root)
    charts.pack(fill='both', expand=True, padx=20, pady=20)
    
    root.mainloop()
