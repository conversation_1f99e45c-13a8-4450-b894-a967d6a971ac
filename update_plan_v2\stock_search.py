#!/usr/bin/env python3
"""
Stock Search Module - Handles stock search and dropdown functionality
Separated from main application for better code organization
"""

import tkinter as tk
import customtkinter as ctk
import json
import os
import threading
from typing import List, Tuple, Callable, Optional


class StockSearchWidget:
    """
    Dedicated widget for stock search with dropdown functionality
    Handles positioning, search logic, and user interactions
    """
    
    def __init__(self, parent_frame, callbacks: dict = None):
        """
        Initialize stock search widget
        
        Args:
            parent_frame: Parent CTkFrame to contain the search widget
            callbacks: Dictionary of callback functions
        """
        self.parent_frame = parent_frame
        self.callbacks = callbacks or {}
        
        # Widget references
        self.search_frame = None
        self.stock_entry = None
        self.search_btn = None
        self.current_stock_label = None
        self.status_label = None
        self.suggestions_frame = None
        
        # Data
        self.stock_database = []
        self.current_stock = "CGCL-EQ"
        self.is_enabled = False
        
        # Load stock database
        self.load_stock_database()
        
        # Create UI
        self.create_ui()
    
    def load_stock_database(self):
        """Load stock database from local file or create if not exists"""
        try:
            database_path = os.path.join(os.path.dirname(__file__), 'stock_database.json')
            if os.path.exists(database_path):
                with open(database_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.stock_database = data.get('stocks', [])
                    print(f"✅ Loaded {len(self.stock_database)} stocks from local database")
            else:
                print("⚠️ Stock database not found, creating new one...")
                self.create_stock_database()
        except Exception as e:
            print(f"❌ Error loading stock database: {e}")
            self.stock_database = self.get_popular_stocks()

    def create_stock_database(self):
        """Create stock database with comprehensive stock list"""
        try:
            print("📊 Creating comprehensive stock database...")

            # Start with popular stocks
            stocks = self.get_popular_stocks()

            # Add more comprehensive stock list
            additional_stocks = [
                # Banking & Financial Services
                ('BANKBARODA-EQ', '4668', 'NSE'),
                ('PNB-EQ', '10666', 'NSE'),
                ('CANBK-EQ', '10794', 'NSE'),
                ('UNIONBANK-EQ', '11195', 'NSE'),
                ('IDFCFIRSTB-EQ', '11184', 'NSE'),
                ('FEDERALBNK-EQ', '1023', 'NSE'),
                ('RBLBANK-EQ', '18391', 'NSE'),
                ('BANDHANBNK-EQ', '2263', 'NSE'),
                ('AXISBANK-EQ', '5900', 'NSE'),
                ('BAJAJFINSV-EQ', '16675', 'NSE'),
                ('INDUSINDBK-EQ', '5258', 'NSE'),

                # IT & Technology
                ('WIPRO-EQ', '3787', 'NSE'),
                ('HCLTECH-EQ', '7229', 'NSE'),
                ('TECHM-EQ', '13538', 'NSE'),
                ('MINDTREE-EQ', '14356', 'NSE'),
                ('MPHASIS-EQ', '4503', 'NSE'),
                ('LTI-EQ', '17818', 'NSE'),
                ('COFORGE-EQ', '11543', 'NSE'),
                ('PERSISTENT-EQ', '18365', 'NSE'),

                # Pharma & Healthcare
                ('DRREDDY-EQ', '881', 'NSE'),
                ('SUNPHARMA-EQ', '3351', 'NSE'),
                ('LUPIN-EQ', '10440', 'NSE'),
                ('BIOCON-EQ', '11373', 'NSE'),
                ('CADILAHC-EQ', '7929', 'NSE'),
                ('AUROPHARMA-EQ', '275', 'NSE'),
                ('TORNTPHARM-EQ', '3518', 'NSE'),
                ('DIVISLAB-EQ', '10940', 'NSE'),
                ('CIPLA-EQ', '694', 'NSE'),
                ('APOLLOHOSP-EQ', '157', 'NSE'),

                # Auto & Auto Components
                ('TATAMOTORS-EQ', '884', 'NSE'),
                ('BAJAJ-AUTO-EQ', '16669', 'NSE'),
                ('HEROMOTOCO-EQ', '345', 'NSE'),
                ('TVSMOTORS-EQ', '8479', 'NSE'),
                ('EICHERMOT-EQ', '910', 'NSE'),
                ('ASHOKLEY-EQ', '212', 'NSE'),
                ('ESCORTS-EQ', '958', 'NSE'),
                ('FORCE-EQ', '14304', 'NSE'),

                # FMCG & Consumer
                ('NESTLEIND-EQ', '17963', 'NSE'),
                ('BRITANNIA-EQ', '547', 'NSE'),
                ('GODREJCP-EQ', '1223', 'NSE'),
                ('MARICO-EQ', '531', 'NSE'),
                ('DABUR-EQ', '772', 'NSE'),
                ('COLPAL-EQ', '15141', 'NSE'),
                ('EMAMILTD-EQ', '14299', 'NSE'),
                ('JUBLFOOD-EQ', '18096', 'NSE'),
                ('VBL-EQ', '16490', 'NSE'),
                ('TATACONSUM-EQ', '3432', 'NSE'),
                ('MCDOWELL-N-EQ', '13611', 'NSE'),

                # Metals & Mining
                ('TATASTEEL-EQ', '895', 'NSE'),
                ('JSWSTEEL-EQ', '11723', 'NSE'),
                ('HINDALCO-EQ', '348', 'NSE'),
                ('VEDL-EQ', '784', 'NSE'),
                ('NMDC-EQ', '15332', 'NSE'),
                ('HINDZINC-EQ', '364', 'NSE'),
                ('NATIONALUM-EQ', '6364', 'NSE'),
                ('MOIL-EQ', '2303', 'NSE'),
                ('SAIL-EQ', '2963', 'NSE'),

                # Energy & Power
                ('ONGC-EQ', '2475', 'NSE'),
                ('BPCL-EQ', '526', 'NSE'),
                ('IOC-EQ', '1624', 'NSE'),
                ('GAIL-EQ', '1207', 'NSE'),
                ('COALINDIA-EQ', '20374', 'NSE'),
                ('NTPC-EQ', '11630', 'NSE'),
                ('POWERGRID-EQ', '14977', 'NSE'),
                ('ADANIGREEN-EQ', '25746', 'NSE'),
                ('ADANITRANS-EQ', '13611', 'NSE'),
                ('ADANIPOWER-EQ', '25746', 'NSE'),
                ('TATAPOWER-EQ', '3426', 'NSE'),
                ('NHPC-EQ', '4204', 'NSE'),

                # Cement
                ('ULTRACEMCO-EQ', '11532', 'NSE'),
                ('AMBUJACEM-EQ', '1270', 'NSE'),
                ('ACC-EQ', '22', 'NSE'),
                ('SHREECEM-EQ', '3103', 'NSE'),
                ('RAMCOCEM-EQ', '2043', 'NSE'),
                ('INDIACEM-EQ', '1515', 'NSE'),

                # Infrastructure & Construction
                ('ADANIPORTS-EQ', '15083', 'NSE'),
                ('GRASIM-EQ', '1232', 'NSE'),
                ('M&M-EQ', '519', 'NSE'),

                # Retail & Others
                ('DMART-EQ', '14299', 'NSE'),
                ('PIDILITIND-EQ', '2664', 'NSE'),
                ('BERGEPAINT-EQ', '404', 'NSE'),
                ('IDEA-EQ', '14366', 'NSE'),
                ('YESBANK-EQ', '11915', 'NSE'),
            ]

            # Combine all stocks and remove duplicates
            all_stocks = stocks + additional_stocks
            unique_stocks = list(set(all_stocks))  # Remove duplicates

            # Sort by symbol name
            unique_stocks.sort(key=lambda x: x[0])

            self.stock_database = unique_stocks

            # Save to file
            self.save_stock_database()

            print(f"✅ Created stock database with {len(unique_stocks)} stocks")

        except Exception as e:
            print(f"❌ Error creating stock database: {e}")
            self.stock_database = self.get_popular_stocks()

    def save_stock_database(self):
        """Save stock database to local file"""
        try:
            database_path = os.path.join(os.path.dirname(__file__), 'stock_database.json')

            database = {
                "metadata": {
                    "created_at": "2024-01-31T00:00:00",
                    "total_stocks": len(self.stock_database),
                    "source": "Curated NSE Stock List",
                    "version": "1.0"
                },
                "stocks": self.stock_database
            }

            with open(database_path, 'w', encoding='utf-8') as f:
                json.dump(database, f, indent=2, ensure_ascii=False)

            print(f"💾 Stock database saved to: {database_path}")

        except Exception as e:
            print(f"❌ Error saving stock database: {e}")
    
    def get_popular_stocks(self) -> List[Tuple[str, str, str]]:
        """Get popular stocks as fallback"""
        return [
            ('RELIANCE-EQ', '2885', 'NSE'),
            ('TCS-EQ', '11536', 'NSE'),
            ('INFY-EQ', '1594', 'NSE'),
            ('HDFCBANK-EQ', '1333', 'NSE'),
            ('ICICIBANK-EQ', '4963', 'NSE'),
            ('SBIN-EQ', '3045', 'NSE'),
            ('BHARTIARTL-EQ', '10604', 'NSE'),
            ('ITC-EQ', '424', 'NSE'),
            ('HINDUNILVR-EQ', '356', 'NSE'),
            ('LT-EQ', '11483', 'NSE'),
            ('KOTAKBANK-EQ', '1922', 'NSE'),
            ('ASIANPAINT-EQ', '236', 'NSE'),
            ('MARUTI-EQ', '10999', 'NSE'),
            ('BAJFINANCE-EQ', '317', 'NSE'),
            ('TITAN-EQ', '3506', 'NSE'),
            ('CGCL-EQ', '20329', 'NSE'),
        ]
    
    def create_ui(self):
        """Create the stock search UI components"""
        # Main search frame
        self.search_frame = ctk.CTkFrame(self.parent_frame, fg_color="transparent")
        self.search_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Title
        title_label = ctk.CTkLabel(self.search_frame, text="Stock Selection",
                                  font=ctk.CTkFont(family="Segoe UI", size=12, weight="bold"))
        title_label.pack(pady=(0, 8))
        
        # Input row
        input_row = ctk.CTkFrame(self.search_frame, fg_color="transparent")
        input_row.pack(fill=tk.X)
        
        # Search entry
        self.stock_entry = ctk.CTkEntry(input_row,
                                       placeholder_text="Type stock symbol (e.g., RELIANCE, TCS, INFY)",
                                       font=ctk.CTkFont(family="Segoe UI", size=11),
                                       width=300, height=35, corner_radius=8,
                                       state="disabled")
        self.stock_entry.pack(side=tk.LEFT, padx=(0, 8))
        
        # Search button
        self.search_btn = ctk.CTkButton(input_row, text="🔍 Search",
                                       fg_color="#4A90E2", hover_color="#3A7BC8",
                                       font=ctk.CTkFont(family="Segoe UI", size=11, weight="bold"),
                                       width=80, height=35, corner_radius=8,
                                       command=self._on_search_click,
                                       state="disabled")
        self.search_btn.pack(side=tk.LEFT, padx=(0, 8))
        
        # Current stock display
        self.current_stock_label = ctk.CTkLabel(input_row, text=f"Current: {self.current_stock}",
                                               font=ctk.CTkFont(family="Segoe UI", size=11, weight="bold"),
                                               text_color="#7ED321")
        self.current_stock_label.pack(side=tk.LEFT, padx=(0, 8))
        
        # Status label
        self.status_label = ctk.CTkLabel(input_row, text="📊 Loading...",
                                        font=ctk.CTkFont(family="Segoe UI", size=10),
                                        text_color="#FFA500")
        self.status_label.pack(side=tk.LEFT)
        
        # Bind events
        self.stock_entry.bind('<KeyRelease>', self._on_key_release)
        self.stock_entry.bind('<FocusOut>', self._on_focus_out)
        
        # Enable if database is loaded
        if self.stock_database:
            self.enable_search()
    
    def enable_search(self):
        """Enable search functionality"""
        try:
            self.is_enabled = True
            self.stock_entry.configure(state="normal")
            self.stock_entry.configure(placeholder_text="Type stock symbol (e.g., RELIANCE, TCS, INFY)")
            self.stock_entry.delete(0, tk.END)
            self.stock_entry.insert(0, self.current_stock.replace('-EQ', ''))
            
            self.search_btn.configure(state="normal")
            self.status_label.configure(text=f"✅ {len(self.stock_database)} stocks ready", text_color="#7ED321")
            
            print("✅ Stock search enabled")
        except Exception as e:
            print(f"❌ Error enabling search: {e}")
    
    def disable_search(self):
        """Disable search functionality"""
        try:
            self.is_enabled = False
            self.stock_entry.configure(state="disabled")
            self.search_btn.configure(state="disabled")
            self.status_label.configure(text="📊 Loading...", text_color="#FFA500")
            self._hide_suggestions()
        except Exception as e:
            print(f"❌ Error disabling search: {e}")
    
    def _on_key_release(self, event):
        """Handle key release in search entry"""
        if not self.is_enabled:
            return
            
        try:
            search_text = self.stock_entry.get().strip().upper()
            if len(search_text) >= 1:
                # Use threading to prevent UI blocking
                threading.Thread(target=self._perform_search, args=(search_text,), daemon=True).start()
            else:
                self._hide_suggestions()
        except Exception as e:
            print(f"❌ Error in key release: {e}")
    
    def _on_search_click(self):
        """Handle search button click"""
        if not self.is_enabled:
            return
            
        try:
            # Temporarily disable button
            self.search_btn.configure(state="disabled")
            
            search_text = self.stock_entry.get().strip().upper()
            if search_text:
                threading.Thread(target=self._perform_search, args=(search_text,), daemon=True).start()
            
            # Re-enable after delay
            self.parent_frame.after(500, lambda: self.search_btn.configure(state="normal"))
            
        except Exception as e:
            print(f"❌ Error in search click: {e}")
            self.search_btn.configure(state="normal")
    
    def _on_focus_out(self, event):
        """Handle focus out - delay hiding suggestions"""
        self.parent_frame.after(200, self._hide_suggestions)
    
    def _perform_search(self, search_text: str):
        """Perform stock search in background thread"""
        try:
            results = self.search_stocks(search_text)
            # Schedule UI update on main thread
            self.parent_frame.after_idle(lambda: self._show_suggestions(results))
        except Exception as e:
            print(f"❌ Error in background search: {e}")
            self.parent_frame.after_idle(self._hide_suggestions)
    
    def search_stocks(self, search_text: str) -> List[Tuple[str, str, str]]:
        """Search stocks in local database"""
        try:
            search_text = search_text.upper().strip()
            results = []
            
            # First: exact matches and starts with
            for symbol, token, exchange in self.stock_database:
                clean_symbol = symbol.replace('-EQ', '')
                if clean_symbol == search_text or clean_symbol.startswith(search_text):
                    results.append((symbol, token, exchange))
                    if len(results) >= 5:
                        break
            
            # Second: contains matches (if we need more results)
            if len(results) < 5:
                for symbol, token, exchange in self.stock_database:
                    clean_symbol = symbol.replace('-EQ', '')
                    if search_text in clean_symbol and (symbol, token, exchange) not in results:
                        results.append((symbol, token, exchange))
                        if len(results) >= 5:
                            break
            
            return results
            
        except Exception as e:
            print(f"❌ Error searching stocks: {e}")
            return []
    
    def _show_suggestions(self, suggestions: List[Tuple[str, str, str]]):
        """Show suggestions dropdown with proper positioning"""
        try:
            self._hide_suggestions()

            if not suggestions or not self.is_enabled:
                return

            # Limit to 5 suggestions
            limited_suggestions = suggestions[:5]

            # Calculate proper positioning - ensure widgets are updated
            self.stock_entry.update_idletasks()
            self.parent_frame.update_idletasks()

            # Get the scrollable frame (parent of parent_frame) for proper positioning
            scrollable_frame = self.parent_frame.master

            # Get position relative to the scrollable frame
            entry_x = self.stock_entry.winfo_x()
            entry_y = self.stock_entry.winfo_y() + self.stock_entry.winfo_height() + 2
            entry_width = self.stock_entry.winfo_width()

            # Add parent frame offsets
            parent_x = self.parent_frame.winfo_x()
            parent_y = self.parent_frame.winfo_y()

            # Calculate final position
            final_x = parent_x + entry_x
            final_y = parent_y + entry_y

            # Create suggestions frame as child of scrollable frame
            dropdown_height = len(limited_suggestions) * 35 + 10
            self.suggestions_frame = ctk.CTkFrame(scrollable_frame,
                                                 fg_color="#2B2B2B",
                                                 border_width=1,
                                                 border_color="#4A4A4A",
                                                 width=entry_width,
                                                 height=dropdown_height)

            # Position the dropdown directly below the search entry
            self.suggestions_frame.place(x=final_x, y=final_y)

            # Bring to front
            self.suggestions_frame.lift()

            # Add suggestion buttons
            for i, (symbol, token, exchange) in enumerate(limited_suggestions):
                clean_symbol = symbol.replace('-EQ', '')
                suggestion_text = f"{clean_symbol} ({exchange})"

                btn = ctk.CTkButton(self.suggestions_frame,
                                   text=suggestion_text,
                                   fg_color="transparent",
                                   hover_color="#3A3A3A",
                                   font=ctk.CTkFont(family="Segoe UI", size=10),
                                   height=30,
                                   corner_radius=4,
                                   anchor="w",
                                   command=lambda s=symbol, t=token: self._select_stock(s, t))
                btn.pack(fill=tk.X, padx=3, pady=1)

            print(f"📍 Dropdown positioned at: x={final_x}, y={final_y} (below search input)")

        except Exception as e:
            print(f"❌ Error showing suggestions: {e}")
            import traceback
            traceback.print_exc()
            self._hide_suggestions()
    
    def _hide_suggestions(self):
        """Hide suggestions dropdown"""
        try:
            if self.suggestions_frame:
                self.suggestions_frame.destroy()
                self.suggestions_frame = None
        except Exception as e:
            print(f"⚠️ Error hiding suggestions: {e}")
            self.suggestions_frame = None
    
    def _select_stock(self, symbol: str, token: str):
        """Handle stock selection with complete function restart"""
        try:
            # Update UI immediately
            clean_symbol = symbol.replace('-EQ', '')
            self.stock_entry.delete(0, tk.END)
            self.stock_entry.insert(0, clean_symbol)
            self.current_stock_label.configure(text=f"Current: {symbol}")
            self.current_stock = symbol

            # Hide suggestions
            self._hide_suggestions()

            # Show loading status
            self.status_label.configure(text="🛑 Stopping all functions...", text_color="#FF6B6B")

            # Disable search temporarily to prevent multiple selections
            self.stock_entry.configure(state="disabled")
            self.search_btn.configure(state="disabled")

            print(f"🔄 Switching to stock: {symbol}")

            # Call callback immediately on main thread (no background thread needed)
            # The main app will handle the complete stop and restart
            try:
                if 'stock_selected' in self.callbacks:
                    self.callbacks['stock_selected'](symbol, token)

                # Update status to show restart
                self.status_label.configure(text="🚀 Restarting functions...", text_color="#FFA500")

                # Re-enable UI after a delay
                self.parent_frame.after(3000, self._enable_after_switch)

            except Exception as e:
                print(f"❌ Error in stock switch: {e}")
                # Re-enable UI even on error
                self._enable_after_switch()

        except Exception as e:
            print(f"❌ Error selecting stock: {e}")
            self._enable_after_switch()

    def _enable_after_switch(self):
        """Re-enable UI after stock switch"""
        try:
            self.stock_entry.configure(state="normal")
            self.search_btn.configure(state="normal")
            self.status_label.configure(text=f"✅ {len(self.stock_database)} stocks ready", text_color="#7ED321")
            print(f"✅ Stock switch completed")
        except Exception as e:
            print(f"❌ Error re-enabling UI: {e}")
    
    def update_current_stock(self, symbol: str):
        """Update current stock display"""
        try:
            self.current_stock = symbol
            self.current_stock_label.configure(text=f"Current: {symbol}")
            if self.is_enabled:
                clean_symbol = symbol.replace('-EQ', '')
                self.stock_entry.delete(0, tk.END)
                self.stock_entry.insert(0, clean_symbol)
        except Exception as e:
            print(f"❌ Error updating current stock: {e}")
    
    def get_widget(self):
        """Get the main widget frame"""
        return self.search_frame
