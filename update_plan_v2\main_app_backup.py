#!/usr/bin/env python3
"""
Main Application - Ultimate Trading Analysis v2
Modular version with separate component files
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import numpy as np
import websocket
import json
import ssl
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from config import EXECUTIVE_COLORS, APP_CONFIG
from trading_signals import TradingSignalsWidget
from market_analytics import MarketAnalyticsWidget
from trading_charts import TradingChartsWidget
from data_simulator import MarketDataSimulator


class UltimateTrading:
    """Main trading application with modular components"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        
        # Data simulator
        self.simulator = MarketDataSimulator()
        
        # UI components
        self.order_book_frame = None
        self.trading_signals = None
        self.market_analytics = None
        self.trading_charts = None
        
        # Control flags
        self.running = False
        self.data_source = "simulated"  # simulated, live, disconnected

        # WebSocket connection for live data
        self.ws = None
        self.ws_connected = False
        self.live_order_book_data = {'bids': [], 'asks': []}
        
        self.create_ui()
        self.start_data_updates()
    
    def setup_window(self):
        """Setup main window"""
        self.root.title(APP_CONFIG['window_title'])
        self.root.geometry(APP_CONFIG['window_size'])
        self.root.configure(bg=EXECUTIVE_COLORS['bg_primary'])
        
        # Make window resizable
        self.root.resizable(True, True)
        
        # Center window on screen
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_ui(self):
        """Create main user interface"""
        # Header
        self.create_header()
        
        # Main content area
        self.create_main_content()
    
    def create_header(self):
        """Create application header"""
        header_frame = tk.Frame(self.root, bg=EXECUTIVE_COLORS['bg_secondary'], height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # Left side - Title and icon
        left_frame = tk.Frame(header_frame, bg=EXECUTIVE_COLORS['bg_secondary'])
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20)
        
        title_label = tk.Label(left_frame, text="🚀 Ultimate Trading Analysis v2 - CGCL Live Market",
                              bg=EXECUTIVE_COLORS['bg_secondary'], 
                              fg=EXECUTIVE_COLORS['text_primary'],
                              font=('Arial', 16, 'bold'))
        title_label.pack(side=tk.LEFT, pady=15)
        
        # Right side - Data source controls
        right_frame = tk.Frame(header_frame, bg=EXECUTIVE_COLORS['bg_secondary'])
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=20)
        
        # Data source label
        tk.Label(right_frame, text="Data Source:",
                bg=EXECUTIVE_COLORS['bg_secondary'], 
                fg=EXECUTIVE_COLORS['text_secondary'],
                font=('Arial', 10)).pack(side=tk.LEFT, pady=20, padx=(0, 10))
        
        # Data source buttons
        self.create_data_source_buttons(right_frame)
    
    def create_data_source_buttons(self, parent):
        """Create data source control buttons"""
        button_frame = tk.Frame(parent, bg=EXECUTIVE_COLORS['bg_secondary'])
        button_frame.pack(side=tk.LEFT, pady=15)
        
        # Live button
        self.live_btn = tk.Button(button_frame, text="📡 Live", 
                                 bg=EXECUTIVE_COLORS['success'], fg='white',
                                 font=('Arial', 9, 'bold'), width=8,
                                 command=lambda: self.set_data_source('live'))
        self.live_btn.pack(side=tk.LEFT, padx=2)
        
        # Simulated button
        self.sim_btn = tk.Button(button_frame, text="🎯 Simulated", 
                                bg=EXECUTIVE_COLORS['accent_blue'], fg='white',
                                font=('Arial', 9, 'bold'), width=10,
                                command=lambda: self.set_data_source('simulated'))
        self.sim_btn.pack(side=tk.LEFT, padx=2)
        
        # Disconnected button
        self.disc_btn = tk.Button(button_frame, text="❌ Disconnected", 
                                 bg=EXECUTIVE_COLORS['danger'], fg='white',
                                 font=('Arial', 9, 'bold'), width=12,
                                 command=lambda: self.set_data_source('disconnected'))
        self.disc_btn.pack(side=tk.LEFT, padx=2)
    
    def create_main_content(self):
        """Create HFT-style single column, multi-row layout"""
        # Main container - single column
        main_container = tk.Frame(self.root, bg='#0a0a0a')  # Deep black background
        main_container.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        # ROW 1: Market Status Header
        self.create_market_status_row(main_container)

        # ROW 2: Order Book Display
        self.create_order_book_row(main_container)

        # ROW 3: Price Chart
        self.create_price_chart_row(main_container)

        # ROW 4: Market Depth & Volume
        self.create_market_depth_row(main_container)

        # ROW 5: Trading Signals & Analytics
        self.create_signals_analytics_row(main_container)

        # ROW 6: Position & PnL
        self.create_position_pnl_row(main_container)

    def create_market_status_row(self, parent):
        """ROW 1: Market status header with key metrics"""
        status_frame = tk.Frame(parent, bg='#0a0a0a', height=60)
        status_frame.pack(fill=tk.X, padx=1, pady=1)
        status_frame.pack_propagate(False)

        # Left side - Symbol and price
        left_frame = tk.Frame(status_frame, bg='#151515')
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=1, pady=1)

        symbol_label = tk.Label(left_frame, text="CGCL", font=('Consolas', 16, 'bold'),
                               bg='#151515', fg='#00ffff', width=8)
        symbol_label.pack(side=tk.LEFT, padx=10, pady=15)

        self.price_label = tk.Label(left_frame, text="₹872.45", font=('Consolas', 16, 'bold'),
                                   bg='#151515', fg='#00ff41', width=12)
        self.price_label.pack(side=tk.LEFT, padx=5, pady=15)

        # Center - Connection status
        center_frame = tk.Frame(status_frame, bg='#151515')
        center_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=1, pady=1)

        self.connection_label = tk.Label(center_frame, text="● LIVE FEED", font=('Consolas', 12, 'bold'),
                                        bg='#151515', fg='#00ff41')
        self.connection_label.pack(expand=True, pady=20)

        # Right side - Key metrics
        right_frame = tk.Frame(status_frame, bg='#151515')
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=1, pady=1)

        metrics_text = "VOL: 1.2M | SPREAD: ₹0.15 | LAST: 14:32:45"
        self.metrics_label = tk.Label(right_frame, text=metrics_text, font=('Consolas', 10),
                                     bg='#151515', fg='#b0b0b0')
        self.metrics_label.pack(side=tk.RIGHT, padx=10, pady=20)

    def create_order_book_row(self, parent):
        """ROW 2: Order book display"""
        book_frame = tk.Frame(parent, bg='#0a0a0a', height=400)
        book_frame.pack(fill=tk.X, padx=1, pady=1)
        book_frame.pack_propagate(False)

        # Order book container
        book_container = tk.Frame(book_frame, bg='#151515')
        book_container.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

        # Professional header with proper column layout
        header_frame = tk.Frame(book_container, bg='#1a1a1a', height=50)
        header_frame.pack(fill=tk.X, padx=1, pady=1)
        header_frame.pack_propagate(False)

        # Main title row
        title_frame = tk.Frame(header_frame, bg='#1a1a1a', height=25)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)

        # Bid title (left)
        tk.Label(title_frame, text="BIDS (BUY ORDERS)", font=('Consolas', 11, 'bold'),
                bg='#1a1a1a', fg='#00ff41', width=40).pack(side=tk.LEFT, padx=10)

        # Center spread
        tk.Label(title_frame, text="SPREAD", font=('Consolas', 10, 'bold'),
                bg='#1a1a1a', fg='#ffaa00', width=15).pack(side=tk.LEFT, padx=5)

        # Ask title (right)
        tk.Label(title_frame, text="ASKS (SELL ORDERS)", font=('Consolas', 11, 'bold'),
                bg='#1a1a1a', fg='#ff0844', width=40).pack(side=tk.RIGHT, padx=10)

        # Column headers row using GRID layout for perfect alignment
        cols_frame = tk.Frame(header_frame, bg='#1a1a1a', height=25)
        cols_frame.pack(fill=tk.X)
        cols_frame.pack_propagate(False)

        # Configure grid columns with increased widths for better readability
        cols_frame.grid_columnconfigure(0, weight=0, minsize=150)  # TOTAL (increased)
        cols_frame.grid_columnconfigure(1, weight=0, minsize=100)  # ORDERS (increased)
        cols_frame.grid_columnconfigure(2, weight=0, minsize=120)  # SIZE (increased)
        cols_frame.grid_columnconfigure(3, weight=0, minsize=120)  # BID PRICE (increased)
        cols_frame.grid_columnconfigure(4, weight=0, minsize=40)   # SEPARATOR (increased)
        cols_frame.grid_columnconfigure(5, weight=0, minsize=120)  # ASK PRICE (increased)
        cols_frame.grid_columnconfigure(6, weight=0, minsize=120)  # SIZE (increased)
        cols_frame.grid_columnconfigure(7, weight=0, minsize=100)  # ORDERS (increased)
        cols_frame.grid_columnconfigure(8, weight=0, minsize=150)  # TOTAL (increased)

        # Bid headers
        tk.Label(cols_frame, text="TOTAL", font=('Consolas', 9, 'bold'),
                bg='#1a1a1a', fg='#00ff41', anchor='e').grid(row=0, column=0, sticky='ew', padx=2)
        tk.Label(cols_frame, text="ORDERS", font=('Consolas', 9, 'bold'),
                bg='#1a1a1a', fg='#00ff41', anchor='center').grid(row=0, column=1, sticky='ew', padx=2)
        tk.Label(cols_frame, text="SIZE", font=('Consolas', 9, 'bold'),
                bg='#1a1a1a', fg='#00ff41', anchor='e').grid(row=0, column=2, sticky='ew', padx=2)
        tk.Label(cols_frame, text="PRICE", font=('Consolas', 9, 'bold'),
                bg='#1a1a1a', fg='#00ff41', anchor='e').grid(row=0, column=3, sticky='ew', padx=2)

        # Center separator
        tk.Label(cols_frame, text="│", font=('Consolas', 12, 'bold'),
                bg='#1a1a1a', fg='#ffaa00', anchor='center').grid(row=0, column=4, sticky='ew')

        # Ask headers
        tk.Label(cols_frame, text="PRICE", font=('Consolas', 9, 'bold'),
                bg='#1a1a1a', fg='#ff0844', anchor='w').grid(row=0, column=5, sticky='ew', padx=2)
        tk.Label(cols_frame, text="SIZE", font=('Consolas', 9, 'bold'),
                bg='#1a1a1a', fg='#ff0844', anchor='e').grid(row=0, column=6, sticky='ew', padx=2)
        tk.Label(cols_frame, text="ORDERS", font=('Consolas', 9, 'bold'),
                bg='#1a1a1a', fg='#ff0844', anchor='center').grid(row=0, column=7, sticky='ew', padx=2)
        tk.Label(cols_frame, text="TOTAL", font=('Consolas', 9, 'bold'),
                bg='#1a1a1a', fg='#ff0844', anchor='e').grid(row=0, column=8, sticky='ew', padx=2)

        # Scrollable order book data
        canvas = tk.Canvas(book_container, bg='#151515', highlightthickness=0, height=350)
        scrollbar = tk.Scrollbar(book_container, orient="vertical", command=canvas.yview)
        self.order_book_frame = tk.Frame(canvas, bg='#151515')

        self.order_book_frame.bind("<Configure>",
                                  lambda e: canvas.configure(scrollregion=canvas.bbox("all")))

        canvas.create_window((0, 0), window=self.order_book_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=1, pady=1)
        scrollbar.pack(side="right", fill="y")

        # Initialize with sample data
        self.update_order_book_display([], [])

    def create_price_chart_row(self, parent):
        """ROW 3: Price chart"""
        chart_frame = tk.Frame(parent, bg='#0a0a0a', height=300)
        chart_frame.pack(fill=tk.X, padx=1, pady=1)
        chart_frame.pack_propagate(False)

        # Chart container
        chart_container = tk.Frame(chart_frame, bg='#151515')
        chart_container.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

        # Chart header
        chart_header = tk.Frame(chart_container, bg='#1a1a1a', height=25)
        chart_header.pack(fill=tk.X, padx=1, pady=1)
        chart_header.pack_propagate(False)

        tk.Label(chart_header, text="PRICE CHART - 1MIN", font=('Consolas', 12, 'bold'),
                bg='#1a1a1a', fg='#00ffff').pack(side=tk.LEFT, padx=10, pady=3)

        # Trading charts
        self.trading_charts = TradingChartsWidget(chart_container)
        self.trading_charts.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

    def create_market_depth_row(self, parent):
        """ROW 4: Market depth and volume"""
        depth_frame = tk.Frame(parent, bg='#0a0a0a', height=150)
        depth_frame.pack(fill=tk.X, padx=1, pady=1)
        depth_frame.pack_propagate(False)

        # Depth container
        depth_container = tk.Frame(depth_frame, bg='#151515')
        depth_container.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

        # Header
        depth_header = tk.Frame(depth_container, bg='#1a1a1a', height=25)
        depth_header.pack(fill=tk.X, padx=1, pady=1)
        depth_header.pack_propagate(False)

        tk.Label(depth_header, text="MARKET DEPTH & VOLUME", font=('Consolas', 12, 'bold'),
                bg='#1a1a1a', fg='#00ffff').pack(side=tk.LEFT, padx=10, pady=3)

        # Volume metrics
        volume_frame = tk.Frame(depth_container, bg='#151515')
        volume_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.volume_label = tk.Label(volume_frame, text="VOLUME: 1,234,567 | AVG: 45,230 | VWAP: ₹871.23",
                                    font=('Consolas', 11), bg='#151515', fg='#ffaa00')
        self.volume_label.pack(pady=10)

        self.depth_label = tk.Label(volume_frame, text="BID DEPTH: ₹2.1M | ASK DEPTH: ₹1.8M | IMBALANCE: +14%",
                                   font=('Consolas', 11), bg='#151515', fg='#b0b0b0')
        self.depth_label.pack(pady=5)

    def create_signals_analytics_row(self, parent):
        """ROW 5: Trading signals and analytics"""
        signals_frame = tk.Frame(parent, bg='#0a0a0a', height=120)
        signals_frame.pack(fill=tk.X, padx=1, pady=1)
        signals_frame.pack_propagate(False)

        # Signals container
        signals_container = tk.Frame(signals_frame, bg='#151515')
        signals_container.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

        # Left - Trading signals
        left_signals = tk.Frame(signals_container, bg='#151515')
        left_signals.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=1, pady=1)

        self.trading_signals = TradingSignalsWidget(left_signals, height=100)
        self.trading_signals.pack(fill=tk.BOTH, expand=True)

        # Right - Market analytics
        right_analytics = tk.Frame(signals_container, bg='#151515')
        right_analytics.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=1, pady=1)

        self.market_analytics = MarketAnalyticsWidget(right_analytics)
        self.market_analytics.pack(fill=tk.BOTH, expand=True)

    def create_position_pnl_row(self, parent):
        """ROW 6: Position and PnL information"""
        pnl_frame = tk.Frame(parent, bg='#0a0a0a', height=80)
        pnl_frame.pack(fill=tk.X, padx=1, pady=1)
        pnl_frame.pack_propagate(False)

        # PnL container
        pnl_container = tk.Frame(pnl_frame, bg='#151515')
        pnl_container.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

        # Header
        pnl_header = tk.Frame(pnl_container, bg='#1a1a1a', height=25)
        pnl_header.pack(fill=tk.X, padx=1, pady=1)
        pnl_header.pack_propagate(False)

        tk.Label(pnl_header, text="POSITION & P&L", font=('Consolas', 12, 'bold'),
                bg='#1a1a1a', fg='#00ffff').pack(side=tk.LEFT, padx=10, pady=3)

        # PnL metrics
        pnl_metrics = tk.Frame(pnl_container, bg='#151515')
        pnl_metrics.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.position_label = tk.Label(pnl_metrics, text="POSITION: FLAT | SIZE: 0 | AVG: ₹0.00",
                                      font=('Consolas', 11), bg='#151515', fg='#b0b0b0')
        self.position_label.pack(side=tk.LEFT, padx=10)

        self.pnl_label = tk.Label(pnl_metrics, text="UNREALIZED: ₹0.00 | REALIZED: ₹+1,250.00",
                                 font=('Consolas', 11), bg='#151515', fg='#00ff41')
        self.pnl_label.pack(side=tk.RIGHT, padx=10)

    def update_order_book_display(self, bids, asks):
        """Update professional order book display with bids left, asks right"""
        # Clear existing data
        for widget in self.order_book_frame.winfo_children():
            widget.destroy()

        # Display 10 levels with professional layout
        max_levels = 10

        for i in range(max_levels):
            level_frame = tk.Frame(self.order_book_frame, bg='#151515')
            level_frame.pack(fill=tk.X, pady=1)

            # Configure grid layout for this row - same increased widths as headers
            level_frame.grid_columnconfigure(0, weight=0, minsize=150)  # TOTAL (increased)
            level_frame.grid_columnconfigure(1, weight=0, minsize=100)  # ORDERS (increased)
            level_frame.grid_columnconfigure(2, weight=0, minsize=120)  # SIZE (increased)
            level_frame.grid_columnconfigure(3, weight=0, minsize=120)  # BID PRICE (increased)
            level_frame.grid_columnconfigure(4, weight=0, minsize=40)   # SEPARATOR (increased)
            level_frame.grid_columnconfigure(5, weight=0, minsize=120)  # ASK PRICE (increased)
            level_frame.grid_columnconfigure(6, weight=0, minsize=120)  # SIZE (increased)
            level_frame.grid_columnconfigure(7, weight=0, minsize=100)  # ORDERS (increased)
            level_frame.grid_columnconfigure(8, weight=0, minsize=150)  # TOTAL (increased)

            if i < len(bids):
                bid_qty, bid_orders, bid_price = bids[i]
                bid_total = bid_price * bid_qty

                # Bid columns using GRID - exact same layout as headers
                tk.Label(level_frame, text=f"₹{bid_total:,.0f}", font=('Consolas', 10),
                        bg='#151515', fg='#666666', anchor='e').grid(row=0, column=0, sticky='ew', padx=2)
                tk.Label(level_frame, text=f"({bid_orders})", font=('Consolas', 10),
                        bg='#151515', fg='#666666', anchor='center').grid(row=0, column=1, sticky='ew', padx=2)
                tk.Label(level_frame, text=f"{bid_qty:,}", font=('Consolas', 10),
                        bg='#151515', fg='#b0b0b0', anchor='e').grid(row=0, column=2, sticky='ew', padx=2)
                tk.Label(level_frame, text=f"{bid_price:.2f}", font=('Consolas', 10, 'bold'),
                        bg='#151515', fg='#00ff41', anchor='e').grid(row=0, column=3, sticky='ew', padx=2)
            else:
                # Empty bid level with exact same grid structure
                tk.Label(level_frame, text="", font=('Consolas', 10),
                        bg='#151515', fg='#666666', anchor='e').grid(row=0, column=0, sticky='ew', padx=2)
                tk.Label(level_frame, text="", font=('Consolas', 10),
                        bg='#151515', fg='#666666', anchor='center').grid(row=0, column=1, sticky='ew', padx=2)
                tk.Label(level_frame, text="", font=('Consolas', 10),
                        bg='#151515', fg='#b0b0b0', anchor='e').grid(row=0, column=2, sticky='ew', padx=2)
                tk.Label(level_frame, text="", font=('Consolas', 10),
                        bg='#151515', fg='#00ff41', anchor='e').grid(row=0, column=3, sticky='ew', padx=2)

            # Center separator using GRID with spread info
            if i == 0 and bids and asks:  # Show spread on first level
                spread = asks[0][0] - bids[0][2]
                tk.Label(level_frame, text=f"₹{spread:.2f}", font=('Consolas', 9, 'bold'),
                        bg='#151515', fg='#ffaa00', anchor='center').grid(row=0, column=4, sticky='ew')
            else:
                tk.Label(level_frame, text="│", font=('Consolas', 10),
                        bg='#151515', fg='#333333', anchor='center').grid(row=0, column=4, sticky='ew')

            # For asks, show from lowest to highest price (reverse order)
            ask_idx = max_levels - 1 - i
            if ask_idx < len(asks):
                ask_price, ask_orders, ask_qty = asks[ask_idx]
                ask_total = ask_price * ask_qty

                # Ask columns using GRID - exact same layout as headers
                tk.Label(level_frame, text=f"{ask_price:.2f}", font=('Consolas', 10, 'bold'),
                        bg='#151515', fg='#ff0844', anchor='w').grid(row=0, column=5, sticky='ew', padx=2)
                tk.Label(level_frame, text=f"{ask_qty:,}", font=('Consolas', 10),
                        bg='#151515', fg='#b0b0b0', anchor='e').grid(row=0, column=6, sticky='ew', padx=2)
                tk.Label(level_frame, text=f"({ask_orders})", font=('Consolas', 10),
                        bg='#151515', fg='#666666', anchor='center').grid(row=0, column=7, sticky='ew', padx=2)
                tk.Label(level_frame, text=f"₹{ask_total:,.0f}", font=('Consolas', 10),
                        bg='#151515', fg='#666666', anchor='e').grid(row=0, column=8, sticky='ew', padx=2)
            else:
                # Empty ask level with exact same grid structure
                tk.Label(level_frame, text="", font=('Consolas', 10),
                        bg='#151515', fg='#ff0844', anchor='w').grid(row=0, column=5, sticky='ew', padx=2)
                tk.Label(level_frame, text="", font=('Consolas', 10),
                        bg='#151515', fg='#b0b0b0', anchor='e').grid(row=0, column=6, sticky='ew', padx=2)
                tk.Label(level_frame, text="", font=('Consolas', 10),
                        bg='#151515', fg='#666666', anchor='center').grid(row=0, column=7, sticky='ew', padx=2)
                tk.Label(level_frame, text="", font=('Consolas', 10),
                        bg='#151515', fg='#666666', anchor='e').grid(row=0, column=8, sticky='ew', padx=2)





    def create_trading_analysis(self, parent):
        """Create trading analysis and signals"""
        # Title
        title_label = tk.Label(
            parent,
            text="📈 TRADING ANALYSIS & SIGNALS",
            font=('Segoe UI', 14, 'bold'),
            bg=EXECUTIVE_COLORS['bg_primary'],
            fg=EXECUTIVE_COLORS['text_primary']
        )
        title_label.pack(pady=(10, 15))

        # Analysis container
        analysis_container = tk.Frame(parent, bg=EXECUTIVE_COLORS['bg_primary'])
        analysis_container.pack(fill=tk.BOTH, expand=True, padx=10)

        # Trading signals (top half)
        signals_frame = tk.Frame(analysis_container, bg=EXECUTIVE_COLORS['bg_primary'])
        signals_frame.pack(fill=tk.X, pady=(0, 10))

        self.trading_signals = TradingSignalsWidget(signals_frame, height=180)
        self.trading_signals.pack(fill=tk.BOTH, expand=True)

        # Market analytics (bottom half)
        analytics_frame = tk.Frame(analysis_container, bg=EXECUTIVE_COLORS['bg_primary'])
        analytics_frame.pack(fill=tk.BOTH, expand=True)

        self.market_analytics = MarketAnalyticsWidget(analytics_frame)
        self.market_analytics.pack(fill=tk.BOTH, expand=True)

    def create_charts_container(self, parent):
        """Create charts container"""
        # Title
        title_label = tk.Label(
            parent,
            text="📊 TRADING CHARTS",
            font=('Segoe UI', 14, 'bold'),
            bg=EXECUTIVE_COLORS['bg_primary'],
            fg=EXECUTIVE_COLORS['text_primary']
        )
        title_label.pack(pady=(10, 5))

        # Charts container
        charts_frame = tk.Frame(parent, bg=EXECUTIVE_COLORS['bg_primary'])
        charts_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.trading_charts = TradingChartsWidget(charts_frame)
        self.trading_charts.pack(fill=tk.BOTH, expand=True)

    def update_order_book_levels(self, bids, asks):
        """Update order book levels with green/red text"""
        # Clear existing data
        for widget in self.order_book_frame.winfo_children():
            widget.destroy()

        # Display asks (red, top to bottom - highest to lowest)
        if asks:
            # Header for asks
            ask_header = tk.Label(
                self.order_book_frame,
                text="🔴 ASKS (Sell Orders)",
                font=('Segoe UI', 11, 'bold'),
                bg=EXECUTIVE_COLORS['bg_card'],
                fg=EXECUTIVE_COLORS['danger']
            )
            ask_header.pack(fill=tk.X, pady=(5, 10))

            # Show asks in reverse order (highest price first)
            for i, (price, orders, qty) in enumerate(reversed(asks[:10])):
                ask_frame = tk.Frame(self.order_book_frame, bg=EXECUTIVE_COLORS['bg_card'])
                ask_frame.pack(fill=tk.X, pady=1)

                # Price (red)
                price_label = tk.Label(
                    ask_frame,
                    text=f"₹{price:.2f}",
                    font=('Segoe UI', 10, 'bold'),
                    bg=EXECUTIVE_COLORS['bg_card'],
                    fg=EXECUTIVE_COLORS['danger'],
                    width=12,
                    anchor='w'
                )
                price_label.pack(side=tk.LEFT, padx=(10, 5))

                # Quantity
                qty_label = tk.Label(
                    ask_frame,
                    text=f"{qty:,}",
                    font=('Segoe UI', 10),
                    bg=EXECUTIVE_COLORS['bg_card'],
                    fg=EXECUTIVE_COLORS['text_secondary'],
                    width=10,
                    anchor='e'
                )
                qty_label.pack(side=tk.RIGHT, padx=(5, 10))

                # Orders count
                orders_label = tk.Label(
                    ask_frame,
                    text=f"({orders})",
                    font=('Segoe UI', 9),
                    bg=EXECUTIVE_COLORS['bg_card'],
                    fg=EXECUTIVE_COLORS['text_muted'],
                    width=6,
                    anchor='center'
                )
                orders_label.pack(side=tk.RIGHT, padx=2)

        # Spread separator
        spread_frame = tk.Frame(self.order_book_frame, bg=EXECUTIVE_COLORS['bg_card'], height=30)
        spread_frame.pack(fill=tk.X, pady=10)

        if bids and asks:
            spread = asks[0][0] - bids[0][2]  # ask_price - bid_price
            spread_label = tk.Label(
                spread_frame,
                text=f"📊 SPREAD: ₹{spread:.2f}",
                font=('Segoe UI', 10, 'bold'),
                bg=EXECUTIVE_COLORS['bg_card'],
                fg=EXECUTIVE_COLORS['warning']
            )
            spread_label.pack(expand=True)

        # Display bids (green, top to bottom - highest to lowest)
        if bids:
            # Header for bids
            bid_header = tk.Label(
                self.order_book_frame,
                text="🟢 BIDS (Buy Orders)",
                font=('Segoe UI', 11, 'bold'),
                bg=EXECUTIVE_COLORS['bg_card'],
                fg=EXECUTIVE_COLORS['success']
            )
            bid_header.pack(fill=tk.X, pady=(10, 10))

            # Show bids (highest price first)
            for i, (qty, orders, price) in enumerate(bids[:10]):
                bid_frame = tk.Frame(self.order_book_frame, bg=EXECUTIVE_COLORS['bg_card'])
                bid_frame.pack(fill=tk.X, pady=1)

                # Price (green)
                price_label = tk.Label(
                    bid_frame,
                    text=f"₹{price:.2f}",
                    font=('Segoe UI', 10, 'bold'),
                    bg=EXECUTIVE_COLORS['bg_card'],
                    fg=EXECUTIVE_COLORS['success'],
                    width=12,
                    anchor='w'
                )
                price_label.pack(side=tk.LEFT, padx=(10, 5))

                # Quantity
                qty_label = tk.Label(
                    bid_frame,
                    text=f"{qty:,}",
                    font=('Segoe UI', 10),
                    bg=EXECUTIVE_COLORS['bg_card'],
                    fg=EXECUTIVE_COLORS['text_secondary'],
                    width=10,
                    anchor='e'
                )
                qty_label.pack(side=tk.RIGHT, padx=(5, 10))

                # Orders count
                orders_label = tk.Label(
                    bid_frame,
                    text=f"({orders})",
                    font=('Segoe UI', 9),
                    bg=EXECUTIVE_COLORS['bg_card'],
                    fg=EXECUTIVE_COLORS['text_muted'],
                    width=6,
                    anchor='center'
                )
                orders_label.pack(side=tk.RIGHT, padx=2)

    def set_data_source(self, source):
        """Set data source and update button states"""
        self.data_source = source

        # Update button states
        if source == 'live':
            self.live_btn.configure(bg=EXECUTIVE_COLORS['success'])
            self.sim_btn.configure(bg=EXECUTIVE_COLORS['bg_secondary'])
            self.disc_btn.configure(bg=EXECUTIVE_COLORS['bg_secondary'])
            self.connect_websocket()  # Connect to live data
        elif source == 'simulated':
            self.live_btn.configure(bg=EXECUTIVE_COLORS['bg_secondary'])
            self.sim_btn.configure(bg=EXECUTIVE_COLORS['info'])
            self.disc_btn.configure(bg=EXECUTIVE_COLORS['bg_secondary'])
            self.disconnect_websocket()  # Disconnect from live data
        else:  # disconnected
            self.live_btn.configure(bg=EXECUTIVE_COLORS['bg_secondary'])
            self.sim_btn.configure(bg=EXECUTIVE_COLORS['bg_secondary'])
            self.disc_btn.configure(bg=EXECUTIVE_COLORS['danger'])
            self.disconnect_websocket()  # Disconnect from live data

        print(f"✅ Data source set to: {source}")

    def connect_websocket(self):
        """Connect to Smart API WebSocket for live market data"""
        try:
            print("🔌 Connecting to Smart API WebSocket...")

            # Smart API WebSocket URL (you'll need to replace with actual URL and auth)
            ws_url = "wss://smartapisocket.angelone.in/smart-stream"

            # Create WebSocket connection
            self.ws = websocket.WebSocketApp(
                ws_url,
                on_open=self.on_ws_open,
                on_message=self.on_ws_message,
                on_error=self.on_ws_error,
                on_close=self.on_ws_close
            )

            # Start WebSocket in separate thread
            ws_thread = threading.Thread(target=self.ws.run_forever, kwargs={'sslopt': {"cert_reqs": ssl.CERT_NONE}})
            ws_thread.daemon = True
            ws_thread.start()

        except Exception as e:
            print(f"❌ WebSocket connection failed: {e}")
            self.ws_connected = False

    def disconnect_websocket(self):
        """Disconnect from WebSocket"""
        if self.ws:
            self.ws.close()
            self.ws = None
            self.ws_connected = False
            print("🔌 WebSocket disconnected")

    def on_ws_open(self, ws):
        """WebSocket connection opened"""
        print("✅ WebSocket connected successfully!")
        self.ws_connected = True

        # Subscribe to CGCL market depth
        subscribe_message = {
            "a": "subscribe",
            "v": [[1, "26000"]]  # CGCL token (you'll need the correct token)
        }
        ws.send(json.dumps(subscribe_message))
        print("📡 Subscribed to CGCL market depth")

    def on_ws_message(self, ws, message):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(message)

            # Parse market depth data (format depends on Smart API response)
            if 'tk' in data and data['tk'] == '26000':  # CGCL token
                # Extract bid/ask data from Smart API response
                # This is a placeholder - you'll need to adapt to actual Smart API format
                if 'bp1' in data and 'ap1' in data:
                    # Build order book from Smart API data
                    bids = []
                    asks = []

                    # Extract up to 5 levels of bid/ask data
                    for i in range(1, 6):
                        bp_key = f'bp{i}'
                        bq_key = f'bq{i}'
                        ap_key = f'ap{i}'
                        aq_key = f'aq{i}'

                        if bp_key in data and bq_key in data:
                            bids.append([
                                int(data[bq_key]),  # quantity
                                1,  # orders (not provided by Smart API)
                                float(data[bp_key])  # price
                            ])

                        if ap_key in data and aq_key in data:
                            asks.append([
                                float(data[ap_key]),  # price
                                1,  # orders (not provided by Smart API)
                                int(data[aq_key])  # quantity
                            ])

                    # Update live order book data
                    self.live_order_book_data = {'bids': bids, 'asks': asks}

        except Exception as e:
            print(f"❌ Error parsing WebSocket message: {e}")

    def on_ws_error(self, ws, error):
        """WebSocket error handler"""
        print(f"❌ WebSocket error: {error}")
        self.ws_connected = False

    def on_ws_close(self, ws, close_status_code, close_msg):
        """WebSocket connection closed"""
        print("🔌 WebSocket connection closed")
        self.ws_connected = False


    
    def start_data_updates(self):
        """Start background data updates"""
        self.running = True
        update_thread = threading.Thread(target=self.data_update_loop, daemon=True)
        update_thread.start()
    
    def data_update_loop(self):
        """Background data update loop"""
        while self.running:
            try:
                if self.data_source == 'simulated':
                    # Update simulator
                    self.simulator.update_order_book()
                    
                    # Get data
                    bids, asks = self.simulator.get_order_book_data()
                    signals = self.simulator.get_trading_signals()
                    analytics = self.simulator.get_market_analytics()
                    
                    # Update UI in main thread
                    def update_ui():
                        # Update HFT-style order book display
                        self.update_order_book_display(bids, asks)

                        # Update market status
                        if bids and asks:
                            current_price = (bids[0][2] + asks[0][0]) / 2
                            self.price_label.config(text=f"₹{current_price:.2f}")

                        # Update trading analysis and signals
                        self.trading_signals.update_signals(signals)
                        self.market_analytics.update_analytics(analytics)

                        # Update trading charts with new price data
                        if bids and asks:
                            # Update order book data for trading charts
                            self.trading_charts.update_order_book_data(bids, asks)

                            # Get current price from best bid/ask
                            best_bid = bids[0][2] if bids else 870.0  # price from (qty, orders, price)
                            best_ask = asks[0][0] if asks else 872.0  # price from (price, orders, qty)
                            current_price = (best_bid + best_ask) / 2

                            # Generate OHLC data (simplified for demo)
                            price_data = {
                                'open': current_price - 0.5,
                                'high': current_price + 0.8,
                                'low': current_price - 0.8,
                                'close': current_price
                            }
                            volume = sum([qty for qty, _, _ in bids[:5]]) + sum([qty for _, _, qty in asks[:5]])

                            self.trading_charts.add_new_data(price_data, volume)

                    self.root.after(0, update_ui)
                
                elif self.data_source == 'live':
                    # Use live WebSocket data
                    if self.ws_connected and self.live_order_book_data['bids']:
                        bids = self.live_order_book_data['bids']
                        asks = self.live_order_book_data['asks']

                        # Update UI with live data
                        def update_ui():
                            if hasattr(self, 'order_book_frame'):
                                self.update_order_book_display(bids, asks)

                            # Update analytics with live data
                            if self.market_analytics:
                                # Calculate current price from best bid/ask
                                current_price = (bids[0][2] + asks[0][0]) / 2 if bids and asks else 850.0
                                volume = sum([bid[0] for bid in bids]) + sum([ask[2] for ask in asks])

                                price_data = {
                                    'timestamp': time.time(),
                                    'price': current_price,
                                    'volume': volume,
                                    'bid': bids[0][2] if bids else 0,
                                    'ask': asks[0][0] if asks else 0
                                }

                                self.market_analytics.update_data(price_data)

                            # Update trading charts
                            if self.trading_charts:
                                current_price = (bids[0][2] + asks[0][0]) / 2 if bids and asks else 850.0
                                volume = sum([bid[0] for bid in bids]) + sum([ask[2] for ask in asks])

                                price_data = {
                                    'timestamp': time.time(),
                                    'open': current_price,
                                    'high': current_price,
                                    'low': current_price,
                                    'close': current_price,
                                    'volume': volume
                                }

                                self.trading_charts.add_new_data(price_data, volume)

                        self.root.after(0, update_ui)
                        print("📡 Live data updated from WebSocket")
                    else:
                        print("⏳ Waiting for WebSocket connection...")
                        time.sleep(1)
                
                elif self.data_source == 'disconnected':
                    # No updates when disconnected
                    time.sleep(1)
                
                # Wait for next update
                time.sleep(APP_CONFIG['update_interval'])
                
            except Exception as e:
                print(f"❌ Error in data update loop: {e}")
                time.sleep(1)
    
    def run(self):
        """Run the application"""
        try:
            print("🚀 Starting Ultimate Trading Analysis v2...")
            self.root.mainloop()
        except KeyboardInterrupt:
            print("👋 Application stopped by user")
        finally:
            self.running = False


if __name__ == "__main__":
    app = UltimateTrading()
    app.run()
