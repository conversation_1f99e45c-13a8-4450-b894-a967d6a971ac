#!/usr/bin/env python3
"""
Trading Signals Widget
"""

import tkinter as tk
from config import EXECUTIVE_COLORS


class TradingSignalsWidget(tk.Frame):
    """Trading signals display widget"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, bg=EXECUTIVE_COLORS['bg_card'], **kwargs)
        
        self.create_trading_signals()
    
    def create_trading_signals(self):
        """Create trading signals display"""
        # Title
        title_label = tk.Label(self, text="🔔 Trading Signals",
                              bg=EXECUTIVE_COLORS['bg_card'], 
                              fg=EXECUTIVE_COLORS['text_primary'],
                              font=('Arial', 12, 'bold'))
        title_label.pack(pady=(10, 5))
        
        # Signal container
        signal_frame = tk.Frame(self, bg=EXECUTIVE_COLORS['bg_secondary'])
        signal_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Buy signal
        buy_frame = tk.Frame(signal_frame, bg=EXECUTIVE_COLORS['success'], height=120)
        buy_frame.pack(fill=tk.X, padx=5, pady=5)
        buy_frame.pack_propagate(False)
        
        buy_label = tk.Label(buy_frame, text="BUY",
                           bg=EXECUTIVE_COLORS['success'], 
                           fg='white',
                           font=('Arial', 16, 'bold'))
        buy_label.pack(pady=5)
        
        # Entry details
        self.entry_label = tk.Label(buy_frame, text="Entry: ₹184.75",
                                   bg=EXECUTIVE_COLORS['success'], 
                                   fg='white',
                                   font=('Arial', 10))
        self.entry_label.pack()
        
        self.target_label = tk.Label(buy_frame, text="Target: ₹186.50",
                                    bg=EXECUTIVE_COLORS['success'], 
                                    fg='white',
                                    font=('Arial', 10))
        self.target_label.pack()
        
        self.stop_label = tk.Label(buy_frame, text="Stop: ₹181.50",
                                  bg=EXECUTIVE_COLORS['success'], 
                                  fg='white',
                                  font=('Arial', 10))
        self.stop_label.pack()
        
        # Signal strength
        strength_frame = tk.Frame(buy_frame, bg=EXECUTIVE_COLORS['success'])
        strength_frame.pack(pady=5)
        
        tk.Label(strength_frame, text="Signal Strength:",
                bg=EXECUTIVE_COLORS['success'], fg='white',
                font=('Arial', 9)).pack(side=tk.LEFT)
        
        self.strength_label = tk.Label(strength_frame, text="85%",
                                      bg=EXECUTIVE_COLORS['success'], 
                                      fg='white',
                                      font=('Arial', 9, 'bold'))
        self.strength_label.pack(side=tk.LEFT, padx=(5, 0))
    
    def update_signals(self, signal_data):
        """Update trading signals"""
        try:
            if signal_data:
                self.entry_label.config(text=f"Entry: ₹{signal_data.get('entry', 0):.2f}")
                self.target_label.config(text=f"Target: ₹{signal_data.get('target', 0):.2f}")
                self.stop_label.config(text=f"Stop: ₹{signal_data.get('stop', 0):.2f}")
                self.strength_label.config(text=f"{signal_data.get('strength', 0):.0f}%")
        except Exception as e:
            print(f"❌ Error updating signals: {e}")
