"""
Find CGCL Token for Smart API
=============================

Utility to find the correct CGCL token for Smart API subscription.
"""

import json
from smartapi import SmartConnect

def find_cgcl_token():
    """Find CGCL token using Smart API"""
    try:
        # Load credentials
        with open("smart_api_credentials.json", 'r') as f:
            credentials = json.load(f)
        
        # Initialize Smart API
        smart_api = SmartConnect(api_key=credentials['api_key'])
        
        # Login
        login_response = smart_api.generateSession(
            credentials['username'],
            credentials['password'],
            credentials['totp']
        )
        
        if login_response['status']:
            print("✅ Smart API login successful")
            
            # Search for CGCL
            search_results = smart_api.searchScrip("NSE", "CGCL")
            
            if search_results['status'] and search_results['data']:
                print("\n📊 CGCL Search Results:")
                print("=" * 50)
                
                for result in search_results['data']:
                    print(f"Symbol: {result.get('symbol', 'N/A')}")
                    print(f"Token: {result.get('symboltoken', 'N/A')}")
                    print(f"Exchange: {result.get('exch_seg', 'N/A')}")
                    print(f"Name: {result.get('name', 'N/A')}")
                    print(f"ISIN: {result.get('isin', 'N/A')}")
                    print("-" * 30)
                
                # Get the first CGCL token
                cgcl_token = search_results['data'][0].get('symboltoken')
                
                if cgcl_token:
                    print(f"\n🎯 CGCL Token Found: {cgcl_token}")
                    
                    # Save token to file
                    token_data = {
                        'symbol': 'CGCL',
                        'token': cgcl_token,
                        'exchange': search_results['data'][0].get('exch_seg', 'NSE'),
                        'name': search_results['data'][0].get('name', 'CGCL'),
                        'isin': search_results['data'][0].get('isin', '')
                    }
                    
                    with open('cgcl_token.json', 'w') as f:
                        json.dump(token_data, f, indent=2)
                    
                    print(f"💾 Token saved to cgcl_token.json")
                    
                    return cgcl_token
                else:
                    print("❌ No token found for CGCL")
                    return None
            else:
                print("❌ No search results for CGCL")
                return None
        else:
            print(f"❌ Smart API login failed: {login_response.get('message', 'Unknown error')}")
            return None
            
    except FileNotFoundError:
        print("❌ smart_api_credentials.json not found")
        print("Please run test_smart_api_auth.py first to create credentials")
        return None
    except Exception as e:
        print(f"❌ Error finding CGCL token: {e}")
        return None

def get_market_data_sample():
    """Get sample market data for CGCL"""
    try:
        # Load credentials
        with open("smart_api_credentials.json", 'r') as f:
            credentials = json.load(f)
        
        # Load CGCL token
        with open("cgcl_token.json", 'r') as f:
            token_data = json.load(f)
        
        # Initialize Smart API
        smart_api = SmartConnect(api_key=credentials['api_key'])
        
        # Login
        login_response = smart_api.generateSession(
            credentials['username'],
            credentials['password'],
            credentials['totp']
        )
        
        if login_response['status']:
            print("✅ Getting CGCL market data...")
            
            # Get LTP (Last Traded Price)
            ltp_data = smart_api.ltpData(
                exchange=token_data['exchange'],
                tradingsymbol=token_data['symbol'],
                symboltoken=token_data['token']
            )
            
            if ltp_data['status']:
                print(f"\n📊 CGCL Current Price: ₹{ltp_data['data']['ltp']}")
                print(f"Open: ₹{ltp_data['data']['open']}")
                print(f"High: ₹{ltp_data['data']['high']}")
                print(f"Low: ₹{ltp_data['data']['low']}")
                print(f"Close: ₹{ltp_data['data']['close']}")
            
            # Get market depth (if available)
            try:
                depth_data = smart_api.getMarketData(
                    mode="FULL",
                    exchangeTokens={
                        token_data['exchange']: [token_data['token']]
                    }
                )
                
                if depth_data['status'] and depth_data['data']:
                    market_data = depth_data['data'][0]
                    print(f"\n📈 Market Depth Available:")
                    print(f"Best Bid: ₹{market_data.get('bestbidprice', 'N/A')}")
                    print(f"Best Ask: ₹{market_data.get('bestaskprice', 'N/A')}")
                    print(f"Total Buy Qty: {market_data.get('totalbuyqty', 'N/A')}")
                    print(f"Total Sell Qty: {market_data.get('totalsellqty', 'N/A')}")
                
            except Exception as e:
                print(f"⚠️ Market depth not available: {e}")
            
            return True
        else:
            print(f"❌ Smart API login failed: {login_response.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error getting market data: {e}")
        return False

def main():
    """Main function"""
    print("🔍 CGCL TOKEN FINDER")
    print("=" * 30)
    print("This utility will:")
    print("1. Search for CGCL in Smart API")
    print("2. Find the correct token")
    print("3. Save token information")
    print("4. Test market data access")
    print("=" * 30)
    
    # Find CGCL token
    token = find_cgcl_token()
    
    if token:
        print(f"\n✅ CGCL Token: {token}")
        
        # Test market data
        print("\n🧪 Testing market data access...")
        if get_market_data_sample():
            print("\n✅ Market data access successful!")
            print("\nYou can now use this token in your market depth system:")
            print(f"Token: {token}")
            print("File: cgcl_token.json")
        else:
            print("\n⚠️ Market data access failed, but token is valid")
    else:
        print("\n❌ Could not find CGCL token")
        print("Please check:")
        print("1. Smart API credentials are correct")
        print("2. CGCL is available on your exchange")
        print("3. Your account has access to CGCL data")

if __name__ == "__main__":
    main()
