"""
Order Flow Engine Demo and Testing
==================================

This script demonstrates the capabilities of the order flow engine
with simulated market data and real-time analysis.
"""

import asyncio
import random
import time
from datetime import datetime, timedelta
from typing import List
import numpy as np

from order_flow_engine import OrderFlowEngine, AdvancedOrderFlowAnalyzer, Tick, OrderBook, OrderBookLevel
from order_flow_monitor import OrderFlowMonitor

class MarketDataSimulator:
    """
    Simulates realistic market data for testing the order flow engine
    """
    
    def __init__(self, symbol: str, base_price: float = 2500.0):
        self.symbol = symbol
        self.base_price = base_price
        self.current_price = base_price
        self.current_time = datetime.now()
        
        # Market microstructure parameters
        self.tick_size = 0.05
        self.min_volume = 100
        self.max_volume = 10000
        self.large_order_probability = 0.05  # 5% chance of large order
        self.institutional_volume_min = 50000
        self.institutional_volume_max = 500000
        
        # Price movement parameters
        self.volatility = 0.02  # 2% volatility
        self.trend_strength = 0.0  # No trend initially
        self.mean_reversion = 0.1
        
    def generate_tick(self) -> Tick:
        """Generate a realistic tick"""
        # Update time
        self.current_time += timedelta(milliseconds=random.randint(100, 2000))
        
        # Price movement with trend and mean reversion
        price_change = (
            random.gauss(0, self.volatility) +  # Random walk
            self.trend_strength * 0.001 +       # Trend component
            (self.base_price - self.current_price) * self.mean_reversion * 0.001  # Mean reversion
        )
        
        self.current_price += price_change
        self.current_price = round(self.current_price / self.tick_size) * self.tick_size
        
        # Generate volume
        if random.random() < self.large_order_probability:
            # Large institutional order
            volume = random.randint(self.institutional_volume_min, self.institutional_volume_max)
        else:
            # Regular retail order
            volume = random.randint(self.min_volume, self.max_volume)
        
        # Determine if buyer initiated (with some bias based on trend)
        buyer_probability = 0.5 + self.trend_strength * 0.3
        buyer_initiated = random.random() < buyer_probability
        
        return Tick(
            timestamp=self.current_time,
            price=self.current_price,
            volume=volume,
            buyer_initiated=buyer_initiated
        )
    
    def generate_order_book(self) -> OrderBook:
        """Generate a realistic order book"""
        bids = []
        asks = []
        
        # Generate 5 levels each side
        for i in range(5):
            # Bid levels (below current price)
            bid_price = self.current_price - (i + 1) * self.tick_size
            bid_qty = random.randint(1000, 50000)
            bids.append(OrderBookLevel(price=bid_price, quantity=bid_qty, orders=random.randint(1, 10)))
            
            # Ask levels (above current price)
            ask_price = self.current_price + (i + 1) * self.tick_size
            ask_qty = random.randint(1000, 50000)
            asks.append(OrderBookLevel(price=ask_price, quantity=ask_qty, orders=random.randint(1, 10)))
        
        return OrderBook(
            timestamp=self.current_time,
            bids=bids,
            asks=asks
        )
    
    def set_trend(self, trend_strength: float):
        """Set market trend (-1.0 to 1.0)"""
        self.trend_strength = trend_strength
    
    def create_volume_spike(self, multiplier: float = 5.0):
        """Create a volume spike for testing"""
        self.large_order_probability = min(0.5, self.large_order_probability * multiplier)
    
    def create_institutional_activity(self, duration_seconds: int = 60):
        """Simulate institutional activity"""
        self.institutional_volume_min *= 2
        self.institutional_volume_max *= 2
        self.large_order_probability = 0.3  # 30% chance of large orders


class OrderFlowDemo:
    """
    Comprehensive demo of the order flow engine capabilities
    """
    
    def __init__(self):
        self.symbols = ["RELIANCE", "TCS", "INFY"]
        self.simulators = {
            symbol: MarketDataSimulator(symbol, base_price=2500 + i*500) 
            for i, symbol in enumerate(self.symbols)
        }
        self.monitor = OrderFlowMonitor(self.symbols)
        
    async def run_basic_demo(self):
        """Run basic order flow analysis demo"""
        print("🚀 Starting Basic Order Flow Demo")
        print("="*50)
        
        # Create analyzer for single symbol
        analyzer = AdvancedOrderFlowAnalyzer("RELIANCE")
        simulator = self.simulators["RELIANCE"]
        
        print("📊 Generating market data and analyzing flow...")
        
        # Generate 100 ticks
        for i in range(100):
            tick = simulator.generate_tick()
            signal = analyzer.flow_engine.add_tick(tick)
            
            # Add order book data occasionally
            if i % 10 == 0:
                order_book = simulator.generate_order_book()
                analyzer.flow_engine.add_order_book(order_book)
            
            # Print signal if generated
            if signal:
                print(f"🚨 Signal {i}: {signal.signal_type} "
                      f"(Strength: {signal.strength:.2f}, "
                      f"Confidence: {signal.confidence:.2f})")
                print(f"   Reasons: {', '.join(signal.reasons)}")
            
            await asyncio.sleep(0.1)  # 100ms delay
        
        # Get final analysis
        analysis = analyzer.get_comprehensive_analysis()
        self._print_analysis_summary(analysis)
    
    async def run_pattern_detection_demo(self):
        """Demo advanced pattern detection"""
        print("\n🔍 Starting Pattern Detection Demo")
        print("="*50)
        
        analyzer = AdvancedOrderFlowAnalyzer("TCS")
        simulator = self.simulators["TCS"]
        
        # Create different market scenarios
        scenarios = [
            ("Normal Trading", lambda: None),
            ("Volume Spike", lambda: simulator.create_volume_spike(3.0)),
            ("Bullish Trend", lambda: simulator.set_trend(0.8)),
            ("Institutional Activity", lambda: simulator.create_institutional_activity(30)),
            ("Bearish Trend", lambda: simulator.set_trend(-0.8))
        ]
        
        for scenario_name, setup_func in scenarios:
            print(f"\n📈 Scenario: {scenario_name}")
            print("-" * 30)
            
            # Setup scenario
            setup_func()
            
            # Generate data for this scenario
            patterns_detected = []
            for i in range(50):
                tick = simulator.generate_tick()
                analyzer.flow_engine.add_tick(tick)
                
                if i % 5 == 0:
                    order_book = simulator.generate_order_book()
                    analyzer.flow_engine.add_order_book(order_book)
                
                # Check for patterns
                patterns = analyzer.detect_order_flow_patterns()
                for pattern in patterns:
                    if pattern not in patterns_detected:
                        patterns_detected.append(pattern)
                        print(f"  🔍 Pattern: {pattern['pattern']} - "
                              f"{pattern['direction']} (strength: {pattern['strength']:.2f})")
                
                await asyncio.sleep(0.05)
            
            if not patterns_detected:
                print("  No significant patterns detected")
    
    async def run_real_time_monitoring_demo(self):
        """Demo real-time monitoring with multiple symbols"""
        print("\n📡 Starting Real-Time Monitoring Demo")
        print("="*50)
        
        # Start monitoring in background
        monitor_task = asyncio.create_task(self.monitor.start_monitoring())
        
        # Generate data for all symbols
        data_task = asyncio.create_task(self._generate_multi_symbol_data())
        
        # Let it run for 30 seconds
        try:
            await asyncio.wait_for(asyncio.gather(monitor_task, data_task), timeout=30)
        except asyncio.TimeoutError:
            print("\n⏰ Demo timeout reached")
        
        # Stop monitoring
        self.monitor.stop_monitoring()
        
        # Print summary
        print(f"\n📊 Demo Summary:")
        print(f"Total signals generated: {self.monitor.total_signals_generated}")
        for symbol, count in self.monitor.signals_by_symbol.items():
            print(f"  {symbol}: {count} signals")
    
    async def _generate_multi_symbol_data(self):
        """Generate data for multiple symbols"""
        while self.monitor.is_running:
            for symbol in self.symbols:
                simulator = self.simulators[symbol]
                
                # Generate tick
                tick = simulator.generate_tick()
                self.monitor.add_tick_data(
                    symbol=symbol,
                    price=tick.price,
                    volume=tick.volume,
                    buyer_initiated=tick.buyer_initiated,
                    timestamp=tick.timestamp
                )
                
                # Occasionally add order book
                if random.random() < 0.1:  # 10% chance
                    order_book = simulator.generate_order_book()
                    bids = [(level.price, level.quantity) for level in order_book.bids]
                    asks = [(level.price, level.quantity) for level in order_book.asks]
                    self.monitor.add_order_book_data(symbol, bids, asks, order_book.timestamp)
            
            await asyncio.sleep(0.2)  # 200ms between data generation cycles
    
    def _print_analysis_summary(self, analysis: dict):
        """Print comprehensive analysis summary"""
        print("\n📊 Analysis Summary")
        print("="*40)
        
        # Basic metrics
        if analysis['basic_metrics']:
            metrics = analysis['basic_metrics']
            print(f"Symbol: {metrics.get('symbol', 'N/A')}")
            print(f"Current Price: ₹{metrics.get('current_price', 0):.2f}")
            print(f"VWAP: ₹{metrics.get('current_vwap', 0):.2f}")
            print(f"VWAP Deviation: {metrics.get('vwap_deviation', 0)*100:.2f}%")
            print(f"Cumulative Delta: {metrics.get('cumulative_delta', 0):,}")
            print(f"Ticks Processed: {metrics.get('total_ticks_processed', 0)}")
        
        # Current signal
        if analysis['current_signal']:
            signal = analysis['current_signal']
            print(f"\nCurrent Signal: {signal['signal_type']}")
            print(f"Strength: {signal['strength']:.2f}")
            print(f"Confidence: {signal['confidence']:.2f}")
            print(f"Reasons: {', '.join(signal['reasons'])}")
        
        # Detected patterns
        if analysis['detected_patterns']:
            print(f"\nDetected Patterns:")
            for pattern in analysis['detected_patterns']:
                print(f"  • {pattern['pattern']}: {pattern['direction']} "
                      f"(strength: {pattern['strength']:.2f})")
        else:
            print("\nNo patterns detected")
    
    async def run_performance_test(self):
        """Test performance with high-frequency data"""
        print("\n⚡ Starting Performance Test")
        print("="*40)
        
        analyzer = AdvancedOrderFlowAnalyzer("PERFORMANCE_TEST")
        simulator = MarketDataSimulator("PERFORMANCE_TEST")
        
        start_time = time.time()
        tick_count = 10000
        
        print(f"Processing {tick_count} ticks...")
        
        for i in range(tick_count):
            tick = simulator.generate_tick()
            analyzer.flow_engine.add_tick(tick)
            
            if i % 100 == 0:
                order_book = simulator.generate_order_book()
                analyzer.flow_engine.add_order_book(order_book)
        
        end_time = time.time()
        duration = end_time - start_time
        ticks_per_second = tick_count / duration
        
        print(f"✅ Performance Results:")
        print(f"  Processed {tick_count} ticks in {duration:.2f} seconds")
        print(f"  Rate: {ticks_per_second:.0f} ticks/second")
        print(f"  Memory usage: {len(analyzer.flow_engine.ticks)} ticks in memory")
        
        # Get final metrics
        metrics = analyzer.flow_engine.get_current_metrics()
        print(f"  Final VWAP: ₹{metrics.get('current_vwap', 0):.2f}")
        print(f"  Cumulative Delta: {metrics.get('cumulative_delta', 0):,}")


async def main():
    """Run all demos"""
    demo = OrderFlowDemo()
    
    print("🎯 ORDER FLOW ENGINE COMPREHENSIVE DEMO")
    print("="*60)
    
    try:
        # Run all demos
        await demo.run_basic_demo()
        await demo.run_pattern_detection_demo()
        await demo.run_real_time_monitoring_demo()
        await demo.run_performance_test()
        
        print("\n✅ All demos completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
