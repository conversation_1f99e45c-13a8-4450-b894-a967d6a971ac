"""
Complete Real-Time Order Flow System
===================================

Integrated system with:
- Every second tick updates
- Real-time order book GUI
- 5-minute order book history recording
- Live order flow analysis
- Smart API WebSocket integration
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import json
from datetime import datetime, timedelta
from collections import deque
import pandas as pd
import logging

from smart_api_realtime import SmartAPIRealTimeConnector
from order_flow_engine import AdvancedOrderFlowAnalyzer
from realtime_orderflow_gui import OrderBookGUI

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteRealTimeSystem:
    """Complete real-time order flow system with GUI and Smart API integration"""
    
    def __init__(self, symbols=None, credentials_file="smart_api_credentials.json"):
        if symbols is None:
            symbols = ["RELIANCE", "TCS", "INFY", "HDFCBANK"]
        
        self.symbols = symbols
        self.credentials_file = credentials_file
        
        # Order flow analyzers
        self.analyzers = {symbol: AdvancedOrderFlowAnalyzer(symbol) for symbol in symbols}
        
        # Smart API connector
        self.api_connector = None
        
        # GUI for order book
        self.order_book_gui = None
        
        # Data tracking
        self.tick_count = 0
        self.signal_count = 0
        self.order_book_count = 0
        self.start_time = None
        
        # System state
        self.is_running = False
        
        # Performance metrics
        self.performance_metrics = {
            'ticks_per_second': 0,
            'signals_per_hour': 0,
            'last_signal_time': None,
            'connection_uptime': 0
        }
    
    def initialize_system(self):
        """Initialize all system components"""
        logger.info("🚀 Initializing Complete Real-Time Order Flow System...")
        
        try:
            # Initialize Smart API connector
            self.api_connector = SmartAPIRealTimeConnector(self.credentials_file)
            self.api_connector.set_callbacks(
                on_tick=self.handle_tick,
                on_order_book=self.handle_order_book
            )
            
            # Initialize GUI
            self.order_book_gui = OrderBookGUI(self.symbols)
            
            logger.info("✅ System components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            return False
    
    def start_system(self):
        """Start the complete system"""
        if not self.initialize_system():
            return False
        
        logger.info("🚀 Starting Complete Real-Time System...")
        self.is_running = True
        self.start_time = time.time()
        
        try:
            # Start GUI in separate thread
            gui_thread = threading.Thread(target=self.run_gui, daemon=True)
            gui_thread.start()
            
            # Start performance monitoring
            perf_thread = threading.Thread(target=self.monitor_performance, daemon=True)
            perf_thread.start()
            
            # Connect to Smart API
            if self.api_connector.connect(self.symbols):
                logger.info("✅ Smart API connected successfully")
                
                # Start heartbeat
                self.api_connector.start_heartbeat()
                
                # Start main monitoring loop
                self.run_monitoring_loop()
                
            else:
                logger.error("❌ Smart API connection failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ System startup failed: {e}")
            return False
    
    def run_gui(self):
        """Run the GUI in separate thread"""
        try:
            self.order_book_gui.run()
        except Exception as e:
            logger.error(f"GUI error: {e}")
    
    def handle_tick(self, symbol, tick):
        """Handle incoming tick data"""
        try:
            self.tick_count += 1
            
            # Process through order flow engine
            if symbol in self.analyzers:
                signal = self.analyzers[symbol].flow_engine.add_tick(tick)
                
                if signal:
                    self.signal_count += 1
                    self.performance_metrics['last_signal_time'] = datetime.now()
                    self.handle_signal(symbol, signal, tick)
            
            # Log every 50th tick to avoid spam
            if self.tick_count % 50 == 0:
                logger.info(f"📊 Processed {self.tick_count} ticks, Generated {self.signal_count} signals")
                
        except Exception as e:
            logger.error(f"Tick handling error: {e}")
    
    def handle_order_book(self, symbol, order_book):
        """Handle incoming order book data"""
        try:
            self.order_book_count += 1
            
            # Update GUI
            if self.order_book_gui:
                self.order_book_gui.update_order_book(symbol, order_book)
            
            # Process through order flow engine
            if symbol in self.analyzers:
                self.analyzers[symbol].flow_engine.add_order_book(order_book)
            
            # Log every 20th order book
            if self.order_book_count % 20 == 0:
                logger.info(f"📚 Processed {self.order_book_count} order books")
                
        except Exception as e:
            logger.error(f"Order book handling error: {e}")
    
    def handle_signal(self, symbol, signal, tick):
        """Handle generated trading signal"""
        logger.info(f"🚨 SIGNAL #{self.signal_count}: {symbol} - {signal.signal_type} "
                   f"(Strength: {signal.strength:.2f}, Confidence: {signal.confidence:.2f})")
        
        # Print detailed signal to console
        print(f"\n{'='*60}")
        print(f"📊 ORDER FLOW SIGNAL - {symbol}")
        print(f"{'='*60}")
        print(f"Time: {tick.timestamp.strftime('%H:%M:%S.%f')[:-3]}")
        print(f"Signal: {signal.signal_type}")
        print(f"Strength: {signal.strength:.2f}")
        print(f"Confidence: {signal.confidence:.2f}")
        print(f"Price: ₹{tick.price:.2f}")
        print(f"Volume: {tick.volume:,}")
        print(f"Reasons: {', '.join(signal.reasons)}")
        
        # Get current metrics
        metrics = self.analyzers[symbol].flow_engine.get_current_metrics()
        print(f"\n📈 Current Flow Metrics:")
        print(f"  VWAP: ₹{metrics.get('current_vwap', 0):.2f}")
        print(f"  VWAP Deviation: {metrics.get('vwap_deviation', 0)*100:.2f}%")
        print(f"  Cumulative Delta: {metrics.get('cumulative_delta', 0):,}")
        print(f"  Recent Volume (5min): {metrics.get('recent_volume_5min', 0):,}")
        
        # Get advanced patterns
        patterns = self.analyzers[symbol].detect_order_flow_patterns()
        if patterns:
            print(f"\n🔍 Detected Patterns:")
            for pattern in patterns:
                print(f"  • {pattern['pattern']}: {pattern['direction']} "
                      f"(strength: {pattern['strength']:.2f})")
        
        print(f"{'='*60}\n")
        
        # Save signal to file
        self.save_signal_to_file(symbol, signal, tick, metrics, patterns)
    
    def save_signal_to_file(self, symbol, signal, tick, metrics, patterns):
        """Save signal data to file"""
        try:
            signal_data = {
                'timestamp': tick.timestamp.isoformat(),
                'symbol': symbol,
                'signal_type': signal.signal_type,
                'strength': signal.strength,
                'confidence': signal.confidence,
                'price': tick.price,
                'volume': tick.volume,
                'reasons': signal.reasons,
                'vwap': metrics.get('current_vwap', 0),
                'vwap_deviation': metrics.get('vwap_deviation', 0),
                'cumulative_delta': metrics.get('cumulative_delta', 0),
                'patterns': [p['pattern'] for p in patterns] if patterns else []
            }
            
            # Append to daily signals file
            filename = f"signals_{datetime.now().strftime('%Y%m%d')}.json"
            with open(filename, 'a') as f:
                f.write(json.dumps(signal_data) + '\n')
                
        except Exception as e:
            logger.error(f"Signal saving error: {e}")
    
    def monitor_performance(self):
        """Monitor system performance"""
        while self.is_running:
            try:
                time.sleep(60)  # Update every minute
                
                # Calculate performance metrics
                if self.start_time:
                    uptime = time.time() - self.start_time
                    self.performance_metrics['connection_uptime'] = uptime
                    self.performance_metrics['ticks_per_second'] = self.tick_count / uptime if uptime > 0 else 0
                    self.performance_metrics['signals_per_hour'] = (self.signal_count / uptime) * 3600 if uptime > 0 else 0
                
                # Log performance
                logger.info(f"📊 Performance: {self.performance_metrics['ticks_per_second']:.1f} ticks/sec, "
                           f"{self.performance_metrics['signals_per_hour']:.1f} signals/hour")
                
                # Get API status
                if self.api_connector:
                    api_status = self.api_connector.get_status()
                    logger.info(f"📡 API Status: Connected: {api_status['connected']}, "
                               f"Uptime: {api_status['uptime_seconds']:.0f}s")
                
            except Exception as e:
                logger.error(f"Performance monitoring error: {e}")
    
    def run_monitoring_loop(self):
        """Main monitoring loop"""
        logger.info("📡 Starting main monitoring loop...")
        
        try:
            while self.is_running:
                # Check connection status
                if self.api_connector and not self.api_connector.is_connected:
                    logger.warning("⚠️ API connection lost, attempting reconnection...")
                    self.api_connector.connect(self.symbols)
                
                # Sleep for a short time
                time.sleep(5)
                
        except KeyboardInterrupt:
            logger.info("⏹️ System stopped by user")
        except Exception as e:
            logger.error(f"Monitoring loop error: {e}")
        finally:
            self.stop_system()
    
    def stop_system(self):
        """Stop the system gracefully"""
        logger.info("🛑 Stopping system...")
        self.is_running = False
        
        # Disconnect API
        if self.api_connector:
            self.api_connector.disconnect()
        
        # Export order book history
        if self.order_book_gui:
            for symbol in self.symbols:
                if len(self.order_book_gui.order_book_history[symbol]) > 0:
                    self.order_book_gui.export_order_book_history(symbol)
        
        # Generate final report
        self.generate_session_report()
        
        logger.info("✅ System stopped successfully")
    
    def generate_session_report(self):
        """Generate session performance report"""
        try:
            if not self.start_time:
                return
            
            session_duration = time.time() - self.start_time
            
            report = {
                'session_start': datetime.fromtimestamp(self.start_time).isoformat(),
                'session_end': datetime.now().isoformat(),
                'duration_seconds': session_duration,
                'total_ticks': self.tick_count,
                'total_signals': self.signal_count,
                'total_order_books': self.order_book_count,
                'performance_metrics': self.performance_metrics,
                'symbols_monitored': self.symbols
            }
            
            # Save report
            filename = f"session_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"📊 Session report saved to {filename}")
            
            # Print summary
            print(f"\n{'='*60}")
            print(f"📊 SESSION SUMMARY")
            print(f"{'='*60}")
            print(f"Duration: {session_duration/60:.1f} minutes")
            print(f"Total Ticks: {self.tick_count:,}")
            print(f"Total Signals: {self.signal_count}")
            print(f"Total Order Books: {self.order_book_count:,}")
            print(f"Avg Ticks/Second: {self.performance_metrics['ticks_per_second']:.1f}")
            print(f"Avg Signals/Hour: {self.performance_metrics['signals_per_hour']:.1f}")
            print(f"{'='*60}")
            
        except Exception as e:
            logger.error(f"Report generation error: {e}")


def main():
    """Main function"""
    print("🚀 COMPLETE REAL-TIME ORDER FLOW SYSTEM")
    print("=" * 60)
    print("Features:")
    print("✅ Every second tick updates from Smart API")
    print("✅ Real-time order book GUI with 5-min history")
    print("✅ Live order flow analysis and signal generation")
    print("✅ Performance monitoring and reporting")
    print("✅ Automatic data export and session reports")
    print("=" * 60)
    
    # Symbols to monitor
    symbols = ["RELIANCE", "TCS", "INFY", "HDFCBANK"]
    
    try:
        # Create and start system
        system = CompleteRealTimeSystem(symbols)
        
        print(f"\n📊 Monitoring symbols: {', '.join(symbols)}")
        print("🔄 Starting system... (Press Ctrl+C to stop)")
        
        # Start the system
        system.start_system()
        
    except KeyboardInterrupt:
        print("\n⏹️ System stopped by user")
    except Exception as e:
        print(f"\n❌ System failed: {e}")
        logger.error(f"System failure: {e}")


if __name__ == "__main__":
    main()
