"""
Smart API Order Flow Integration
================================

This module integrates Smart API with the order flow engine to provide
real-time market data for order flow analysis.
"""

import asyncio
import json
import websocket
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Callable, Tuple
import logging

from order_flow_monitor import OrderFlowMonitor
from order_flow_engine import Tick, OrderBook, OrderBookLevel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SmartAPIOrderFlowConnector:
    """
    Smart API connector optimized for order flow analysis
    
    Features:
    - Real-time tick data streaming
    - Order book depth data
    - WebSocket connection management
    - Automatic reconnection
    - Data validation and filtering
    """
    
    def __init__(self, api_key: str, access_token: str, symbols: List[str]):
        self.api_key = api_key
        self.access_token = access_token
        self.symbols = symbols
        
        # WebSocket connection
        self.ws = None
        self.is_connected = False
        self.connection_thread = None
        
        # Order flow monitor
        self.order_flow_monitor = OrderFlowMonitor(symbols)
        
        # Data processing
        self.tick_count = 0
        self.last_heartbeat = time.time()
        
        # Callbacks
        self.on_tick_callback: Optional[Callable] = None
        self.on_order_book_callback: Optional[Callable] = None
        self.on_signal_callback: Optional[Callable] = None
        
        # Symbol mapping (Smart API token to symbol)
        self.token_symbol_map = {}
        
    def set_callbacks(self, on_tick: Optional[Callable] = None,
                     on_order_book: Optional[Callable] = None,
                     on_signal: Optional[Callable] = None):
        """Set callback functions for different data types"""
        self.on_tick_callback = on_tick
        self.on_order_book_callback = on_order_book
        self.on_signal_callback = on_signal
    
    def connect(self):
        """Connect to Smart API WebSocket"""
        try:
            # WebSocket URL for Smart API
            ws_url = "wss://smartapisocket.angelone.in/smart-stream"
            
            self.ws = websocket.WebSocketApp(
                ws_url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )
            
            # Start connection in separate thread
            self.connection_thread = threading.Thread(target=self.ws.run_forever)
            self.connection_thread.daemon = True
            self.connection_thread.start()
            
            logger.info("Connecting to Smart API WebSocket...")
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
    
    def _on_open(self, ws):
        """WebSocket connection opened"""
        logger.info("✅ Connected to Smart API WebSocket")
        self.is_connected = True
        self.last_heartbeat = time.time()
        
        # Send authentication
        auth_message = {
            "a": "auth",
            "user": self.api_key,
            "token": self.access_token
        }
        ws.send(json.dumps(auth_message))
        
        # Subscribe to symbols
        self._subscribe_symbols()
    
    def _on_message(self, ws, message):
        """Process incoming WebSocket message"""
        try:
            # Update heartbeat
            self.last_heartbeat = time.time()
            
            # Parse message
            if isinstance(message, bytes):
                # Binary data (tick data)
                self._process_binary_data(message)
            else:
                # JSON data (order book, etc.)
                data = json.loads(message)
                self._process_json_data(data)
                
        except Exception as e:
            logger.error(f"Message processing error: {e}")
    
    def _on_error(self, ws, error):
        """WebSocket error handler"""
        logger.error(f"WebSocket error: {error}")
    
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket connection closed"""
        logger.warning("WebSocket connection closed")
        self.is_connected = False
        
        # Attempt reconnection after delay
        time.sleep(5)
        self.connect()
    
    def _subscribe_symbols(self):
        """Subscribe to symbol data"""
        # This would use actual Smart API subscription format
        # For now, we'll simulate the subscription
        
        for symbol in self.symbols:
            # Subscribe to tick data
            tick_subscription = {
                "a": "subscribe",
                "v": [[1, symbol]]  # Mode 1 = tick data
            }
            self.ws.send(json.dumps(tick_subscription))
            
            # Subscribe to order book data
            depth_subscription = {
                "a": "subscribe", 
                "v": [[2, symbol]]  # Mode 2 = order book depth
            }
            self.ws.send(json.dumps(depth_subscription))
            
        logger.info(f"Subscribed to {len(self.symbols)} symbols")
    
    def _process_binary_data(self, data: bytes):
        """Process binary tick data"""
        try:
            # Parse binary tick data (Smart API specific format)
            # This is a simplified example - actual parsing would be more complex
            
            if len(data) < 20:  # Minimum tick data size
                return
                
            # Extract tick information (example format)
            # Actual Smart API binary format would be different
            token = int.from_bytes(data[0:4], 'big')
            price = int.from_bytes(data[4:8], 'big') / 100  # Price in paisa
            volume = int.from_bytes(data[8:12], 'big')
            timestamp_ms = int.from_bytes(data[12:20], 'big')
            
            # Convert to datetime
            timestamp = datetime.fromtimestamp(timestamp_ms / 1000)
            
            # Get symbol from token
            symbol = self.token_symbol_map.get(token, f"TOKEN_{token}")
            
            # Determine if buyer initiated (simplified logic)
            # In reality, this would come from the data or be calculated
            buyer_initiated = True  # Placeholder
            
            # Create tick object
            tick = Tick(
                timestamp=timestamp,
                price=price,
                volume=volume,
                buyer_initiated=buyer_initiated
            )
            
            # Process tick
            self._process_tick(symbol, tick)
            
        except Exception as e:
            logger.error(f"Binary data processing error: {e}")
    
    def _process_json_data(self, data: Dict):
        """Process JSON data (order book, etc.)"""
        try:
            message_type = data.get('t', '')
            
            if message_type == 'tk':  # Tick data
                self._process_json_tick(data)
            elif message_type == 'dp':  # Depth/Order book data
                self._process_order_book(data)
            elif message_type == 'ck':  # Connection acknowledgment
                logger.info("Connection acknowledged")
            else:
                logger.debug(f"Unknown message type: {message_type}")
                
        except Exception as e:
            logger.error(f"JSON data processing error: {e}")
    
    def _process_json_tick(self, data: Dict):
        """Process JSON tick data"""
        try:
            symbol = data.get('tk', '')
            price = float(data.get('lp', 0))  # Last price
            volume = int(data.get('v', 0))   # Volume
            timestamp = datetime.now()  # Use current time or parse from data
            
            # Determine buyer initiated (simplified)
            buyer_initiated = True  # Would need actual logic
            
            tick = Tick(
                timestamp=timestamp,
                price=price,
                volume=volume,
                buyer_initiated=buyer_initiated
            )
            
            self._process_tick(symbol, tick)
            
        except Exception as e:
            logger.error(f"JSON tick processing error: {e}")
    
    def _process_order_book(self, data: Dict):
        """Process order book data"""
        try:
            symbol = data.get('tk', '')
            
            # Extract bid and ask data
            bids = []
            asks = []
            
            # Parse bid levels (example format)
            for i in range(5):  # Top 5 levels
                bid_price = data.get(f'bp{i+1}', 0)
                bid_qty = data.get(f'bq{i+1}', 0)
                if bid_price > 0:
                    bids.append(OrderBookLevel(
                        price=float(bid_price),
                        quantity=int(bid_qty),
                        orders=1  # Smart API might not provide order count
                    ))
                
                ask_price = data.get(f'sp{i+1}', 0)
                ask_qty = data.get(f'sq{i+1}', 0)
                if ask_price > 0:
                    asks.append(OrderBookLevel(
                        price=float(ask_price),
                        quantity=int(ask_qty),
                        orders=1
                    ))
            
            # Sort levels
            bids.sort(key=lambda x: x.price, reverse=True)  # Highest first
            asks.sort(key=lambda x: x.price)  # Lowest first
            
            order_book = OrderBook(
                timestamp=datetime.now(),
                bids=bids,
                asks=asks
            )
            
            self._process_order_book_data(symbol, order_book)
            
        except Exception as e:
            logger.error(f"Order book processing error: {e}")
    
    def _process_tick(self, symbol: str, tick: Tick):
        """Process tick data through order flow engine"""
        self.tick_count += 1
        
        # Add to order flow monitor
        signal = self.order_flow_monitor.add_tick_data(
            symbol=symbol,
            price=tick.price,
            volume=tick.volume,
            buyer_initiated=tick.buyer_initiated,
            timestamp=tick.timestamp
        )
        
        # Call tick callback
        if self.on_tick_callback:
            self.on_tick_callback(symbol, tick)
        
        # Handle signal if generated
        if signal and self.on_signal_callback:
            self.on_signal_callback(symbol, signal)
        
        # Log periodic stats
        if self.tick_count % 1000 == 0:
            logger.info(f"Processed {self.tick_count} ticks")
    
    def _process_order_book_data(self, symbol: str, order_book: OrderBook):
        """Process order book data through order flow engine"""
        # Add to order flow monitor
        bids_tuples = [(level.price, level.quantity) for level in order_book.bids]
        asks_tuples = [(level.price, level.quantity) for level in order_book.asks]
        
        self.order_flow_monitor.add_order_book_data(
            symbol=symbol,
            bids=bids_tuples,
            asks=asks_tuples,
            timestamp=order_book.timestamp
        )
        
        # Call order book callback
        if self.on_order_book_callback:
            self.on_order_book_callback(symbol, order_book)
    
    async def start_monitoring(self):
        """Start order flow monitoring"""
        # Connect to Smart API
        self.connect()
        
        # Wait for connection
        while not self.is_connected:
            await asyncio.sleep(1)
        
        # Start order flow monitoring
        await self.order_flow_monitor.start_monitoring()
    
    def disconnect(self):
        """Disconnect from Smart API"""
        if self.ws:
            self.ws.close()
        self.is_connected = False
        logger.info("Disconnected from Smart API")
    
    def get_connection_status(self) -> Dict:
        """Get connection status and statistics"""
        return {
            'connected': self.is_connected,
            'ticks_processed': self.tick_count,
            'last_heartbeat': self.last_heartbeat,
            'symbols_monitored': len(self.symbols),
            'uptime_seconds': time.time() - self.last_heartbeat if self.is_connected else 0
        }


# Example usage
async def main():
    """Example usage of Smart API Order Flow Connector"""
    
    # Configuration
    API_KEY = "your_api_key"
    ACCESS_TOKEN = "your_access_token"
    SYMBOLS = ["RELIANCE", "TCS", "INFY", "HDFCBANK"]
    
    # Create connector
    connector = SmartAPIOrderFlowConnector(API_KEY, ACCESS_TOKEN, SYMBOLS)
    
    # Set up callbacks
    def on_tick(symbol: str, tick: Tick):
        print(f"📊 {symbol}: ₹{tick.price} (Vol: {tick.volume})")
    
    def on_signal(symbol: str, signal):
        print(f"🚨 SIGNAL: {symbol} - {signal.signal_type} "
              f"(Strength: {signal.strength:.2f})")
    
    connector.set_callbacks(on_tick=on_tick, on_signal=on_signal)
    
    try:
        # Start monitoring
        await connector.start_monitoring()
    except KeyboardInterrupt:
        print("Stopping...")
    finally:
        connector.disconnect()

if __name__ == "__main__":
    # asyncio.run(main())
