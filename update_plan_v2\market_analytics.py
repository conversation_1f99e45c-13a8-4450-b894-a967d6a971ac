#!/usr/bin/env python3
"""
Market Analytics Widget
"""

import tkinter as tk
from config import EXECUTIVE_COLORS


class MarketAnalyticsWidget(tk.Frame):
    """Market analytics display widget"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, bg=EXECUTIVE_COLORS['bg_card'], **kwargs)
        
        self.create_market_analytics()
    
    def create_market_analytics(self):
        """Create market analytics display"""
        # Title
        title_label = tk.Label(self, text="📊 Market Analytics",
                              bg=EXECUTIVE_COLORS['bg_card'], 
                              fg=EXECUTIVE_COLORS['text_primary'],
                              font=('Arial', 12, 'bold'))
        title_label.pack(pady=(10, 5))
        
        # Analytics container
        analytics_frame = tk.Frame(self, bg=EXECUTIVE_COLORS['bg_secondary'])
        analytics_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Volume section
        volume_frame = tk.Frame(analytics_frame, bg=EXECUTIVE_COLORS['bg_secondary'])
        volume_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(volume_frame, text="Volume:",
                bg=EXECUTIVE_COLORS['bg_secondary'], 
                fg=EXECUTIVE_COLORS['text_secondary'],
                font=('Arial', 10)).pack(side=tk.LEFT)
        
        self.volume_label = tk.Label(volume_frame, text="1.2M",
                                    bg=EXECUTIVE_COLORS['bg_secondary'], 
                                    fg=EXECUTIVE_COLORS['accent_blue'],
                                    font=('Arial', 10, 'bold'))
        self.volume_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Spread section
        spread_frame = tk.Frame(analytics_frame, bg=EXECUTIVE_COLORS['bg_secondary'])
        spread_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(spread_frame, text="Spread:",
                bg=EXECUTIVE_COLORS['bg_secondary'], 
                fg=EXECUTIVE_COLORS['text_secondary'],
                font=('Arial', 10)).pack(side=tk.LEFT)
        
        self.spread_label = tk.Label(spread_frame, text="₹0.05",
                                    bg=EXECUTIVE_COLORS['bg_secondary'], 
                                    fg=EXECUTIVE_COLORS['warning'],
                                    font=('Arial', 10, 'bold'))
        self.spread_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Volatility section
        volatility_frame = tk.Frame(analytics_frame, bg=EXECUTIVE_COLORS['bg_secondary'])
        volatility_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(volatility_frame, text="Volatility:",
                bg=EXECUTIVE_COLORS['bg_secondary'], 
                fg=EXECUTIVE_COLORS['text_secondary'],
                font=('Arial', 10)).pack(side=tk.LEFT)
        
        self.volatility_label = tk.Label(volatility_frame, text="Low",
                                        bg=EXECUTIVE_COLORS['bg_secondary'], 
                                        fg=EXECUTIVE_COLORS['success'],
                                        font=('Arial', 10, 'bold'))
        self.volatility_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Trend section
        trend_frame = tk.Frame(analytics_frame, bg=EXECUTIVE_COLORS['bg_secondary'])
        trend_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(trend_frame, text="Trend:",
                bg=EXECUTIVE_COLORS['bg_secondary'], 
                fg=EXECUTIVE_COLORS['text_secondary'],
                font=('Arial', 10)).pack(side=tk.LEFT)
        
        self.trend_label = tk.Label(trend_frame, text="Bullish",
                                   bg=EXECUTIVE_COLORS['bg_secondary'], 
                                   fg=EXECUTIVE_COLORS['success'],
                                   font=('Arial', 10, 'bold'))
        self.trend_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # RSI section
        rsi_frame = tk.Frame(analytics_frame, bg=EXECUTIVE_COLORS['bg_secondary'])
        rsi_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(rsi_frame, text="RSI:",
                bg=EXECUTIVE_COLORS['bg_secondary'], 
                fg=EXECUTIVE_COLORS['text_secondary'],
                font=('Arial', 10)).pack(side=tk.LEFT)
        
        self.rsi_label = tk.Label(rsi_frame, text="65.2",
                                 bg=EXECUTIVE_COLORS['bg_secondary'], 
                                 fg=EXECUTIVE_COLORS['accent_blue'],
                                 font=('Arial', 10, 'bold'))
        self.rsi_label.pack(side=tk.LEFT, padx=(10, 0))
    
    def update_analytics(self, analytics_data):
        """Update market analytics"""
        try:
            if analytics_data:
                self.volume_label.config(text=analytics_data.get('volume', '1.2M'))
                self.spread_label.config(text=f"₹{analytics_data.get('spread', 0.05):.2f}")
                self.volatility_label.config(text=analytics_data.get('volatility', 'Low'))
                self.trend_label.config(text=analytics_data.get('trend', 'Bullish'))
                self.rsi_label.config(text=f"{analytics_data.get('rsi', 65.2):.1f}")
        except Exception as e:
            print(f"❌ Error updating analytics: {e}")
