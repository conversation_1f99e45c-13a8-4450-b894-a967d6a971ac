"""
Professional CGCL Trading Dashboard - Executive Level Interface
Sophisticated dark-themed interface for management presentation
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np
from datetime import datetime, timedelta
import threading
import time
from typing import Dict, List, Optional

# Professional color scheme for executive dashboards
PROFESSIONAL_COLORS = {
    'bg_primary': '#0a0a0a',           # Deep black background
    'bg_secondary': '#1a1a1a',        # Secondary panels
    'bg_tertiary': '#2a2a2a',         # Card backgrounds
    'bg_accent': '#3a3a3a',           # Accent panels
    'text_primary': '#ffffff',         # Primary text
    'text_secondary': '#b0b0b0',       # Secondary text
    'text_muted': '#808080',           # Muted text
    'accent_blue': '#00d4ff',          # Professional blue
    'accent_green': '#00ff88',         # Success green
    'accent_red': '#ff4757',           # Alert red
    'accent_orange': '#ffa726',        # Warning orange
    'accent_purple': '#9c27b0',        # Analytics purple
    'border_light': '#404040',         # Light borders
    'border_dark': '#2a2a2a',          # Dark borders
    'gradient_start': '#1a1a1a',       # Gradient start
    'gradient_end': '#0a0a0a',         # Gradient end
}

class ProfessionalCGCLDashboard:
    """Professional CGCL Trading Dashboard for Executive Presentation"""
    
    def __init__(self):
        self.root = None
        self.is_running = False
        self.market_data = {}
        self.analytics_data = {}
        self.charts = {}
        
        # Initialize GUI
        self.initialize_professional_gui()
        self.create_executive_interface()
        self.setup_real_time_updates()
        
    def initialize_professional_gui(self):
        """Initialize professional GUI with executive styling"""
        try:
            print("🎨 Initializing Professional Executive Dashboard...")
            
            self.root = tk.Tk()
            self.root.title("CGCL Advanced Trading Analytics - Executive Dashboard")
            self.root.geometry("1600x1000")
            self.root.configure(bg=PROFESSIONAL_COLORS['bg_primary'])
            self.root.state('zoomed')  # Maximize window
            
            # Configure professional styling
            style = ttk.Style()
            style.theme_use('clam')
            
            # Configure professional styles
            style.configure('Professional.TFrame', 
                          background=PROFESSIONAL_COLORS['bg_secondary'],
                          relief='flat',
                          borderwidth=1)
            
            style.configure('Card.TFrame',
                          background=PROFESSIONAL_COLORS['bg_tertiary'],
                          relief='raised',
                          borderwidth=2)
            
            style.configure('Executive.TLabel',
                          background=PROFESSIONAL_COLORS['bg_secondary'],
                          foreground=PROFESSIONAL_COLORS['text_primary'],
                          font=('Segoe UI', 12))
            
            style.configure('Title.TLabel',
                          background=PROFESSIONAL_COLORS['bg_secondary'],
                          foreground=PROFESSIONAL_COLORS['accent_blue'],
                          font=('Segoe UI', 16, 'bold'))
            
            style.configure('Metric.TLabel',
                          background=PROFESSIONAL_COLORS['bg_tertiary'],
                          foreground=PROFESSIONAL_COLORS['text_primary'],
                          font=('Segoe UI', 14, 'bold'))
            
            print("✅ Professional GUI initialized successfully")
            
        except Exception as e:
            print(f"❌ Error initializing professional GUI: {e}")
            raise
    
    def create_executive_interface(self):
        """Create sophisticated executive-level interface"""
        try:
            # Main container with professional padding
            main_container = tk.Frame(self.root, bg=PROFESSIONAL_COLORS['bg_primary'])
            main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
            
            # Create header section
            self.create_executive_header(main_container)
            
            # Create main dashboard grid
            self.create_dashboard_grid(main_container)
            
            # Create footer with controls
            self.create_professional_footer(main_container)
            
            print("✅ Executive interface created successfully")
            
        except Exception as e:
            print(f"❌ Error creating executive interface: {e}")
            raise
    
    def create_executive_header(self, parent):
        """Create professional header with key metrics"""
        header_frame = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_secondary'], height=120)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # Add subtle border
        border_frame = tk.Frame(header_frame, bg=PROFESSIONAL_COLORS['border_light'], height=2)
        border_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Title section
        title_section = tk.Frame(header_frame, bg=PROFESSIONAL_COLORS['bg_secondary'])
        title_section.pack(side=tk.LEFT, fill=tk.Y, padx=30, pady=20)
        
        title_label = tk.Label(title_section,
                              text="CGCL TRADING ANALYTICS",
                              bg=PROFESSIONAL_COLORS['bg_secondary'],
                              fg=PROFESSIONAL_COLORS['accent_blue'],
                              font=('Segoe UI', 24, 'bold'))
        title_label.pack(anchor=tk.W)
        
        subtitle_label = tk.Label(title_section,
                                 text="Executive Dashboard • Real-time Market Intelligence",
                                 bg=PROFESSIONAL_COLORS['bg_secondary'],
                                 fg=PROFESSIONAL_COLORS['text_secondary'],
                                 font=('Segoe UI', 12))
        subtitle_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Key metrics section
        metrics_section = tk.Frame(header_frame, bg=PROFESSIONAL_COLORS['bg_secondary'])
        metrics_section.pack(side=tk.RIGHT, fill=tk.Y, padx=30, pady=20)
        
        self.create_key_metrics(metrics_section)
        
        # Live status indicator
        status_section = tk.Frame(header_frame, bg=PROFESSIONAL_COLORS['bg_secondary'])
        status_section.pack(side=tk.RIGHT, fill=tk.Y, padx=20, pady=20)
        
        self.create_live_status(status_section)
    
    def create_key_metrics(self, parent):
        """Create key performance metrics display"""
        metrics_grid = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_secondary'])
        metrics_grid.pack(fill=tk.BOTH, expand=True)
        
        # Current Price
        price_frame = self.create_metric_card(metrics_grid, "CURRENT PRICE", "₹184.75", 
                                            PROFESSIONAL_COLORS['accent_green'], 0, 0)
        
        # Change
        change_frame = self.create_metric_card(metrics_grid, "24H CHANGE", "+2.45%", 
                                             PROFESSIONAL_COLORS['accent_green'], 0, 1)
        
        # Volume
        volume_frame = self.create_metric_card(metrics_grid, "VOLUME", "1.2M", 
                                             PROFESSIONAL_COLORS['accent_blue'], 1, 0)
        
        # Market Cap
        mcap_frame = self.create_metric_card(metrics_grid, "MARKET CAP", "₹2.1B", 
                                           PROFESSIONAL_COLORS['accent_purple'], 1, 1)
    
    def create_metric_card(self, parent, title, value, color, row, col):
        """Create individual metric card"""
        card_frame = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_tertiary'], 
                             relief=tk.RAISED, bd=1)
        card_frame.grid(row=row, column=col, padx=10, pady=5, sticky='nsew', ipadx=15, ipady=10)
        
        title_label = tk.Label(card_frame, text=title,
                              bg=PROFESSIONAL_COLORS['bg_tertiary'],
                              fg=PROFESSIONAL_COLORS['text_secondary'],
                              font=('Segoe UI', 9, 'bold'))
        title_label.pack()
        
        value_label = tk.Label(card_frame, text=value,
                              bg=PROFESSIONAL_COLORS['bg_tertiary'],
                              fg=color,
                              font=('Segoe UI', 16, 'bold'))
        value_label.pack(pady=(5, 0))
        
        return card_frame
    
    def create_live_status(self, parent):
        """Create live connection status indicator"""
        status_frame = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_tertiary'],
                               relief=tk.RAISED, bd=1)
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Live indicator
        live_label = tk.Label(status_frame, text="🔴 LIVE",
                             bg=PROFESSIONAL_COLORS['bg_tertiary'],
                             fg=PROFESSIONAL_COLORS['accent_red'],
                             font=('Segoe UI', 12, 'bold'))
        live_label.pack(pady=(10, 5))
        
        # Connection status
        conn_label = tk.Label(status_frame, text="WebSocket Connected",
                             bg=PROFESSIONAL_COLORS['bg_tertiary'],
                             fg=PROFESSIONAL_COLORS['text_secondary'],
                             font=('Segoe UI', 9))
        conn_label.pack()
        
        # Last update
        self.last_update_label = tk.Label(status_frame, text="Updated: --:--:--",
                                         bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                         fg=PROFESSIONAL_COLORS['text_muted'],
                                         font=('Segoe UI', 8))
        self.last_update_label.pack(pady=(5, 10))

    def create_dashboard_grid(self, parent):
        """Create dashboard with horizontal signals header and full-width scrollable content"""
        # Main container with vertical layout
        main_container = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_primary'])
        main_container.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Configure main layout: signals header + scrollable content
        main_container.grid_rowconfigure(0, weight=0)  # Static signals header (fixed height)
        main_container.grid_rowconfigure(1, weight=1)  # Scrollable content (expandable)
        main_container.grid_columnconfigure(0, weight=1)

        # Create static signals header
        self.create_static_signals_header(main_container)

        # Create scrollable content area (full width)
        self.create_scrollable_content_area(main_container)

    def create_scrollable_content_area(self, parent):
        """Create scrollable area for charts and analytics (full width)"""
        # Scrollable frame container (full width)
        scrollable_container = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_primary'])
        scrollable_container.grid(row=1, column=0, sticky='nsew')

        # Create canvas and scrollbar
        canvas = tk.Canvas(scrollable_container, bg=PROFESSIONAL_COLORS['bg_primary'],
                          highlightthickness=0)
        scrollbar = ttk.Scrollbar(scrollable_container, orient="vertical", command=canvas.yview)

        # Scrollable frame
        self.scrollable_frame = tk.Frame(canvas, bg=PROFESSIONAL_COLORS['bg_primary'])

        # Configure scrolling
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # Create content in scrollable frame
        self.create_scrollable_content(self.scrollable_frame)

    def create_scrollable_content(self, parent):
        """Create all charts and analytics in scrollable area (full width)"""
        # Configure grid for scrollable content (now with more space for 4 charts)
        parent.grid_columnconfigure(0, weight=1)
        parent.grid_columnconfigure(1, weight=1)
        parent.grid_columnconfigure(2, weight=1)
        parent.grid_columnconfigure(3, weight=1)

        # Row 1: Order Book + 4 Order Flow Analysis charts (side by side)
        self.create_professional_order_book_panel_scrollable(parent, row=0, col=0)
        self.create_large_order_flow_analysis_panel_scrollable(parent, row=0, col=1, colspan=3)

        # Row 2: Market Analytics (full width)
        self.create_market_analytics_panel_scrollable(parent, row=1, col=0, colspan=4)

        # Add some padding at the bottom
        padding_frame = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_primary'], height=50)
        padding_frame.grid(row=2, column=0, columnspan=4, sticky='ew', pady=20)

    def create_static_signals_header(self, parent):
        """Create horizontal signals header that doesn't scroll"""
        signals_frame = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_secondary'],
                                relief=tk.RAISED, bd=2, height=180)
        signals_frame.grid(row=0, column=0, sticky='ew', pady=(0, 10))
        signals_frame.grid_propagate(False)  # Maintain fixed height

        # Configure horizontal layout
        signals_frame.grid_columnconfigure(0, weight=1)
        signals_frame.grid_columnconfigure(1, weight=1)
        signals_frame.grid_columnconfigure(2, weight=1)
        signals_frame.grid_columnconfigure(3, weight=1)

        # Header
        header_frame = tk.Frame(signals_frame, bg=PROFESSIONAL_COLORS['bg_secondary'], height=40)
        header_frame.grid(row=0, column=0, columnspan=4, sticky='ew', padx=15, pady=(10, 0))
        header_frame.grid_propagate(False)

        signals_title = tk.Label(header_frame, text="⚡ Trading Signals",
                                bg=PROFESSIONAL_COLORS['bg_secondary'],
                                fg=PROFESSIONAL_COLORS['accent_blue'],
                                font=('Segoe UI', 16, 'bold'))
        signals_title.pack(side=tk.LEFT, pady=10)

        # Signals content in horizontal layout
        self.create_horizontal_signals_content(signals_frame)

    def create_horizontal_signals_content(self, parent):
        """Create horizontal signals content layout"""
        # AI Prediction Card (Column 1)
        prediction_card = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                  relief=tk.RAISED, bd=2)
        prediction_card.grid(row=1, column=0, padx=(15, 5), pady=(0, 15), sticky='nsew')

        pred_title = tk.Label(prediction_card, text="🤖 30-Min AI Prediction",
                             bg=PROFESSIONAL_COLORS['bg_tertiary'],
                             fg=PROFESSIONAL_COLORS['accent_purple'],
                             font=('Segoe UI', 11, 'bold'))
        pred_title.pack(pady=(10, 5))

        pred_price = tk.Label(prediction_card, text="Target: ₹186.25",
                             bg=PROFESSIONAL_COLORS['bg_tertiary'],
                             fg=PROFESSIONAL_COLORS['accent_green'],
                             font=('Segoe UI', 16, 'bold'))
        pred_price.pack(pady=3)

        pred_confidence = tk.Label(prediction_card, text="Confidence: 87%\nDirection: BULLISH ↗",
                                  bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                  fg=PROFESSIONAL_COLORS['text_secondary'],
                                  font=('Segoe UI', 9), justify=tk.CENTER)
        pred_confidence.pack(pady=(0, 10))

        # Current Signal Card (Column 2)
        signal_card = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_tertiary'],
                              relief=tk.RAISED, bd=2)
        signal_card.grid(row=1, column=1, padx=5, pady=(0, 15), sticky='nsew')

        signal_title = tk.Label(signal_card, text="📊 Current Signal",
                               bg=PROFESSIONAL_COLORS['bg_tertiary'],
                               fg=PROFESSIONAL_COLORS['accent_blue'],
                               font=('Segoe UI', 11, 'bold'))
        signal_title.pack(pady=(10, 5))

        signal_action = tk.Label(signal_card, text="BUY",
                                bg=PROFESSIONAL_COLORS['accent_green'],
                                fg='white',
                                font=('Segoe UI', 20, 'bold'),
                                relief=tk.RAISED, bd=2, padx=20, pady=8)
        signal_action.pack(pady=8)

        # Signal details (compact)
        details = [
            ("Entry", "₹184.75", PROFESSIONAL_COLORS['text_secondary']),
            ("Target", "₹186.50", PROFESSIONAL_COLORS['accent_green']),
            ("Stop Loss", "₹183.50", PROFESSIONAL_COLORS['accent_red']),
            ("Risk/Reward", "1:3.5", PROFESSIONAL_COLORS['accent_blue'])
        ]

        for label, value, color in details:
            detail_frame = tk.Frame(signal_card, bg=PROFESSIONAL_COLORS['bg_tertiary'])
            detail_frame.pack(fill=tk.X, padx=10, pady=1)

            label_widget = tk.Label(detail_frame, text=label,
                                   bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                   fg=PROFESSIONAL_COLORS['text_secondary'],
                                   font=('Segoe UI', 9))
            label_widget.pack(side=tk.LEFT)

            value_widget = tk.Label(detail_frame, text=value,
                                   bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                   fg=color,
                                   font=('Segoe UI', 10, 'bold'))
            value_widget.pack(side=tk.RIGHT)

        # Add padding
        tk.Frame(signal_card, bg=PROFESSIONAL_COLORS['bg_tertiary'], height=10).pack()

        # Order Flow Status Card (Column 3)
        status_card = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_tertiary'],
                              relief=tk.RAISED, bd=2)
        status_card.grid(row=1, column=2, padx=5, pady=(0, 15), sticky='nsew')

        status_title = tk.Label(status_card, text="🌊 Order Flow Status",
                               bg=PROFESSIONAL_COLORS['bg_tertiary'],
                               fg=PROFESSIONAL_COLORS['accent_orange'],
                               font=('Segoe UI', 11, 'bold'))
        status_title.pack(pady=(10, 5))

        # Order flow metrics (compact)
        flow_metrics = [
            ("CVD Divergence", "Detected", PROFESSIONAL_COLORS['accent_red']),
            ("Imbalance Ratio", "3.2:1", PROFESSIONAL_COLORS['accent_green']),
            ("Volume Profile", "HVN ₹184.85", PROFESSIONAL_COLORS['accent_blue']),
            ("Exhaustion", "Buying", PROFESSIONAL_COLORS['accent_orange'])
        ]

        for label, value, color in flow_metrics:
            metric_frame = tk.Frame(status_card, bg=PROFESSIONAL_COLORS['bg_tertiary'])
            metric_frame.pack(fill=tk.X, padx=10, pady=1)

            label_widget = tk.Label(metric_frame, text=label,
                                   bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                   fg=PROFESSIONAL_COLORS['text_secondary'],
                                   font=('Segoe UI', 9))
            label_widget.pack(side=tk.LEFT)

            value_widget = tk.Label(metric_frame, text=value,
                                   bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                   fg=color,
                                   font=('Segoe UI', 10, 'bold'))
            value_widget.pack(side=tk.RIGHT)

        # Add padding
        tk.Frame(status_card, bg=PROFESSIONAL_COLORS['bg_tertiary'], height=10).pack()

        # Market Metrics Card (Column 4)
        metrics_card = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_tertiary'],
                               relief=tk.RAISED, bd=2)
        metrics_card.grid(row=1, column=3, padx=(5, 15), pady=(0, 15), sticky='nsew')

        metrics_title = tk.Label(metrics_card, text="📈 Market Metrics",
                                bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                fg=PROFESSIONAL_COLORS['accent_blue'],
                                font=('Segoe UI', 11, 'bold'))
        metrics_title.pack(pady=(10, 5))

        # Market metrics
        market_metrics = [
            ("Volume", "1.2M", PROFESSIONAL_COLORS['accent_blue']),
            ("Spread", "₹0.05", PROFESSIONAL_COLORS['accent_orange']),
            ("Volatility", "Low", PROFESSIONAL_COLORS['accent_green']),
            ("Trend", "Bullish", PROFESSIONAL_COLORS['accent_green'])
        ]

        for label, value, color in market_metrics:
            metric_frame = tk.Frame(metrics_card, bg=PROFESSIONAL_COLORS['bg_tertiary'])
            metric_frame.pack(fill=tk.X, padx=10, pady=1)

            label_widget = tk.Label(metric_frame, text=label,
                                   bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                   fg=PROFESSIONAL_COLORS['text_secondary'],
                                   font=('Segoe UI', 9))
            label_widget.pack(side=tk.LEFT)

            value_widget = tk.Label(metric_frame, text=value,
                                   bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                   fg=color,
                                   font=('Segoe UI', 10, 'bold'))
            value_widget.pack(side=tk.RIGHT)

        # Add padding
        tk.Frame(metrics_card, bg=PROFESSIONAL_COLORS['bg_tertiary'], height=10).pack()

    def create_professional_order_book_panel_scrollable(self, parent, row, col):
        """Create professional order book panel with doubled height"""
        ob_frame = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_secondary'],
                           relief=tk.RAISED, bd=2, height=1400)
        ob_frame.grid(row=row, column=col, padx=(0, 10), pady=(0, 20), sticky='nsew')
        ob_frame.grid_propagate(False)  # Maintain fixed height

        # Header with title and spread
        header_frame = tk.Frame(ob_frame, bg=PROFESSIONAL_COLORS['bg_secondary'], height=50)
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 0))
        header_frame.pack_propagate(False)

        # Title
        title_label = tk.Label(header_frame, text="📊 Order Book Depth",
                              bg=PROFESSIONAL_COLORS['bg_secondary'],
                              fg=PROFESSIONAL_COLORS['accent_blue'],
                              font=('Segoe UI', 16, 'bold'))
        title_label.pack(side=tk.LEFT, pady=15)

        # Spread indicator
        self.spread_label = tk.Label(header_frame, text="Spread: ₹0.05",
                                    bg=PROFESSIONAL_COLORS['bg_secondary'],
                                    fg=PROFESSIONAL_COLORS['accent_orange'],
                                    font=('Segoe UI', 12, 'bold'))
        self.spread_label.pack(side=tk.RIGHT, pady=15)

        # Create professional order book display
        self.create_web_style_order_book(ob_frame)

    def create_web_style_order_book(self, parent):
        """Create web-style order book like professional trading platforms"""
        # Main container
        content_frame = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_secondary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Column headers
        header_frame = tk.Frame(content_frame, bg=PROFESSIONAL_COLORS['bg_tertiary'], height=40)
        header_frame.pack(fill=tk.X, pady=(0, 5))
        header_frame.pack_propagate(False)

        # Header labels with proper spacing
        headers = [
            ("Qty", PROFESSIONAL_COLORS['text_secondary'], 120),
            ("Orders", PROFESSIONAL_COLORS['text_secondary'], 80),
            ("Bid", PROFESSIONAL_COLORS['accent_green'], 100),
            ("Ask", PROFESSIONAL_COLORS['accent_red'], 100),
            ("Orders", PROFESSIONAL_COLORS['text_secondary'], 80),
            ("Qty", PROFESSIONAL_COLORS['text_secondary'], 120)
        ]

        for header, color, width in headers:
            label = tk.Label(header_frame, text=header,
                           bg=PROFESSIONAL_COLORS['bg_tertiary'],
                           fg=color,
                           font=('Segoe UI', 11, 'bold'),
                           width=width//8, anchor='center')
            label.pack(side=tk.LEFT, padx=2, fill=tk.Y)

        # Order book rows container
        rows_frame = tk.Frame(content_frame, bg=PROFESSIONAL_COLORS['bg_secondary'])
        rows_frame.pack(fill=tk.BOTH, expand=True)

        # Create 10 levels of order book data
        self.order_book_rows = []
        self.create_order_book_levels(rows_frame)

    def create_order_book_levels(self, parent):
        """Create 10 levels of order book display"""
        # Sample realistic order book data for CGCL
        sample_bids = [
            (1250, 15, 184.70), (890, 12, 184.65), (1100, 18, 184.60),
            (750, 8, 184.55), (950, 14, 184.50), (1200, 20, 184.45),
            (680, 9, 184.40), (1050, 16, 184.35), (920, 11, 184.30),
            (800, 13, 184.25)
        ]

        sample_asks = [
            (184.75, 10, 980), (184.80, 16, 1200), (184.85, 12, 850),
            (184.90, 20, 1400), (184.95, 8, 600), (185.00, 15, 1100),
            (185.05, 11, 750), (185.10, 18, 1300), (185.15, 9, 650),
            (185.20, 14, 950)
        ]

        # Create 10 rows for order book levels
        for i in range(10):
            row_frame = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_secondary'], height=35)
            row_frame.pack(fill=tk.X, pady=1)
            row_frame.pack_propagate(False)

            # Add subtle hover effect background
            if i % 2 == 0:
                row_bg = PROFESSIONAL_COLORS['bg_secondary']
            else:
                row_bg = PROFESSIONAL_COLORS['bg_tertiary']

            row_frame.config(bg=row_bg)

            # Bid side data
            if i < len(sample_bids):
                qty, orders, price = sample_bids[i]

                # Bid quantity
                bid_qty_label = tk.Label(row_frame, text=f"{qty:,}",
                                       bg=row_bg, fg=PROFESSIONAL_COLORS['accent_green'],
                                       font=('Consolas', 11), width=15, anchor='e')
                bid_qty_label.pack(side=tk.LEFT, padx=2)

                # Bid orders
                bid_orders_label = tk.Label(row_frame, text=str(orders),
                                          bg=row_bg, fg=PROFESSIONAL_COLORS['text_secondary'],
                                          font=('Consolas', 11), width=10, anchor='center')
                bid_orders_label.pack(side=tk.LEFT, padx=2)

                # Bid price (highlighted)
                bid_price_label = tk.Label(row_frame, text=f"₹{price:.2f}",
                                         bg=row_bg, fg=PROFESSIONAL_COLORS['accent_green'],
                                         font=('Consolas', 12, 'bold'), width=12, anchor='e')
                bid_price_label.pack(side=tk.LEFT, padx=2)
            else:
                # Empty bid side
                for width in [15, 10, 12]:
                    empty_label = tk.Label(row_frame, text="",
                                         bg=row_bg, width=width)
                    empty_label.pack(side=tk.LEFT, padx=2)

            # Ask side data
            if i < len(sample_asks):
                price, orders, qty = sample_asks[i]

                # Ask price (highlighted)
                ask_price_label = tk.Label(row_frame, text=f"₹{price:.2f}",
                                         bg=row_bg, fg=PROFESSIONAL_COLORS['accent_red'],
                                         font=('Consolas', 12, 'bold'), width=12, anchor='w')
                ask_price_label.pack(side=tk.LEFT, padx=2)

                # Ask orders
                ask_orders_label = tk.Label(row_frame, text=str(orders),
                                          bg=row_bg, fg=PROFESSIONAL_COLORS['text_secondary'],
                                          font=('Consolas', 11), width=10, anchor='center')
                ask_orders_label.pack(side=tk.LEFT, padx=2)

                # Ask quantity
                ask_qty_label = tk.Label(row_frame, text=f"{qty:,}",
                                       bg=row_bg, fg=PROFESSIONAL_COLORS['accent_red'],
                                       font=('Consolas', 11), width=15, anchor='w')
                ask_qty_label.pack(side=tk.LEFT, padx=2)
            else:
                # Empty ask side
                for width in [12, 10, 15]:
                    empty_label = tk.Label(row_frame, text="",
                                         bg=row_bg, width=width)
                    empty_label.pack(side=tk.LEFT, padx=2)

            # Store row for updates
            self.order_book_rows.append({
                'frame': row_frame,
                'bid_qty': bid_qty_label if i < len(sample_bids) else None,
                'bid_orders': bid_orders_label if i < len(sample_bids) else None,
                'bid_price': bid_price_label if i < len(sample_bids) else None,
                'ask_price': ask_price_label if i < len(sample_asks) else None,
                'ask_orders': ask_orders_label if i < len(sample_asks) else None,
                'ask_qty': ask_qty_label if i < len(sample_asks) else None
            })

    def create_large_order_flow_analysis_panel_scrollable(self, parent, row, col, colspan=1):
        """Create large order flow analysis panel with doubled height"""
        flow_frame = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_secondary'],
                             relief=tk.RAISED, bd=2, height=1400)
        flow_frame.grid(row=row, column=col, columnspan=colspan, padx=(10, 0), pady=(0, 20), sticky='nsew')
        flow_frame.grid_propagate(False)  # Maintain fixed height

        # Header
        header_frame = tk.Frame(flow_frame, bg=PROFESSIONAL_COLORS['bg_secondary'], height=50)
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 0))
        header_frame.pack_propagate(False)

        flow_title = tk.Label(header_frame, text="🌊 Professional Order Flow Analysis",
                             bg=PROFESSIONAL_COLORS['bg_secondary'],
                             fg=PROFESSIONAL_COLORS['accent_blue'],
                             font=('Segoe UI', 16, 'bold'))
        flow_title.pack(side=tk.LEFT, pady=15)

        # Create large order flow chart
        self.create_large_order_flow_chart(flow_frame)

    def create_large_order_flow_chart(self, parent):
        """Create professional order flow analysis with doubled height and scrollbar-adjusted width"""
        # Create much larger figure with doubled height and scrollbar-adjusted width
        fig = Figure(figsize=(18, 12), facecolor=PROFESSIONAL_COLORS['bg_secondary'])
        fig.patch.set_facecolor(PROFESSIONAL_COLORS['bg_secondary'])

        # Create subplots for professional order flow tools with better spacing (now 2x2 grid)
        gs = fig.add_gridspec(2, 2, height_ratios=[1, 1], width_ratios=[1, 1], hspace=0.5, wspace=0.3)

        # Professional Order Flow Analysis Charts
        # 1. Cumulative Volume Delta (CVD) - Top Left
        ax1 = fig.add_subplot(gs[0, 0])
        ax1.set_facecolor(PROFESSIONAL_COLORS['bg_tertiary'])

        # 2. Volume Profile - Top Right
        ax2 = fig.add_subplot(gs[0, 1])
        ax2.set_facecolor(PROFESSIONAL_COLORS['bg_tertiary'])

        # 3. Footprint Analysis - Bottom Left
        ax3 = fig.add_subplot(gs[1, 0])
        ax3.set_facecolor(PROFESSIONAL_COLORS['bg_tertiary'])

        # 4. Order Flow Imbalances - Bottom Right
        ax4 = fig.add_subplot(gs[1, 1])
        ax4.set_facecolor(PROFESSIONAL_COLORS['bg_tertiary'])

        # Generate professional order flow data
        times = np.arange(0, 60, 1)  # Last 60 minutes
        prices = np.linspace(184.00, 185.50, 60)  # Price range

        # 1. CUMULATIVE VOLUME DELTA (CVD) - Key concept from video
        # Generate realistic CVD data with divergences
        price_trend = 184.75 + np.cumsum(np.random.randn(60) * 0.02)  # Price movement
        cvd_data = np.cumsum(np.random.randn(60) * 100)  # CVD movement

        # Create divergence example (price up, CVD down = buying exhaustion)
        cvd_data[40:] = cvd_data[40] - np.cumsum(np.abs(np.random.randn(20)) * 50)

        # Plot price and CVD
        ax1_twin = ax1.twinx()
        line1 = ax1.plot(times, price_trend, color=PROFESSIONAL_COLORS['accent_blue'],
                        linewidth=2, label='Price', alpha=0.8)
        line2 = ax1_twin.plot(times, cvd_data, color=PROFESSIONAL_COLORS['accent_orange'],
                             linewidth=2, label='CVD', alpha=0.8)

        # Highlight divergence area
        ax1.axvspan(40, 60, alpha=0.2, color=PROFESSIONAL_COLORS['accent_red'],
                   label='Divergence Zone')

        ax1.set_title('Cumulative Volume Delta (CVD) Analysis',
                     color=PROFESSIONAL_COLORS['text_primary'], fontsize=16, pad=20)
        ax1.set_ylabel('Price (₹)', color=PROFESSIONAL_COLORS['text_secondary'], fontsize=13)
        ax1_twin.set_ylabel('CVD', color=PROFESSIONAL_COLORS['text_secondary'], fontsize=13)
        ax1.tick_params(colors=PROFESSIONAL_COLORS['text_secondary'], labelsize=11)
        ax1_twin.tick_params(colors=PROFESSIONAL_COLORS['text_secondary'], labelsize=11)
        ax1.grid(True, alpha=0.3, color=PROFESSIONAL_COLORS['border_light'])

        # Add legend
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='upper left', fontsize=11,
                  facecolor=PROFESSIONAL_COLORS['bg_tertiary'])

        # 2. VOLUME PROFILE - Horizontal histogram showing volume at price levels
        # Generate volume profile data
        volume_profile_prices = np.linspace(184.00, 185.50, 30)
        volume_profile_volumes = np.random.gamma(2, 500, 30)

        # Identify High Volume Node (HVN) and Low Volume Areas
        hvn_index = np.argmax(volume_profile_volumes)
        hvn_price = volume_profile_prices[hvn_index]

        # Create volume profile bars
        bars = ax2.barh(volume_profile_prices, volume_profile_volumes,
                       color=PROFESSIONAL_COLORS['accent_blue'], alpha=0.7, height=0.04)

        # Highlight High Volume Node (Fair Value)
        bars[hvn_index].set_color(PROFESSIONAL_COLORS['accent_green'])
        bars[hvn_index].set_alpha(1.0)

        # Mark Low Volume Areas (Imbalances)
        low_vol_threshold = np.percentile(volume_profile_volumes, 25)
        for i, vol in enumerate(volume_profile_volumes):
            if vol < low_vol_threshold:
                bars[i].set_color(PROFESSIONAL_COLORS['accent_red'])
                bars[i].set_alpha(0.5)

        ax2.set_title('Volume Profile (Fair Value Analysis)',
                     color=PROFESSIONAL_COLORS['text_primary'], fontsize=16, pad=20)
        ax2.set_xlabel('Volume', color=PROFESSIONAL_COLORS['text_secondary'], fontsize=11)
        ax2.set_ylabel('Price (₹)', color=PROFESSIONAL_COLORS['text_secondary'], fontsize=11)
        ax2.tick_params(colors=PROFESSIONAL_COLORS['text_secondary'], labelsize=10)
        ax2.grid(True, alpha=0.3, color=PROFESSIONAL_COLORS['border_light'])

        # Add HVN annotation
        ax2.annotate(f'HVN: ₹{hvn_price:.2f}',
                    xy=(volume_profile_volumes[hvn_index], hvn_price),
                    xytext=(volume_profile_volumes[hvn_index] * 1.2, hvn_price + 0.2),
                    color=PROFESSIONAL_COLORS['accent_green'], fontsize=9, fontweight='bold',
                    arrowprops=dict(arrowstyle='->', color=PROFESSIONAL_COLORS['accent_green']))

        # 3. FOOTPRINT ANALYSIS - Bid/Ask volume at each price level
        # Create footprint-style visualization
        footprint_prices = np.linspace(184.50, 185.00, 10)
        bid_volumes = np.random.randint(100, 1000, 10)
        ask_volumes = np.random.randint(100, 1000, 10)

        # Calculate imbalances (300% rule from video)
        imbalances = []
        for i in range(len(bid_volumes)):
            if bid_volumes[i] > 0 and ask_volumes[i] > 0:
                ratio = max(bid_volumes[i], ask_volumes[i]) / min(bid_volumes[i], ask_volumes[i])
                if ratio >= 3.0:  # 300% imbalance threshold
                    imbalances.append(i)

        # Plot bid volumes (left side)
        ax3.barh(footprint_prices, -bid_volumes, color=PROFESSIONAL_COLORS['accent_green'],
                alpha=0.7, label='Bid Volume', height=0.04)

        # Plot ask volumes (right side)
        ax3.barh(footprint_prices, ask_volumes, color=PROFESSIONAL_COLORS['accent_red'],
                alpha=0.7, label='Ask Volume', height=0.04)

        # Highlight imbalances
        for idx in imbalances:
            if bid_volumes[idx] > ask_volumes[idx]:
                # Strong buying imbalance
                ax3.barh(footprint_prices[idx], -bid_volumes[idx],
                        color=PROFESSIONAL_COLORS['accent_green'], alpha=1.0, height=0.04)
                ax3.annotate('IMBAL', xy=(-bid_volumes[idx]/2, footprint_prices[idx]),
                           ha='center', va='center', fontsize=8, fontweight='bold',
                           color='white')
            else:
                # Strong selling imbalance
                ax3.barh(footprint_prices[idx], ask_volumes[idx],
                        color=PROFESSIONAL_COLORS['accent_red'], alpha=1.0, height=0.04)
                ax3.annotate('IMBAL', xy=(ask_volumes[idx]/2, footprint_prices[idx]),
                           ha='center', va='center', fontsize=8, fontweight='bold',
                           color='white')

        ax3.axvline(x=0, color=PROFESSIONAL_COLORS['text_secondary'], linestyle='-', alpha=0.5)
        ax3.set_title('Footprint Analysis (Bid/Ask Imbalances)',
                     color=PROFESSIONAL_COLORS['text_primary'], fontsize=16, pad=20)
        ax3.set_xlabel('Volume (Bid ← | → Ask)', color=PROFESSIONAL_COLORS['text_secondary'], fontsize=11)
        ax3.set_ylabel('Price (₹)', color=PROFESSIONAL_COLORS['text_secondary'], fontsize=11)
        ax3.tick_params(colors=PROFESSIONAL_COLORS['text_secondary'], labelsize=10)
        ax3.legend(fontsize=9, facecolor=PROFESSIONAL_COLORS['bg_tertiary'])
        ax3.grid(True, alpha=0.3, color=PROFESSIONAL_COLORS['border_light'])

        # 4. ORDER FLOW EXHAUSTION PATTERNS
        # Generate exhaustion pattern data
        exhaustion_times = np.arange(0, 20, 1)
        buying_exhaustion = np.array([1, 1.2, 1.5, 1.8, 2.0, 1.9, 1.7, 1.5, 1.2, 1.0,
                                     0.8, 0.6, 0.4, 0.2, 0.1, -0.1, -0.3, -0.5, -0.7, -0.9])
        selling_exhaustion = np.array([-1, -1.2, -1.5, -1.8, -2.0, -1.9, -1.7, -1.5, -1.2, -1.0,
                                      -0.8, -0.6, -0.4, -0.2, -0.1, 0.1, 0.3, 0.5, 0.7, 0.9])

        # Plot exhaustion patterns
        ax4.plot(exhaustion_times, buying_exhaustion, color=PROFESSIONAL_COLORS['accent_green'],
                linewidth=3, label='Buying → Exhaustion', alpha=0.8)
        ax4.plot(exhaustion_times, selling_exhaustion, color=PROFESSIONAL_COLORS['accent_red'],
                linewidth=3, label='Selling → Exhaustion', alpha=0.8)

        # Mark exhaustion points
        ax4.scatter([10], [buying_exhaustion[10]], color=PROFESSIONAL_COLORS['accent_orange'],
                   s=100, zorder=5, label='Exhaustion Points')
        ax4.scatter([10], [selling_exhaustion[10]], color=PROFESSIONAL_COLORS['accent_orange'],
                   s=100, zorder=5)

        # Add exhaustion zones
        ax4.axvspan(8, 12, alpha=0.2, color=PROFESSIONAL_COLORS['accent_orange'],
                   label='Exhaustion Zone')

        ax4.axhline(y=0, color=PROFESSIONAL_COLORS['text_secondary'], linestyle='-', alpha=0.5)
        ax4.set_title('Order Flow Exhaustion Patterns',
                     color=PROFESSIONAL_COLORS['text_primary'], fontsize=16, pad=20)
        ax4.set_xlabel('Time', color=PROFESSIONAL_COLORS['text_secondary'], fontsize=11)
        ax4.set_ylabel('Flow Strength', color=PROFESSIONAL_COLORS['text_secondary'], fontsize=11)
        ax4.tick_params(colors=PROFESSIONAL_COLORS['text_secondary'], labelsize=10)
        ax4.legend(fontsize=9, facecolor=PROFESSIONAL_COLORS['bg_tertiary'])
        ax4.grid(True, alpha=0.3, color=PROFESSIONAL_COLORS['border_light'])

        # Style all axes
        for ax in [ax1, ax2, ax3, ax4]:
            ax.spines['bottom'].set_color(PROFESSIONAL_COLORS['border_light'])
            ax.spines['top'].set_color(PROFESSIONAL_COLORS['border_light'])
            ax.spines['right'].set_color(PROFESSIONAL_COLORS['border_light'])
            ax.spines['left'].set_color(PROFESSIONAL_COLORS['border_light'])

        # Embed chart in tkinter
        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        self.charts['flow_chart'] = {'figure': fig, 'canvas': canvas, 'axes': [ax1, ax2, ax3, ax4]}

    def create_market_analytics_panel_scrollable(self, parent, row, col, colspan):
        """Create market analytics panel with doubled height"""
        analytics_frame = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_secondary'],
                                  relief=tk.RAISED, bd=2, height=600)
        analytics_frame.grid(row=row, column=col, columnspan=colspan,
                           padx=0, pady=(0, 20), sticky='nsew')
        analytics_frame.grid_propagate(False)  # Maintain fixed height

        # Header
        header_frame = tk.Frame(analytics_frame, bg=PROFESSIONAL_COLORS['bg_secondary'], height=40)
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 0))
        header_frame.pack_propagate(False)

        analytics_title = tk.Label(header_frame, text="📈 Professional Market Analytics",
                                  bg=PROFESSIONAL_COLORS['bg_secondary'],
                                  fg=PROFESSIONAL_COLORS['accent_blue'],
                                  font=('Segoe UI', 14, 'bold'))
        analytics_title.pack(side=tk.LEFT, pady=10)

        # Create analytics content
        self.create_market_insights_content(analytics_frame)

    def create_market_insights_content(self, parent):
        """Create market insights and analytics content"""
        content_frame = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_secondary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Create grid for analytics cards (4 columns for full width)
        content_frame.grid_columnconfigure(0, weight=1)
        content_frame.grid_columnconfigure(1, weight=1)
        content_frame.grid_columnconfigure(2, weight=1)
        content_frame.grid_columnconfigure(3, weight=1)
        content_frame.grid_rowconfigure(0, weight=1)

        # Market Depth Analysis Card
        depth_card = tk.Frame(content_frame, bg=PROFESSIONAL_COLORS['bg_tertiary'],
                             relief=tk.RAISED, bd=2)
        depth_card.grid(row=0, column=0, padx=(0, 3), pady=0, sticky='nsew')

        depth_title = tk.Label(depth_card, text="📊 Market Depth",
                              bg=PROFESSIONAL_COLORS['bg_tertiary'],
                              fg=PROFESSIONAL_COLORS['accent_blue'],
                              font=('Segoe UI', 12, 'bold'))
        depth_title.pack(pady=(10, 5))

        # Depth metrics
        depth_metrics = [
            ("Total Bid Volume", "12,450", PROFESSIONAL_COLORS['accent_green']),
            ("Total Ask Volume", "11,890", PROFESSIONAL_COLORS['accent_red']),
            ("Bid/Ask Ratio", "1.05", PROFESSIONAL_COLORS['accent_orange']),
            ("Market Depth", "Strong", PROFESSIONAL_COLORS['accent_green'])
        ]

        for label, value, color in depth_metrics:
            metric_frame = tk.Frame(depth_card, bg=PROFESSIONAL_COLORS['bg_tertiary'])
            metric_frame.pack(fill=tk.X, padx=10, pady=2)

            label_widget = tk.Label(metric_frame, text=label,
                                   bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                   fg=PROFESSIONAL_COLORS['text_secondary'],
                                   font=('Segoe UI', 9))
            label_widget.pack(side=tk.LEFT)

            value_widget = tk.Label(metric_frame, text=value,
                                   bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                   fg=color,
                                   font=('Segoe UI', 10, 'bold'))
            value_widget.pack(side=tk.RIGHT)

        # Order Flow Card
        flow_card = tk.Frame(content_frame, bg=PROFESSIONAL_COLORS['bg_tertiary'],
                            relief=tk.RAISED, bd=2)
        flow_card.grid(row=0, column=1, padx=(3, 3), pady=0, sticky='nsew')

        flow_title = tk.Label(flow_card, text="📊 Professional Order Flow",
                             bg=PROFESSIONAL_COLORS['bg_tertiary'],
                             fg=PROFESSIONAL_COLORS['accent_purple'],
                             font=('Segoe UI', 12, 'bold'))
        flow_title.pack(pady=(10, 5))

        # Professional Order Flow Metrics (based on video concepts)
        flow_metrics = [
            ("CVD Divergence", "Detected", PROFESSIONAL_COLORS['accent_red']),
            ("Footprint Imbalance", "3.2:1", PROFESSIONAL_COLORS['accent_green']),
            ("Volume Profile HVN", "₹184.85", PROFESSIONAL_COLORS['accent_blue']),
            ("Exhaustion Pattern", "Buying", PROFESSIONAL_COLORS['accent_orange'])
        ]

        for label, value, color in flow_metrics:
            metric_frame = tk.Frame(flow_card, bg=PROFESSIONAL_COLORS['bg_tertiary'])
            metric_frame.pack(fill=tk.X, padx=10, pady=2)

            label_widget = tk.Label(metric_frame, text=label,
                                   bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                   fg=PROFESSIONAL_COLORS['text_secondary'],
                                   font=('Segoe UI', 9))
            label_widget.pack(side=tk.LEFT)

            value_widget = tk.Label(metric_frame, text=value,
                                   bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                   fg=color,
                                   font=('Segoe UI', 10, 'bold'))
            value_widget.pack(side=tk.RIGHT)

        # Market Sentiment Card
        sentiment_card = tk.Frame(content_frame, bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                 relief=tk.RAISED, bd=2)
        sentiment_card.grid(row=0, column=2, padx=(3, 3), pady=0, sticky='nsew')

        sentiment_title = tk.Label(sentiment_card, text="📈 Sentiment",
                                  bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                  fg=PROFESSIONAL_COLORS['accent_orange'],
                                  font=('Segoe UI', 12, 'bold'))
        sentiment_title.pack(pady=(10, 5))

        # Sentiment metrics
        sentiment_metrics = [
            ("Bull/Bear Ratio", "2.3:1", PROFESSIONAL_COLORS['accent_green']),
            ("Momentum", "Strong", PROFESSIONAL_COLORS['accent_blue']),
            ("Volatility", "Low", PROFESSIONAL_COLORS['accent_green']),
            ("Trend", "Bullish", PROFESSIONAL_COLORS['accent_green'])
        ]

        for label, value, color in sentiment_metrics:
            metric_frame = tk.Frame(sentiment_card, bg=PROFESSIONAL_COLORS['bg_tertiary'])
            metric_frame.pack(fill=tk.X, padx=10, pady=2)

            label_widget = tk.Label(metric_frame, text=label,
                                   bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                   fg=PROFESSIONAL_COLORS['text_secondary'],
                                   font=('Segoe UI', 9))
            label_widget.pack(side=tk.LEFT)

            value_widget = tk.Label(metric_frame, text=value,
                                   bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                   fg=color,
                                   font=('Segoe UI', 10, 'bold'))
            value_widget.pack(side=tk.RIGHT)

        # Technical Analysis Card (4th column)
        technical_card = tk.Frame(content_frame, bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                 relief=tk.RAISED, bd=2)
        technical_card.grid(row=0, column=3, padx=(3, 0), pady=0, sticky='nsew')

        technical_title = tk.Label(technical_card, text="📊 Technical",
                                  bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                  fg=PROFESSIONAL_COLORS['accent_purple'],
                                  font=('Segoe UI', 12, 'bold'))
        technical_title.pack(pady=(10, 5))

        # Technical metrics
        technical_metrics = [
            ("RSI", "65.2", PROFESSIONAL_COLORS['accent_orange']),
            ("MACD", "Bullish", PROFESSIONAL_COLORS['accent_green']),
            ("Support", "₹183.50", PROFESSIONAL_COLORS['accent_blue']),
            ("Resistance", "₹186.00", PROFESSIONAL_COLORS['accent_red'])
        ]

        for label, value, color in technical_metrics:
            metric_frame = tk.Frame(technical_card, bg=PROFESSIONAL_COLORS['bg_tertiary'])
            metric_frame.pack(fill=tk.X, padx=10, pady=2)

            label_widget = tk.Label(metric_frame, text=label,
                                   bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                   fg=PROFESSIONAL_COLORS['text_secondary'],
                                   font=('Segoe UI', 9))
            label_widget.pack(side=tk.LEFT)

            value_widget = tk.Label(metric_frame, text=value,
                                   bg=PROFESSIONAL_COLORS['bg_tertiary'],
                                   fg=color,
                                   font=('Segoe UI', 10, 'bold'))
            value_widget.pack(side=tk.RIGHT)













    def create_trading_signals_panel(self, parent):
        """Create trading signals and recommendations panel"""
        # Since analytics now spans full width, we'll integrate signals into analytics
        # This method will be called but won't create a separate panel
        pass



    def create_professional_footer(self, parent):
        """Create professional control footer"""
        footer_frame = tk.Frame(parent, bg=PROFESSIONAL_COLORS['bg_secondary'], height=80)
        footer_frame.pack(fill=tk.X, pady=(20, 0))
        footer_frame.pack_propagate(False)

        # Add border
        border_frame = tk.Frame(footer_frame, bg=PROFESSIONAL_COLORS['border_light'], height=2)
        border_frame.pack(side=tk.TOP, fill=tk.X)

        # Control buttons
        controls_frame = tk.Frame(footer_frame, bg=PROFESSIONAL_COLORS['bg_secondary'])
        controls_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # Left side - Connection controls
        left_controls = tk.Frame(controls_frame, bg=PROFESSIONAL_COLORS['bg_secondary'])
        left_controls.pack(side=tk.LEFT, fill=tk.Y)

        connect_btn = tk.Button(left_controls, text="🔌 Connect WebSocket",
                               bg=PROFESSIONAL_COLORS['accent_green'],
                               fg='white',
                               font=('Segoe UI', 11, 'bold'),
                               relief=tk.FLAT, bd=0,
                               padx=20, pady=8,
                               command=self.connect_websocket)
        connect_btn.pack(side=tk.LEFT, padx=(0, 10))

        analytics_btn = tk.Button(left_controls, text="📊 Advanced Analytics",
                                 bg=PROFESSIONAL_COLORS['accent_blue'],
                                 fg='white',
                                 font=('Segoe UI', 11, 'bold'),
                                 relief=tk.FLAT, bd=0,
                                 padx=20, pady=8,
                                 command=self.toggle_analytics)
        analytics_btn.pack(side=tk.LEFT, padx=(0, 10))

        settings_btn = tk.Button(left_controls, text="⚙️ Settings",
                                bg=PROFESSIONAL_COLORS['bg_accent'],
                                fg=PROFESSIONAL_COLORS['text_primary'],
                                font=('Segoe UI', 11, 'bold'),
                                relief=tk.FLAT, bd=0,
                                padx=20, pady=8,
                                command=self.open_settings)
        settings_btn.pack(side=tk.LEFT)

        # Right side - Status and exit
        right_controls = tk.Frame(controls_frame, bg=PROFESSIONAL_COLORS['bg_secondary'])
        right_controls.pack(side=tk.RIGHT, fill=tk.Y)

        # System status
        status_frame = tk.Frame(right_controls, bg=PROFESSIONAL_COLORS['bg_secondary'])
        status_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 20))

        status_label = tk.Label(status_frame, text="System Status:",
                               bg=PROFESSIONAL_COLORS['bg_secondary'],
                               fg=PROFESSIONAL_COLORS['text_secondary'],
                               font=('Segoe UI', 9))
        status_label.pack()

        self.system_status_label = tk.Label(status_frame, text="🟢 All Systems Operational",
                                           bg=PROFESSIONAL_COLORS['bg_secondary'],
                                           fg=PROFESSIONAL_COLORS['accent_green'],
                                           font=('Segoe UI', 10, 'bold'))
        self.system_status_label.pack()

        # Exit button
        exit_btn = tk.Button(right_controls, text="❌ Exit Application",
                            bg=PROFESSIONAL_COLORS['accent_red'],
                            fg='white',
                            font=('Segoe UI', 11, 'bold'),
                            relief=tk.FLAT, bd=0,
                            padx=20, pady=8,
                            command=self.exit_application)
        exit_btn.pack(side=tk.RIGHT)

    def setup_real_time_updates(self):
        """Setup real-time data updates"""
        self.is_running = True
        self.update_thread = threading.Thread(target=self.real_time_update_loop, daemon=True)
        self.update_thread.start()

    def real_time_update_loop(self):
        """Real-time update loop for live data"""
        while self.is_running:
            try:
                # Update timestamp
                current_time = datetime.now().strftime("%H:%M:%S")
                if hasattr(self, 'last_update_label'):
                    self.root.after(0, lambda: self.last_update_label.config(
                        text=f"Updated: {current_time}"))

                # Simulate real-time data updates
                self.update_market_data()

                time.sleep(1)  # Update every second

            except Exception as e:
                print(f"Error in real-time update: {e}")
                time.sleep(5)

    def update_market_data(self):
        """Update market data with simulated real-time values"""
        try:
            # Simulate price movement
            base_price = 184.75
            price_change = np.random.randn() * 0.05
            new_price = base_price + price_change

            # Update price display (if exists)
            # This would connect to your actual market data

        except Exception as e:
            print(f"Error updating market data: {e}")

    # Button callback methods
    def connect_websocket(self):
        """Connect to WebSocket"""
        print("🔌 Connecting to Smart API WebSocket...")
        # Add your WebSocket connection logic here

    def toggle_analytics(self):
        """Toggle advanced analytics"""
        print("📊 Toggling advanced analytics...")
        # Add analytics toggle logic here

    def open_settings(self):
        """Open settings dialog"""
        print("⚙️ Opening settings...")
        # Add settings dialog logic here

    def exit_application(self):
        """Exit the application"""
        print("❌ Exiting application...")
        self.is_running = False
        if self.root:
            self.root.quit()
            self.root.destroy()

    def run(self):
        """Start the professional dashboard"""
        try:
            if not self.root:
                raise Exception("GUI not initialized")

            print("🚀 Starting Professional CGCL Dashboard...")
            self.root.mainloop()

        except Exception as e:
            print(f"❌ Error running dashboard: {e}")
            raise

# Main execution
if __name__ == "__main__":
    try:
        print("🎨 CGCL PROFESSIONAL TRADING DASHBOARD")
        print("=" * 50)
        print("✨ Executive-level interface")
        print("📊 Advanced analytics & charts")
        print("🔍 Real-time market intelligence")
        print("⚡ Professional trading signals")
        print("=" * 50)

        dashboard = ProfessionalCGCLDashboard()
        dashboard.run()

    except KeyboardInterrupt:
        print("\n⏹️ Dashboard stopped by user")
    except Exception as e:
        print(f"\n❌ Critical Error: {e}")
        import traceback
        traceback.print_exc()
