"""
Order Book Reconstruction Engine for Missing Data Handling
"""

import statistics
from collections import deque
from datetime import datetime
from typing import Dict, List, Tuple, Optional

from core.data_structures import OrderBookLevel


class OrderBookReconstructionEngine:
    """Advanced order book reconstruction with missing data handling"""
    
    def __init__(self, max_levels: int = 20):
        self.max_levels = max_levels
        
        # State tracking
        self.last_valid_state = None
        self.missing_data_count = 0
        self.reconstruction_count = 0
        self.data_quality_score = 100.0
        
        # Reconstruction algorithms
        self.interpolation_enabled = True
        self.extrapolation_enabled = True
        self.pattern_matching_enabled = True
        
        # Historical patterns for reconstruction
        self.price_level_patterns = {}  # price -> typical quantity patterns
        self.spread_patterns = deque(maxlen=100)  # historical spreads
        self.imbalance_patterns = deque(maxlen=100)  # historical imbalances
        
        # Quality metrics
        self.reconstruction_accuracy = deque(maxlen=50)
        self.data_gaps = deque(maxlen=100)
    
    def reconstruct_order_book(self, raw_bids: List[Tuple], raw_asks: List[Tuple], 
                              current_price: float, timestamp: datetime) -> Tuple[List[Tuple], List[Tuple], Dict]:
        """Reconstruct complete order book from potentially incomplete data"""
        try:
            quality_metrics = {
                'original_bid_levels': len(raw_bids),
                'original_ask_levels': len(raw_asks),
                'reconstructed_bid_levels': 0,
                'reconstructed_ask_levels': 0,
                'data_quality_score': 100.0,
                'reconstruction_methods_used': [],
                'missing_data_detected': False
            }
            
            # Validate and clean input data
            cleaned_bids = self._validate_and_clean_data(raw_bids, 'bid')
            cleaned_asks = self._validate_and_clean_data(raw_asks, 'ask')
            
            # Detect missing data
            missing_bid_levels = self._detect_missing_levels(cleaned_bids, 'bid')
            missing_ask_levels = self._detect_missing_levels(cleaned_asks, 'ask')
            
            if missing_bid_levels or missing_ask_levels:
                quality_metrics['missing_data_detected'] = True
                self.missing_data_count += 1
            
            # Reconstruct missing bid levels
            reconstructed_bids = self._reconstruct_missing_levels(
                cleaned_bids, missing_bid_levels, current_price, 'bid'
            )
            
            # Reconstruct missing ask levels
            reconstructed_asks = self._reconstruct_missing_levels(
                cleaned_asks, missing_ask_levels, current_price, 'ask'
            )
            
            # Apply consistency checks
            reconstructed_bids, reconstructed_asks = self._apply_consistency_checks(
                reconstructed_bids, reconstructed_asks, current_price
            )
            
            # Update quality metrics
            quality_metrics.update({
                'reconstructed_bid_levels': len(reconstructed_bids),
                'reconstructed_ask_levels': len(reconstructed_asks),
                'data_quality_score': self._calculate_data_quality_score(
                    len(raw_bids), len(raw_asks), len(reconstructed_bids), len(reconstructed_asks)
                )
            })
            
            # Store patterns for future reconstruction
            self._update_patterns(reconstructed_bids, reconstructed_asks, current_price)
            
            # Update state
            self.last_valid_state = {
                'bids': reconstructed_bids,
                'asks': reconstructed_asks,
                'timestamp': timestamp,
                'price': current_price
            }
            
            self.reconstruction_count += 1
            self.data_quality_score = quality_metrics['data_quality_score']
            
            return reconstructed_bids, reconstructed_asks, quality_metrics
            
        except Exception as e:
            print(f"Error in order book reconstruction: {e}")
            return raw_bids, raw_asks, {'error': str(e)}
    
    def _validate_and_clean_data(self, levels: List[Tuple], side: str) -> List[Tuple]:
        """Validate and clean order book level data"""
        try:
            cleaned = []
            
            for level in levels:
                if side == 'bid':
                    # Bid format: (quantity, orders, price)
                    if len(level) >= 3:
                        qty, orders, price = level[0], level[1], level[2]
                        if qty > 0 and price > 0 and orders > 0:
                            cleaned.append((qty, orders, price))
                else:
                    # Ask format: (price, orders, quantity)
                    if len(level) >= 3:
                        price, orders, qty = level[0], level[1], level[2]
                        if qty > 0 and price > 0 and orders > 0:
                            cleaned.append((price, orders, qty))
            
            # Sort levels
            if side == 'bid':
                cleaned.sort(key=lambda x: x[2], reverse=True)  # Sort by price descending
            else:
                cleaned.sort(key=lambda x: x[0])  # Sort by price ascending
            
            return cleaned
            
        except Exception as e:
            print(f"Error validating data: {e}")
            return levels
    
    def _detect_missing_levels(self, levels: List[Tuple], side: str) -> List[int]:
        """Detect missing price levels in order book"""
        try:
            if len(levels) < 2:
                return list(range(len(levels), min(10, self.max_levels)))
            
            missing_levels = []
            
            # Check for gaps in price levels
            if side == 'bid':
                prices = [level[2] for level in levels]  # Extract prices
                # Check if we have fewer levels than expected
                if len(levels) < 10:
                    missing_levels.extend(range(len(levels), 10))
            else:
                prices = [level[0] for level in levels]  # Extract prices
                # Check if we have fewer levels than expected
                if len(levels) < 10:
                    missing_levels.extend(range(len(levels), 10))
            
            # Detect price gaps
            if len(prices) > 1:
                price_diffs = [abs(prices[i] - prices[i-1]) for i in range(1, len(prices))]
                avg_diff = statistics.mean(price_diffs)
                
                for i in range(1, len(prices)):
                    if abs(prices[i] - prices[i-1]) > avg_diff * 2:
                        # Large gap detected
                        missing_levels.append(i)
            
            return missing_levels
            
        except Exception as e:
            print(f"Error detecting missing levels: {e}")
            return []
    
    def _reconstruct_missing_levels(self, existing_levels: List[Tuple], 
                                   missing_indices: List[int], current_price: float, 
                                   side: str) -> List[Tuple]:
        """Reconstruct missing order book levels"""
        try:
            if not missing_indices:
                return existing_levels
            
            reconstructed = existing_levels.copy()
            
            for missing_index in missing_indices:
                if side == 'bid':
                    reconstructed_level = self._interpolate_bid_level(
                        reconstructed, missing_index, current_price
                    )
                else:
                    reconstructed_level = self._interpolate_ask_level(
                        reconstructed, missing_index, current_price
                    )
                
                if reconstructed_level:
                    # Insert at appropriate position
                    if missing_index < len(reconstructed):
                        reconstructed.insert(missing_index, reconstructed_level)
                    else:
                        reconstructed.append(reconstructed_level)
            
            # Limit to max levels
            return reconstructed[:self.max_levels]
            
        except Exception as e:
            print(f"Error reconstructing missing levels: {e}")
            return existing_levels
    
    def _interpolate_bid_level(self, existing_bids: List[Tuple], index: int, 
                              current_price: float) -> Optional[Tuple]:
        """Interpolate missing bid level"""
        try:
            if not existing_bids:
                # Create initial bid level
                price = current_price * 0.999  # 0.1% below current price
                return (100, 1, price)  # Default quantity and orders
            
            if index == 0:
                # Missing best bid
                best_bid_price = existing_bids[0][2]
                price = best_bid_price + 0.05  # Slightly higher
                qty = existing_bids[0][0]  # Same quantity as next level
                orders = max(1, existing_bids[0][1])
                return (qty, orders, price)
            
            elif index < len(existing_bids):
                # Interpolate between levels
                prev_level = existing_bids[index - 1]
                next_level = existing_bids[index]
                
                price = (prev_level[2] + next_level[2]) / 2
                qty = int((prev_level[0] + next_level[0]) / 2)
                orders = max(1, int((prev_level[1] + next_level[1]) / 2))
                
                return (qty, orders, price)
            
            else:
                # Extrapolate beyond existing levels
                if len(existing_bids) >= 2:
                    last_level = existing_bids[-1]
                    second_last = existing_bids[-2]
                    
                    price_diff = second_last[2] - last_level[2]
                    price = last_level[2] - price_diff
                    
                    # Reduce quantity for deeper levels
                    qty = max(50, int(last_level[0] * 0.8))
                    orders = max(1, last_level[1])
                    
                    return (qty, orders, price)
            
            return None
            
        except Exception as e:
            print(f"Error interpolating bid level: {e}")
            return None
    
    def _interpolate_ask_level(self, existing_asks: List[Tuple], index: int, 
                              current_price: float) -> Optional[Tuple]:
        """Interpolate missing ask level"""
        try:
            if not existing_asks:
                # Create initial ask level
                price = current_price * 1.001  # 0.1% above current price
                return (price, 1, 100)  # Default price, orders, quantity
            
            if index == 0:
                # Missing best ask
                best_ask_price = existing_asks[0][0]
                price = best_ask_price - 0.05  # Slightly lower
                qty = existing_asks[0][2]  # Same quantity as next level
                orders = max(1, existing_asks[0][1])
                return (price, orders, qty)
            
            elif index < len(existing_asks):
                # Interpolate between levels
                prev_level = existing_asks[index - 1]
                next_level = existing_asks[index]
                
                price = (prev_level[0] + next_level[0]) / 2
                qty = int((prev_level[2] + next_level[2]) / 2)
                orders = max(1, int((prev_level[1] + next_level[1]) / 2))
                
                return (price, orders, qty)
            
            else:
                # Extrapolate beyond existing levels
                if len(existing_asks) >= 2:
                    last_level = existing_asks[-1]
                    second_last = existing_asks[-2]
                    
                    price_diff = last_level[0] - second_last[0]
                    price = last_level[0] + price_diff
                    
                    # Reduce quantity for deeper levels
                    qty = max(50, int(last_level[2] * 0.8))
                    orders = max(1, last_level[1])
                    
                    return (price, orders, qty)
            
            return None
            
        except Exception as e:
            print(f"Error interpolating ask level: {e}")
            return None
    
    def _apply_consistency_checks(self, bids: List[Tuple], asks: List[Tuple], 
                                 current_price: float) -> Tuple[List[Tuple], List[Tuple]]:
        """Apply consistency checks to reconstructed order book"""
        try:
            # Ensure bid prices are descending
            bids.sort(key=lambda x: x[2], reverse=True)
            
            # Ensure ask prices are ascending
            asks.sort(key=lambda x: x[0])
            
            # Remove crossed orders (bids >= asks)
            if bids and asks:
                best_bid = bids[0][2]
                best_ask = asks[0][0]
                
                if best_bid >= best_ask:
                    # Remove crossed levels
                    bids = [bid for bid in bids if bid[2] < best_ask]
                    asks = [ask for ask in asks if ask[0] > best_bid]
            
            # Ensure reasonable spread
            if bids and asks:
                spread = asks[0][0] - bids[0][2]
                if spread < 0.01:  # Minimum 1 paisa spread
                    # Adjust prices slightly
                    mid_price = (asks[0][0] + bids[0][2]) / 2
                    bids[0] = (bids[0][0], bids[0][1], mid_price - 0.005)
                    asks[0] = (mid_price + 0.005, asks[0][1], asks[0][2])
            
            return bids, asks
            
        except Exception as e:
            print(f"Error in consistency checks: {e}")
            return bids, asks
    
    def _calculate_data_quality_score(self, original_bids: int, original_asks: int,
                                     reconstructed_bids: int, reconstructed_asks: int) -> float:
        """Calculate data quality score"""
        try:
            total_original = original_bids + original_asks
            total_reconstructed = reconstructed_bids + reconstructed_asks
            
            if total_reconstructed == 0:
                return 0.0
            
            # Base score from original data completeness
            completeness_score = (total_original / max(1, total_reconstructed)) * 100
            
            # Penalty for reconstruction
            reconstruction_penalty = (total_reconstructed - total_original) * 2
            
            # Final score
            quality_score = max(0, min(100, completeness_score - reconstruction_penalty))
            
            return quality_score
            
        except Exception as e:
            print(f"Error calculating quality score: {e}")
            return 50.0
    
    def _update_patterns(self, bids: List[Tuple], asks: List[Tuple], current_price: float):
        """Update historical patterns for future reconstruction"""
        try:
            # Update spread patterns
            if bids and asks:
                spread = asks[0][0] - bids[0][2]
                self.spread_patterns.append(spread)
            
            # Update imbalance patterns
            total_bid_qty = sum(bid[0] for bid in bids)
            total_ask_qty = sum(ask[2] for ask in asks)
            total_qty = total_bid_qty + total_ask_qty
            
            if total_qty > 0:
                imbalance = ((total_bid_qty - total_ask_qty) / total_qty) * 100
                self.imbalance_patterns.append(imbalance)
            
        except Exception as e:
            print(f"Error updating patterns: {e}")
    
    def get_reconstruction_stats(self) -> Dict:
        """Get reconstruction engine statistics"""
        try:
            return {
                'missing_data_count': self.missing_data_count,
                'reconstruction_count': self.reconstruction_count,
                'data_quality_score': self.data_quality_score,
                'interpolation_enabled': self.interpolation_enabled,
                'extrapolation_enabled': self.extrapolation_enabled,
                'pattern_matching_enabled': self.pattern_matching_enabled,
                'avg_spread': statistics.mean(self.spread_patterns) if self.spread_patterns else 0,
                'avg_imbalance': statistics.mean(self.imbalance_patterns) if self.imbalance_patterns else 0
            }
            
        except Exception as e:
            print(f"Error getting reconstruction stats: {e}")
            return {}
