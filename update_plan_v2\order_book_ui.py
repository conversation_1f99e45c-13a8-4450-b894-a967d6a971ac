"""
Order Book UI Module
Handles all order book display and UI management
Migrated to CustomTkinter for modern professional appearance
Enhanced with quantity bars and top 5 level highlighting
"""

import customtkinter as ctk
import tkinter as tk  # Keep for some constants if needed
import time
from tkinter import Canvas


class OrderBookUI:
    """Clean, modular order book UI component"""
    
    def __init__(self, parent_frame):
        """Initialize order book UI in the given parent frame"""
        self.parent_frame = parent_frame
        self.order_book_frame = None
        self.order_book_rows = []
        self.ui_update_lock = False
        self.last_update_time = 0
        
        # Create the UI structure
        self.create_order_book_container()
        self.create_stable_structure()
        self.create_top5_highlight_container()

        print("✅ OrderBookUI initialized successfully")
    
    def create_order_book_container(self):
        """Create the main order book container"""
        # Main order book frame with CustomTkinter
        self.order_book_frame = ctk.CTkFrame(self.parent_frame, corner_radius=8)
        self.order_book_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Header row with modern styling
        header_frame = ctk.CTkFrame(self.order_book_frame, height=35, corner_radius=6)
        header_frame.pack(fill=tk.X, pady=(5, 8), padx=5)
        header_frame.pack_propagate(False)
        
        # Configure header grid
        header_frame.grid_columnconfigure(0, weight=2, minsize=120)  # Qty (Bid)
        header_frame.grid_columnconfigure(1, weight=1, minsize=80)   # Orders (Bid)
        header_frame.grid_columnconfigure(2, weight=2, minsize=120)  # Buy Price
        header_frame.grid_columnconfigure(3, weight=2, minsize=120)  # Sell Price
        header_frame.grid_columnconfigure(4, weight=1, minsize=80)   # Orders (Ask)
        header_frame.grid_columnconfigure(5, weight=2, minsize=120)  # Qty (Ask)
        
        # Header labels with CustomTkinter styling
        headers = ['Qty', 'Orders', 'Buy Price', 'Sell Price', 'Orders', 'Qty']
        for i, header in enumerate(headers):
            label = ctk.CTkLabel(header_frame, text=header,
                               font=ctk.CTkFont(family="Consolas", size=12, weight="bold"),
                               anchor='center')
            label.grid(row=0, column=i, sticky='ew', padx=2, pady=5)
    
    def create_stable_structure(self):
        """Create stable 20-row structure for order book"""
        try:
            print("🔧 [ORDER_BOOK_UI] Creating 20-row structure...")
            
            # Clear any existing rows
            if self.order_book_rows:
                for row_data in self.order_book_rows:
                    if row_data['frame'].winfo_exists():
                        row_data['frame'].destroy()
            
            # Initialize tracking list
            self.order_book_rows = []
            
            # Create exactly 20 rows
            for i in range(20):
                # Alternating row colors
                if i % 2 == 0:
                    row_bg = '#1a1a1a'  # Darker for even rows
                else:
                    row_bg = '#151515'  # Lighter for odd rows
                
                # Create row frame with CustomTkinter - enhanced for top 5 highlighting
                is_top5 = i < 5
                if is_top5:
                    # Enhanced styling for top 5 levels
                    level_frame = ctk.CTkFrame(self.order_book_frame, height=30, corner_radius=6,
                                             border_width=1, border_color="#00ffff")
                else:
                    # Standard styling for other levels
                    level_frame = ctk.CTkFrame(self.order_book_frame, height=30, corner_radius=4)

                level_frame.pack(fill=tk.X, pady=1, padx=5)
                level_frame.pack_propagate(False)

                # Configure grid layout with fixed column widths
                level_frame.grid_columnconfigure(0, weight=2, minsize=120)  # Qty (Bid)
                level_frame.grid_columnconfigure(1, weight=1, minsize=80)   # Orders (Bid)
                level_frame.grid_columnconfigure(2, weight=2, minsize=120)  # Buy Price
                level_frame.grid_columnconfigure(3, weight=2, minsize=120)  # Sell Price
                level_frame.grid_columnconfigure(4, weight=1, minsize=80)   # Orders (Ask)
                level_frame.grid_columnconfigure(5, weight=2, minsize=120)  # Qty (Ask)

                # Add simple quantity progress bars for each level
                bid_progress = ctk.CTkProgressBar(level_frame, width=100, height=8, corner_radius=2)
                bid_progress.grid(row=0, column=0, sticky='w', padx=(5, 0), pady=(20, 0))
                bid_progress.set(0)  # Will be updated with actual data

                ask_progress = ctk.CTkProgressBar(level_frame, width=100, height=8, corner_radius=2)
                ask_progress.grid(row=0, column=5, sticky='e', padx=(0, 5), pady=(20, 0))
                ask_progress.set(0)  # Will be updated with actual data

                # Create column labels with CustomTkinter
                row_labels = []
                for col in range(6):
                    label = ctk.CTkLabel(level_frame, text="",
                                       font=ctk.CTkFont(family="Consolas", size=11),
                                       text_color=("gray60", "gray40"), anchor='center')
                    label.grid(row=0, column=col, sticky='ew', padx=2, pady=3)
                    row_labels.append(label)

                # Store row data for easy access
                self.order_book_rows.append({
                    'frame': level_frame,
                    'labels': row_labels,
                    'bid_progress': bid_progress,
                    'ask_progress': ask_progress,
                    'row_index': i,
                    'bg_color': row_bg,
                    'is_top5': is_top5
                })
            
            print(f"✅ [ORDER_BOOK_UI] Created {len(self.order_book_rows)} rows successfully")
            
        except Exception as e:
            print(f"❌ Error creating order book structure: {e}")
            import traceback
            traceback.print_exc()

    def create_top5_highlight_container(self):
        """Create subtle highlighting for top 5 levels using background color changes"""
        try:
            # Instead of a visible border, we'll use background color changes
            # This will be handled in the _update_rows method
            self.top5_highlight_enabled = True
            print("✅ Top 5 highlighting system enabled")

        except Exception as e:
            print(f"❌ Error setting up top 5 highlighting: {e}")
    
    def update_display(self, bids, asks):
        """Update order book display with new bid/ask data - ONLY UPDATE VALUES"""
        # Prevent simultaneous updates
        if self.ui_update_lock:
            return

        self.ui_update_lock = True

        try:
            # ONLY update the text values - NEVER recreate structure
            if self.order_book_rows and len(self.order_book_rows) == 20:
                self._update_rows(bids, asks)
            else:
                print("⚠️ [ORDER_BOOK_UI] Structure not ready, skipping update")

        except Exception as e:
            print(f"❌ Error updating order book display: {e}")
        finally:
            self.ui_update_lock = False
    
    def _update_rows(self, bids, asks):
        """Update individual rows with bid/ask data, quantity bars, and top 5 highlighting"""
        try:
            # Calculate max quantities for progress bar scaling
            max_bid_qty = max([bid[0] for bid in bids[:10]], default=1) if bids else 1
            max_ask_qty = max([ask[2] for ask in asks[:10]], default=1) if asks else 1
            max_qty = max(max_bid_qty, max_ask_qty)

            for i in range(20):  # Fixed 20 rows
                if i >= len(self.order_book_rows):
                    break

                row_data = self.order_book_rows[i]
                labels = row_data['labels']
                bid_progress = row_data['bid_progress']
                ask_progress = row_data['ask_progress']
                is_top5 = row_data['is_top5']

                # Get data for this level
                bid_data = bids[i] if i < len(bids) else None
                ask_data = asks[i] if i < len(asks) else None

                # Enhanced colors for top 5 levels
                if is_top5:
                    bid_color = '#00ff66' if bid_data else '#888888'
                    ask_color = '#ff3366' if ask_data else '#888888'
                    bid_progress.configure(progress_color='#00cc44')
                    ask_progress.configure(progress_color='#cc2244')
                else:
                    bid_color = '#00cc55' if bid_data else '#888888'
                    ask_color = '#cc2244' if ask_data else '#888888'
                    bid_progress.configure(progress_color='#008833')
                    ask_progress.configure(progress_color='#882233')

                # Update bid side (columns 0, 1, 2)
                if bid_data:
                    qty, orders, price = bid_data[0], bid_data[1], bid_data[2]
                    source = bid_data[3] if len(bid_data) > 3 else 'GENERATED'

                    # Only update text if it has changed
                    new_qty_text = f"{qty:,}"
                    new_orders_text = str(orders)
                    new_price_text = f"₹{price:.2f}"

                    if labels[0].cget('text') != new_qty_text:
                        labels[0].configure(text=new_qty_text, text_color=bid_color)
                    if labels[1].cget('text') != new_orders_text:
                        labels[1].configure(text=new_orders_text, text_color='#888888')
                    if labels[2].cget('text') != new_price_text:
                        labels[2].configure(text=new_price_text, text_color=bid_color)

                    # Update bid quantity progress bar
                    progress_value = min(qty / max_qty, 1.0) if max_qty > 0 else 0
                    bid_progress.set(progress_value)
                else:
                    # Clear bid side if no data
                    if labels[0].cget('text') != "":
                        labels[0].configure(text="", text_color='#888888')
                    if labels[1].cget('text') != "":
                        labels[1].configure(text="", text_color='#888888')
                    if labels[2].cget('text') != "":
                        labels[2].configure(text="", text_color='#888888')
                    bid_progress.set(0)

                # Update ask side (columns 3, 4, 5)
                if ask_data:
                    price, orders, qty = ask_data[0], ask_data[1], ask_data[2]
                    source = ask_data[3] if len(ask_data) > 3 else 'GENERATED'

                    # Only update text if it has changed
                    new_price_text = f"₹{price:.2f}"
                    new_orders_text = str(orders)
                    new_qty_text = f"{qty:,}"

                    if labels[3].cget('text') != new_price_text:
                        labels[3].configure(text=new_price_text, text_color=ask_color)
                    if labels[4].cget('text') != new_orders_text:
                        labels[4].configure(text=new_orders_text, text_color='#888888')
                    if labels[5].cget('text') != new_qty_text:
                        labels[5].configure(text=new_qty_text, text_color=ask_color)

                    # Update ask quantity progress bar
                    progress_value = min(qty / max_qty, 1.0) if max_qty > 0 else 0
                    ask_progress.set(progress_value)
                else:
                    # Clear ask side if no data
                    if labels[3].cget('text') != "":
                        labels[3].configure(text="", text_color='#888888')
                    if labels[4].cget('text') != "":
                        labels[4].configure(text="", text_color='#888888')
                    if labels[5].cget('text') != "":
                        labels[5].configure(text="", text_color='#888888')
                    ask_progress.set(0)
                    
        except Exception as e:
            print(f"❌ Error updating rows: {e}")
            import traceback
            traceback.print_exc()
    
    def clear_display(self):
        """Clear all order book data"""
        try:
            for row_data in self.order_book_rows:
                for label in row_data['labels']:
                    label.configure(text="", text_color='#888888')
            print("✅ [ORDER_BOOK_UI] Display cleared")
        except Exception as e:
            print(f"❌ Error clearing display: {e}")
    
    def get_frame(self):
        """Get the main order book frame for external access"""
        return self.order_book_frame
