"""
GUI Components for CGCL Trading System
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
from typing import Dict, Optional

from config.settings import COLORS
from core.data_structures import PerformanceMetrics
from utils.market_hours import market_hours


class PerformancePanel(tk.Frame):
    """Performance metrics display panel"""
    
    def __init__(self, parent):
        super().__init__(parent, bg=COLORS['bg_medium'], relief=tk.RAISED, bd=1)
        self.create_widgets()
        
    def create_widgets(self):
        """Create performance widgets"""
        # Title
        title_label = tk.Label(
            self,
            text="📊 Performance Metrics",
            bg=COLORS['bg_medium'],
            fg=COLORS['text_white'],
            font=('Arial', 12, 'bold')
        )
        title_label.pack(pady=(5, 10))
        
        # Metrics frame
        metrics_frame = tk.Frame(self, bg=COLORS['bg_medium'])
        metrics_frame.pack(fill=tk.X, padx=10, pady=(0, 5))
        
        # Latency
        self.latency_label = tk.Label(
            metrics_frame,
            text="Latency: --ms",
            bg=COLORS['bg_medium'],
            fg=COLORS['text_gray'],
            font=('Arial', 10)
        )
        self.latency_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # Update rate
        self.rate_label = tk.Label(
            metrics_frame,
            text="Rate: --Hz",
            bg=COLORS['bg_medium'],
            fg=COLORS['text_gray'],
            font=('Arial', 10)
        )
        self.rate_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # Data quality
        self.quality_label = tk.Label(
            metrics_frame,
            text="Quality: --%",
            bg=COLORS['bg_medium'],
            fg=COLORS['text_gray'],
            font=('Arial', 10)
        )
        self.quality_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # Connection status
        self.connection_label = tk.Label(
            metrics_frame,
            text="Status: --",
            bg=COLORS['bg_medium'],
            fg=COLORS['text_gray'],
            font=('Arial', 10)
        )
        self.connection_label.pack(side=tk.LEFT)
    
    def update_metrics(self, metrics: PerformanceMetrics):
        """Update performance metrics display"""
        try:
            # Latency
            latency_color = COLORS['text_green'] if metrics.latency_ms < 50 else COLORS['text_gray'] if metrics.latency_ms < 100 else COLORS['ask_red']
            self.latency_label.config(
                text=f"Latency: {metrics.latency_ms:.1f}ms",
                fg=latency_color
            )
            
            # Update rate
            rate_color = COLORS['text_green'] if metrics.update_rate_hz >= 1 else COLORS['ask_red']
            self.rate_label.config(
                text=f"Rate: {metrics.update_rate_hz:.1f}Hz",
                fg=rate_color
            )
            
            # Data quality
            quality_color = COLORS['text_green'] if metrics.data_quality_percent >= 80 else COLORS['text_gray'] if metrics.data_quality_percent >= 60 else COLORS['ask_red']
            self.quality_label.config(
                text=f"Quality: {metrics.data_quality_percent:.0f}%",
                fg=quality_color
            )
            
            # Connection status
            status_color = COLORS['text_green'] if metrics.connection_status == "CONNECTED" else COLORS['ask_red']
            self.connection_label.config(
                text=f"Status: {metrics.connection_status}",
                fg=status_color
            )
            
        except Exception as e:
            print(f"Error updating performance metrics: {e}")


class StatusPanel(tk.Frame):
    """Market status display panel"""
    
    def __init__(self, parent):
        super().__init__(parent, bg=COLORS['bg_medium'], relief=tk.RAISED, bd=1)
        self.create_widgets()
        
    def create_widgets(self):
        """Create status widgets"""
        # Title
        title_label = tk.Label(
            self,
            text="📈 Market Status",
            bg=COLORS['bg_medium'],
            fg=COLORS['text_white'],
            font=('Arial', 12, 'bold')
        )
        title_label.pack(pady=(5, 10))
        
        # Status frame
        status_frame = tk.Frame(self, bg=COLORS['bg_medium'])
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 5))
        
        # Symbol
        self.symbol_label = tk.Label(
            status_frame,
            text="CGCL",
            bg=COLORS['bg_medium'],
            fg=COLORS['text_white'],
            font=('Arial', 14, 'bold')
        )
        self.symbol_label.pack()
        
        # Price
        self.price_label = tk.Label(
            status_frame,
            text="₹---.--",
            bg=COLORS['bg_medium'],
            fg=COLORS['text_white'],
            font=('Arial', 16, 'bold')
        )
        self.price_label.pack()
        
        # Change
        self.change_label = tk.Label(
            status_frame,
            text="---.-- (---.--%) ",
            bg=COLORS['bg_medium'],
            fg=COLORS['text_gray'],
            font=('Arial', 12)
        )
        self.change_label.pack()
        
        # Volume
        self.volume_label = tk.Label(
            status_frame,
            text="Vol: --",
            bg=COLORS['bg_medium'],
            fg=COLORS['text_gray'],
            font=('Arial', 10)
        )
        self.volume_label.pack()
        
        # Update count
        self.update_label = tk.Label(
            status_frame,
            text="Updates: --",
            bg=COLORS['bg_medium'],
            fg=COLORS['text_gray'],
            font=('Arial', 10)
        )
        self.update_label.pack()
    
    def update_status(self, status: Dict):
        """Update market status display with market hours awareness"""
        try:
            # Symbol
            self.symbol_label.config(text=status.get('symbol', 'CGCL'))

            # Get market status
            market_status = market_hours.get_market_status()

            # Price handling based on market status
            price = status.get('price', 0.0)

            if market_status['should_process_data'] and price > 0:
                # Market is open and we have valid price
                self.price_label.config(text=f"₹{price:.2f}")

                # Change
                change = status.get('change', 0.0)
                change_pct = status.get('change_pct', 0.0)
                change_color = COLORS['text_green'] if change >= 0 else COLORS['ask_red']
                change_symbol = "+" if change >= 0 else ""

                self.change_label.config(
                    text=f"{change_symbol}{change:.2f} ({change_symbol}{change_pct:.2f}%)",
                    fg=change_color
                )

                # Volume and updates
                volume = status.get('volume', 0)
                self.volume_label.config(text=f"Vol: {volume:,}")

                update_count = status.get('update_count', 0)
                self.update_label.config(text=f"Updates: {update_count:,}")

            else:
                # Market is closed, show appropriate display
                display_price, price_source = market_hours.get_display_price(price if price > 0 else None)
                self.price_label.config(text=f"₹{display_price:.2f}")

                # Show market status instead of invalid change
                if price_source == "CLOSED":
                    self.change_label.config(text="Market Closed", fg=COLORS['ask_red'])
                elif price_source == "LAST_KNOWN":
                    self.change_label.config(text="Last Known Price", fg=COLORS['text_gray'])
                else:
                    self.change_label.config(text="Estimated Price", fg=COLORS['text_gray'])

                # Show dashes for volume and updates when market is closed
                self.volume_label.config(text="Vol: --")
                self.update_label.config(text="Updates: --")

        except Exception as e:
            print(f"Error updating status: {e}")


class AnalyticsPanel(tk.Frame):
    """Analytics display panel"""
    
    def __init__(self, parent):
        super().__init__(parent, bg=COLORS['analysis_bg'], relief=tk.RAISED, bd=1)
        self.create_widgets()
        
    def create_widgets(self):
        """Create analytics widgets"""
        # Title
        title_label = tk.Label(
            self,
            text="🔍 Flow Analytics",
            bg=COLORS['analysis_bg'],
            fg=COLORS['text_white'],
            font=('Arial', 12, 'bold')
        )
        title_label.pack(pady=(5, 10))
        
        # Analytics frame
        analytics_frame = tk.Frame(self, bg=COLORS['analysis_bg'])
        analytics_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 5))
        
        # Imbalance
        self.imbalance_label = tk.Label(
            analytics_frame,
            text="Imbalance: --%",
            bg=COLORS['analysis_bg'],
            fg=COLORS['text_gray'],
            font=('Arial', 10)
        )
        self.imbalance_label.pack(anchor=tk.W)
        
        # Flow momentum
        self.momentum_label = tk.Label(
            analytics_frame,
            text="Momentum: --",
            bg=COLORS['analysis_bg'],
            fg=COLORS['text_gray'],
            font=('Arial', 10)
        )
        self.momentum_label.pack(anchor=tk.W)
        
        # Prediction
        self.prediction_label = tk.Label(
            analytics_frame,
            text="Prediction: --",
            bg=COLORS['analysis_bg'],
            fg=COLORS['text_gray'],
            font=('Arial', 10)
        )
        self.prediction_label.pack(anchor=tk.W)
        
        # Signals
        self.signals_label = tk.Label(
            analytics_frame,
            text="Signals: --",
            bg=COLORS['analysis_bg'],
            fg=COLORS['text_gray'],
            font=('Arial', 10)
        )
        self.signals_label.pack(anchor=tk.W)
    
    def update_analytics(self, analytics: Dict):
        """Update analytics display"""
        try:
            # Imbalance
            imbalance = analytics.get('basic_imbalance', {}).get('quantity_imbalance', 0)
            imbalance_color = COLORS['text_green'] if imbalance > 5 else COLORS['ask_red'] if imbalance < -5 else COLORS['text_gray']
            self.imbalance_label.config(
                text=f"Imbalance: {imbalance:+.1f}%",
                fg=imbalance_color
            )
            
            # Momentum
            momentum = analytics.get('flow_momentum', {}).get('momentum', 0)
            momentum_color = COLORS['text_green'] if momentum > 1 else COLORS['ask_red'] if momentum < -1 else COLORS['text_gray']
            self.momentum_label.config(
                text=f"Momentum: {momentum:+.1f}",
                fg=momentum_color
            )
            
            # Prediction
            prediction = analytics.get('prediction', {})
            direction = prediction.get('direction', 'NEUTRAL')
            confidence = prediction.get('confidence', 0)
            pred_color = COLORS['text_green'] if direction == 'BULLISH' else COLORS['ask_red'] if direction == 'BEARISH' else COLORS['text_gray']
            self.prediction_label.config(
                text=f"Prediction: {direction} ({confidence:.0f}%)",
                fg=pred_color
            )
            
            # Signals
            signal_count = len(analytics.get('signals', []))
            self.signals_label.config(
                text=f"Signals: {signal_count}",
                fg=COLORS['text_white'] if signal_count > 0 else COLORS['text_gray']
            )
            
        except Exception as e:
            print(f"Error updating analytics: {e}")


class ClockWidget(tk.Frame):
    """Enhanced digital clock widget with trading session info"""

    def __init__(self, parent):
        super().__init__(parent, bg=COLORS['bg_medium'], relief=tk.RAISED, bd=1)
        self.create_widgets()
        self.update_clock()

    def create_widgets(self):
        """Create clock widgets"""
        # Time display
        self.time_label = tk.Label(
            self,
            bg=COLORS['bg_medium'],
            fg=COLORS['text_white'],
            font=('Arial', 14, 'bold')
        )
        self.time_label.pack(pady=(5, 2))

        # Date display
        self.date_label = tk.Label(
            self,
            bg=COLORS['bg_medium'],
            fg=COLORS['text_gray'],
            font=('Arial', 10)
        )
        self.date_label.pack(pady=(0, 2))

        # Market session display
        self.session_label = tk.Label(
            self,
            bg=COLORS['bg_medium'],
            fg=COLORS['text_gray'],
            font=('Arial', 9)
        )
        self.session_label.pack(pady=(0, 5))

    def update_clock(self):
        """Update clock display with trading session info"""
        try:
            now = datetime.now()

            # Update time
            current_time = now.strftime("%H:%M:%S")
            self.time_label.config(text=f"🕐 {current_time}")

            # Update date
            current_date = now.strftime("%d %b %Y")
            self.date_label.config(text=current_date)

            # Update market session
            session_info = self._get_market_session_info(now)
            self.session_label.config(
                text=session_info['text'],
                fg=session_info['color']
            )

            # Schedule next update
            self.after(1000, self.update_clock)

        except Exception as e:
            print(f"Error updating clock: {e}")

    def _get_market_session_info(self, now: datetime) -> Dict:
        """Get market session information"""
        try:
            hour = now.hour
            minute = now.minute
            weekday = now.weekday()  # 0=Monday, 6=Sunday

            # Check if it's a weekend
            if weekday >= 5:  # Saturday or Sunday
                return {
                    'text': '📅 Weekend - Market Closed',
                    'color': COLORS['text_gray']
                }

            # Market hours: 9:15 AM to 3:30 PM
            market_start_hour, market_start_min = 9, 15
            market_end_hour, market_end_min = 15, 30

            current_minutes = hour * 60 + minute
            market_start_minutes = market_start_hour * 60 + market_start_min
            market_end_minutes = market_end_hour * 60 + market_end_min

            if current_minutes < market_start_minutes:
                # Pre-market
                time_to_open = market_start_minutes - current_minutes
                hours_to_open = time_to_open // 60
                mins_to_open = time_to_open % 60

                if hours_to_open > 0:
                    time_text = f"{hours_to_open}h {mins_to_open}m"
                else:
                    time_text = f"{mins_to_open}m"

                return {
                    'text': f'⏰ Opens in {time_text}',
                    'color': COLORS['text_gray']
                }

            elif current_minutes <= market_end_minutes:
                # Market is open
                time_to_close = market_end_minutes - current_minutes
                hours_to_close = time_to_close // 60
                mins_to_close = time_to_close % 60

                if hours_to_close > 0:
                    time_text = f"{hours_to_close}h {mins_to_close}m"
                else:
                    time_text = f"{mins_to_close}m"

                # Color coding based on time left
                if time_to_close <= 30:  # Last 30 minutes
                    color = COLORS['ask_red']
                    status = '🔴 CLOSING SOON'
                elif time_to_close <= 60:  # Last hour
                    color = '#FFA500'  # Orange
                    status = '🟡 MARKET OPEN'
                else:
                    color = COLORS['text_green']
                    status = '🟢 MARKET OPEN'

                return {
                    'text': f'{status} - {time_text} left',
                    'color': color
                }

            else:
                # Post-market
                return {
                    'text': '🔴 Market Closed',
                    'color': COLORS['ask_red']
                }

        except Exception as e:
            print(f"Error getting market session info: {e}")
            return {
                'text': '❓ Unknown Session',
                'color': COLORS['text_gray']
            }


class VolumeBar(tk.Canvas):
    """Volume bar visualization"""
    
    def __init__(self, parent, width=100, height=20):
        super().__init__(
            parent,
            width=width,
            height=height,
            bg=COLORS['bg_dark'],
            highlightthickness=0
        )
        self.width = width
        self.height = height
        
    def update_volume(self, volume: int, max_volume: int = 10000):
        """Update volume bar"""
        try:
            self.delete("all")
            
            if max_volume > 0:
                # Calculate bar width
                bar_width = min(self.width, (volume / max_volume) * self.width)
                
                # Choose color based on volume level
                if volume > max_volume * 0.8:
                    color = COLORS['ask_red']
                elif volume > max_volume * 0.5:
                    color = COLORS['text_gray']
                else:
                    color = COLORS['text_green']
                
                # Draw bar
                self.create_rectangle(
                    0, 2, bar_width, self.height - 2,
                    fill=color, outline=""
                )
                
                # Draw border
                self.create_rectangle(
                    0, 0, self.width, self.height,
                    outline=COLORS['border'], width=1
                )
                
        except Exception as e:
            print(f"Error updating volume bar: {e}")
