"""
Quick launcher for UI Builder Tool
"""

import subprocess
import sys
import os

def main():
    print("🎨 CGCL UI Builder Tool")
    print("=" * 50)
    print("🔧 Drag & Drop Dashboard Designer")
    print("📦 Component Tray with all trading components")
    print("🎨 Visual design canvas with grid")
    print("🖱️ Drag, drop, resize, and position components")
    print("💾 Save/Load layouts as JSON")
    print("🚀 Generate complete dashboard code")
    print("=" * 50)
    print()
    
    try:
        # Launch the UI builder
        subprocess.run([sys.executable, "ui_builder_tool.py"], cwd=os.path.dirname(__file__))
    except Exception as e:
        print(f"❌ Error launching UI builder: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
