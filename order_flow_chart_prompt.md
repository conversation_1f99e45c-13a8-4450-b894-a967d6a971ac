# Prompt for AI: Create a Real-Time Market Depth Chart

## Objective

Create a real-time, interactive market depth chart application using Python. The user interface should closely replicate the design and functionality of the provided image. The application will visualize live order book data, showing the cumulative depth of buy (bids) and sell (asks) orders.

---

## Key Features & UI Components

### 1. Main Window
- **Theme:** Use a dark theme with a charcoal/dark-gray background (`#2b3e50` or similar).
- **Title:** Display a prominent title "DEPTH CHART" at the top left.
- **Tabs:** Implement two tabs at the top right: "Price chart" and "Depth chart". The "Depth chart" tab should be the default and appear selected (e.g., with an underline).

### 2. Mid-Market Price Display
- **Location:** Centered horizontally, below the main title.
- **Content:**
    - Display the "Mid Market Price" in a large, bold, white font.
    - Show a sample price like `3,894.185`.
    - Include circular "➖" (minus) and "➕" (plus) buttons on either side of the price. These will be used for adjusting the chart's zoom/scale later.
    - Add a label "Mid Market Price" below the numerical value.

### 3. Depth Chart Visualization
This is the core component. It's a two-sided, filled, stepped area chart.

- **General:**
    - The chart is split vertically down the middle.
    - The left side represents the **Bids** (buy orders).
    - The right side represents the **Asks** (sell orders).

- **Bids Side (Left):**
    - **Chart Type:** A filled, stepped area plot.
    - **Color:** Use a bright green line (`#2ecc71` or similar) with a semi-transparent, darker green fill.
    - **Data Logic:** This side plots the cumulative quantity of all buy orders at or *below* each price point. The line should step downwards as the price moves away (left) from the center.

- **Asks Side (Right):**
    - **Chart Type:** A filled, stepped area plot.
    - **Color:** Use a bright red/orange line (`#e74c3c` or similar) with a semi-transparent, darker red/orange fill.
    - **Data Logic:** This side plots the cumulative quantity of all sell orders at or *above* each price point. The line should step upwards as the price moves away (right) from the center.

- **Axes:**
    - **X-Axis (Price):**
        - Located at the bottom of the chart.
        - Displays the price levels.
        - The price should increase from left to right.
    - **Y-Axis (Cumulative Quantity):**
        - Displayed on both the left and right sides of the chart for clarity.
        - Represents the cumulative volume of orders.
        - Starts at 0 at the bottom and increases upwards.

---

## Data Processing Logic

The application needs to process order book data, which typically consists of a list of bids and asks `(price, quantity)`.

1.  **Receive Order Book Data:** Assume you have two lists:
    - `bids = [(price, quantity), ...]`
    - `asks = [(price, quantity), ...]`

2.  **Calculate Cumulative Bids:**
    - Sort the `bids` list by price in **descending** order.
    - Create a new list for the cumulative depth. Iterate through the sorted bids, calculating the cumulative quantity at each price step.
    - The result should be a list of `(price, cumulative_quantity)` points for the green side of the chart.

3.  **Calculate Cumulative Asks:**
    - Sort the `asks` list by price in **ascending** order.
    - Create a new list for the cumulative depth. Iterate through the sorted asks, calculating the cumulative quantity at each price step.
    - The result should be a list of `(price, cumulative_quantity)` points for the red side of the chart.

4.  **Real-Time Updates:** The chart must update dynamically whenever new order book data is received. The entire calculation process should be re-run, and the chart should be redrawn efficiently to avoid flickering.

---

## Suggested Technology Stack

- **Language:** Python
- **GUI Framework:**
    - **Option 1 (Web-based):** Use `Dash` with `Plotly`. Plotly's `graph_objects` are perfect for creating this type of chart (`go.Scatter` with `fill='tozeroy'` and `line_shape='hv'`). This is highly recommended.
    - **Option 2 (Desktop):** Use a modern desktop GUI library like `CustomTkinter` or `PyQt6`. You can embed a `Matplotlib` or `Plotly` chart within the application window.
- **Data Simulation:** For development, create a simulator that generates random order book data every second to mimic a live feed.

## Implementation Steps

1.  **Set up the basic UI layout:** Create the main window, title, tabs, and the placeholder for the mid-market price.
2.  **Implement the data processing functions:** Write Python functions to calculate cumulative bid and ask depths from raw order book data.
3.  **Create the chart:** Use your chosen library to plot the two datasets (bids and asks) as filled, stepped area charts on the same graph.
4.  **Style the chart:** Apply the specified colors, labels, and themes to match the image.
5.  **Integrate real-time updates:** Set up a loop or callback (e.g., `dcc.Interval` in Dash) to fetch new data, re-calculate the depth, and update the chart periodically.
6.  **Add interactivity:** Connect the "➕" and "➖" buttons to functions that adjust the visible range of the X-axis (price) to allow zooming.
