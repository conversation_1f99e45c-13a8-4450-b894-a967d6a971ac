"""
Install Dependencies for CGCL Advanced Market Depth
===================================================

This script installs all required dependencies for the CGCL market depth system.
"""

import subprocess
import sys

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} installed successfully")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ Failed to install {package}")
        return False

def main():
    """Install all required packages"""
    print("🔧 INSTALLING CGCL MARKET DEPTH DEPENDENCIES")
    print("=" * 50)
    
    packages = [
        "numpy",
        "websocket-client",  # For WebSocket connections
        "SmartApi",  # Fixed package name for Smart API
        "pyotp"  # For TOTP generation
    ]
    
    success_count = 0
    
    for package in packages:
        print(f"\n📦 Installing {package}...")
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"✅ {success_count}/{len(packages)} packages installed successfully")
    
    if success_count == len(packages):
        print("\n🎉 All dependencies installed!")
        print("You can now run: python cgcl_advanced_depth.py")
    else:
        print("\n⚠️ Some packages failed to install")
        print("Please install them manually using:")
        for package in packages:
            print(f"pip install {package}")

if __name__ == "__main__":
    main()
