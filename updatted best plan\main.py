#!/usr/bin/env python3
"""
CGCL Advanced Trading System - Main Entry Point
Professional-grade order book analysis with real-time WebSocket data
"""

import sys
import os
import traceback
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.main_window import CGC<PERSON>ainWindow
from config.settings import SYSTEM_CONFIG


def print_startup_banner():
    """Print system startup banner"""
    print("🚀 CGCL LIVE TRADING SYSTEM - SMART API WEBSOCKET")
    print("=" * 80)
    print("⚠️  CRITICAL: SMART API REQUIRED FOR LIVE TRADING")
    print("✅ 100% WebSocket streaming (no HTTP polling)")
    print("✅ Real-time Angel One data (10-50ms latency)")
    print("✅ Live CGCL order flow analysis")
    print("✅ Instant 30-minute predictions")
    print("✅ Professional trading performance")
    print("✅ Auto-reconnection on connection loss")
    print("🚫 Demo mode NOT suitable for real trading")
    print("=" * 80)
    print()


def main():
    """Main application entry point"""
    try:
        print_startup_banner()
        
        print("🔑 Checking Smart API credentials...")
        print("🔌 Establishing LIVE WebSocket connections...")
        print("📡 Starting real-time trading data...")
        print("📊 Opening LIVE CGCL trading system...")
        
        # Initialize main application
        print("🚀 Initializing CGCL Advanced Trading System...")
        app = CGCLMainWindow()
        print("✅ Application initialized successfully")
        
        # Start the application
        print("🖥️ Starting GUI main loop...")
        app.run()
        
    except KeyboardInterrupt:
        print("\n⏹️ Application stopped by user")
    except Exception as e:
        print(f"\n❌ Critical Error: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
