"""
Order Flow Imbalance Detection and Analysis
"""

import statistics
from collections import deque
from datetime import datetime
from typing import Dict, List, Tuple, Optional

from core.data_structures import OrderBookSnapshot, FlowAnalysis, TradingSignal
from config.settings import SIGNAL_CONFIG


class OrderFlowImbalanceDetector:
    """Advanced order flow imbalance detection for predicting price movements"""
    
    def __init__(self):
        # Imbalance tracking
        self.imbalance_history = deque(maxlen=300)  # 5 minutes of history
        self.flow_pressure_history = deque(maxlen=100)
        self.volume_imbalance_history = deque(maxlen=100)
        
        # Detection thresholds
        self.thresholds = {
            'strong_bullish': 25,      # >25% imbalance = strong bullish
            'bullish': 10,             # >10% imbalance = bullish
            'neutral_high': 5,         # 5-10% = weak bias
            'neutral_low': -5,         # -5 to 5% = neutral
            'bearish': -10,            # <-10% imbalance = bearish
            'strong_bearish': -25      # <-25% imbalance = strong bearish
        }
        
        # Flow analysis
        self.flow_momentum = 0.0
        self.flow_acceleration = 0.0
        self.flow_divergence = 0.0
        
        # Prediction metrics
        self.price_direction_confidence = 0.0
        self.predicted_direction = "NEUTRAL"
        self.prediction_strength = 0.0
        
        # Advanced analytics
        self.institutional_flow_detected = False
        self.retail_flow_detected = False
        self.algorithmic_flow_detected = False
        
    def analyze_order_flow_imbalance(self, snapshot: OrderBookSnapshot) -> FlowAnalysis:
        """Comprehensive order flow imbalance analysis"""
        try:
            # Convert snapshot data to the format expected by analysis
            bids = [(level.quantity, level.orders, level.price) for level in snapshot.bids]
            asks = [(level.price, level.orders, level.quantity) for level in snapshot.asks]
            current_price = snapshot.mid_price
            
            # Basic imbalance calculation
            basic_imbalance = self._calculate_basic_imbalance(bids, asks)
            
            # Volume-weighted imbalance
            volume_weighted_imbalance = self._calculate_volume_weighted_imbalance(bids, asks, current_price)
            
            # Depth-based imbalance
            depth_imbalance = self._calculate_depth_imbalance(bids, asks, current_price)
            
            # Flow momentum analysis
            flow_momentum = self._calculate_flow_momentum(basic_imbalance)
            
            # Price direction prediction
            prediction = self._predict_price_direction(basic_imbalance, volume_weighted_imbalance, flow_momentum)
            
            # Institutional flow analysis
            institutional_analysis = self._analyze_institutional_flow(bids, asks)
            
            # Generate trading signals
            signals = self._generate_flow_signals(basic_imbalance, flow_momentum, institutional_analysis, current_price)
            
            # Create flow analysis result
            analysis = FlowAnalysis(
                timestamp=snapshot.timestamp,
                basic_imbalance=basic_imbalance,
                volume_weighted_imbalance=volume_weighted_imbalance,
                depth_imbalance=depth_imbalance,
                flow_momentum=flow_momentum,
                prediction=prediction,
                institutional_analysis=institutional_analysis,
                signals=signals
            )
            
            # Store in history
            self.imbalance_history.append(analysis)
            
            return analysis
            
        except Exception as e:
            print(f"Error analyzing order flow imbalance: {e}")
            return FlowAnalysis(
                timestamp=datetime.now(),
                basic_imbalance={},
                volume_weighted_imbalance={},
                depth_imbalance={},
                flow_momentum={},
                prediction={},
                institutional_analysis={},
                signals=[]
            )
    
    def _calculate_basic_imbalance(self, bids: List[Tuple], asks: List[Tuple]) -> Dict:
        """Calculate basic order book imbalance"""
        try:
            if not bids or not asks:
                return {}
            
            # Total quantities
            total_bid_qty = sum(bid[0] for bid in bids)  # (qty, orders, price)
            total_ask_qty = sum(ask[2] for ask in asks)  # (price, orders, qty)
            total_qty = total_bid_qty + total_ask_qty
            
            # Basic imbalance
            imbalance = ((total_bid_qty - total_ask_qty) / total_qty) * 100 if total_qty > 0 else 0
            
            # Order count imbalance
            total_bid_orders = sum(bid[1] for bid in bids)
            total_ask_orders = sum(ask[1] for ask in asks)
            total_orders = total_bid_orders + total_ask_orders
            
            order_imbalance = ((total_bid_orders - total_ask_orders) / total_orders) * 100 if total_orders > 0 else 0
            
            # Classify imbalance
            classification = self._classify_imbalance(imbalance)
            
            return {
                'quantity_imbalance': imbalance,
                'order_imbalance': order_imbalance,
                'total_bid_qty': total_bid_qty,
                'total_ask_qty': total_ask_qty,
                'total_bid_orders': total_bid_orders,
                'total_ask_orders': total_ask_orders,
                'classification': classification,
                'strength': abs(imbalance)
            }
            
        except Exception as e:
            print(f"Error calculating basic imbalance: {e}")
            return {}
    
    def _calculate_volume_weighted_imbalance(self, bids: List[Tuple], asks: List[Tuple], current_price: float) -> Dict:
        """Calculate volume-weighted imbalance based on distance from current price"""
        try:
            if not bids or not asks:
                return {}
            
            # Weight orders by proximity to current price
            weighted_bid_volume = 0
            weighted_ask_volume = 0
            
            for qty, orders, price in bids:
                distance_factor = 1 / (1 + abs(current_price - price) / current_price)
                weighted_bid_volume += qty * distance_factor
            
            for price, orders, qty in asks:
                distance_factor = 1 / (1 + abs(price - current_price) / current_price)
                weighted_ask_volume += qty * distance_factor
            
            total_weighted = weighted_bid_volume + weighted_ask_volume
            weighted_imbalance = ((weighted_bid_volume - weighted_ask_volume) / total_weighted) * 100 if total_weighted > 0 else 0
            
            # Near-touch imbalance (top 3 levels)
            near_bid_volume = sum(bid[0] for bid in bids[:3])
            near_ask_volume = sum(ask[2] for ask in asks[:3])
            near_total = near_bid_volume + near_ask_volume
            near_touch_imbalance = ((near_bid_volume - near_ask_volume) / near_total) * 100 if near_total > 0 else 0
            
            return {
                'weighted_imbalance': weighted_imbalance,
                'near_touch_imbalance': near_touch_imbalance,
                'weighted_bid_volume': weighted_bid_volume,
                'weighted_ask_volume': weighted_ask_volume,
                'near_bid_volume': near_bid_volume,
                'near_ask_volume': near_ask_volume
            }
            
        except Exception as e:
            print(f"Error calculating volume weighted imbalance: {e}")
            return {}
    
    def _calculate_depth_imbalance(self, bids: List[Tuple], asks: List[Tuple], current_price: float) -> Dict:
        """Calculate imbalance at different depth levels"""
        try:
            if not bids or not asks:
                return {}
            
            depth_analysis = {}
            
            # Analyze imbalance at different depth levels
            for depth in [3, 5, 10, 20]:
                depth_bids = bids[:depth] if len(bids) >= depth else bids
                depth_asks = asks[:depth] if len(asks) >= depth else asks
                
                bid_qty = sum(bid[0] for bid in depth_bids)
                ask_qty = sum(ask[2] for ask in depth_asks)
                total_qty = bid_qty + ask_qty
                
                if total_qty > 0:
                    imbalance = ((bid_qty - ask_qty) / total_qty) * 100
                    depth_analysis[f'depth_{depth}'] = {
                        'imbalance': imbalance,
                        'bid_qty': bid_qty,
                        'ask_qty': ask_qty,
                        'levels_used': len(depth_bids) + len(depth_asks)
                    }
            
            # Calculate depth consistency
            imbalances = [data['imbalance'] for data in depth_analysis.values()]
            consistency = 100 - (statistics.stdev(imbalances) if len(imbalances) > 1 else 0)
            
            return {
                'depth_levels': depth_analysis,
                'consistency_score': consistency,
                'trend': 'CONSISTENT' if consistency > 80 else 'VARIABLE'
            }
            
        except Exception as e:
            print(f"Error calculating depth imbalance: {e}")
            return {}
    
    def _calculate_flow_momentum(self, basic_imbalance: Dict) -> Dict:
        """Calculate order flow momentum and acceleration"""
        try:
            if not basic_imbalance:
                return {}
            
            current_imbalance = basic_imbalance.get('quantity_imbalance', 0)
            
            # Store current imbalance
            self.flow_pressure_history.append(current_imbalance)
            
            if len(self.flow_pressure_history) < 2:
                return {'momentum': 0, 'acceleration': 0, 'trend': 'INSUFFICIENT_DATA'}
            
            # Calculate momentum (rate of change)
            if len(self.flow_pressure_history) >= 5:
                recent_avg = statistics.mean(list(self.flow_pressure_history)[-5:])
                older_avg = statistics.mean(list(self.flow_pressure_history)[-10:-5]) if len(self.flow_pressure_history) >= 10 else recent_avg
                momentum = recent_avg - older_avg
            else:
                momentum = current_imbalance - self.flow_pressure_history[-2]
            
            # Calculate acceleration (rate of change of momentum)
            if hasattr(self, 'previous_momentum'):
                acceleration = momentum - self.previous_momentum
            else:
                acceleration = 0
            
            self.previous_momentum = momentum
            
            # Determine trend
            if momentum > 2:
                trend = 'ACCELERATING_BULLISH'
            elif momentum > 0.5:
                trend = 'BULLISH'
            elif momentum < -2:
                trend = 'ACCELERATING_BEARISH'
            elif momentum < -0.5:
                trend = 'BEARISH'
            else:
                trend = 'NEUTRAL'
            
            return {
                'momentum': momentum,
                'acceleration': acceleration,
                'trend': trend,
                'momentum_strength': abs(momentum),
                'samples_used': len(self.flow_pressure_history)
            }
            
        except Exception as e:
            print(f"Error calculating flow momentum: {e}")
            return {}
    
    def _predict_price_direction(self, basic_imbalance: Dict, volume_weighted_imbalance: Dict, flow_momentum: Dict) -> Dict:
        """Predict price direction based on order flow analysis"""
        try:
            # Get key metrics
            imbalance = basic_imbalance.get('quantity_imbalance', 0)
            weighted_imbalance = volume_weighted_imbalance.get('weighted_imbalance', 0)
            near_touch_imbalance = volume_weighted_imbalance.get('near_touch_imbalance', 0)
            momentum = flow_momentum.get('momentum', 0)
            
            # Calculate prediction confidence
            factors = []
            
            # Factor 1: Basic imbalance strength
            if abs(imbalance) > 20:
                factors.append(0.3)
            elif abs(imbalance) > 10:
                factors.append(0.2)
            else:
                factors.append(0.1)
            
            # Factor 2: Weighted imbalance alignment
            if (imbalance > 0 and weighted_imbalance > 0) or (imbalance < 0 and weighted_imbalance < 0):
                factors.append(0.25)
            else:
                factors.append(0.1)
            
            # Factor 3: Near-touch pressure
            if abs(near_touch_imbalance) > 15:
                factors.append(0.2)
            else:
                factors.append(0.1)
            
            # Factor 4: Momentum alignment
            if (imbalance > 0 and momentum > 0) or (imbalance < 0 and momentum < 0):
                factors.append(0.25)
            else:
                factors.append(0.1)
            
            # Calculate overall confidence
            confidence = sum(factors)
            
            # Determine direction
            if imbalance > 5 and weighted_imbalance > 0:
                direction = "BULLISH"
                strength = min(100, abs(imbalance) * 2)
            elif imbalance < -5 and weighted_imbalance < 0:
                direction = "BEARISH"
                strength = min(100, abs(imbalance) * 2)
            else:
                direction = "NEUTRAL"
                strength = 0
            
            # Time horizon prediction
            if abs(momentum) > 2:
                time_horizon = "SHORT_TERM"  # 1-5 minutes
            elif abs(imbalance) > 15:
                time_horizon = "MEDIUM_TERM"  # 5-15 minutes
            else:
                time_horizon = "LONG_TERM"  # 15+ minutes
            
            return {
                'direction': direction,
                'confidence': confidence * 100,
                'strength': strength,
                'time_horizon': time_horizon,
                'factors_aligned': len([f for f in factors if f > 0.15]),
                'prediction_quality': 'HIGH' if confidence > 0.8 else 'MEDIUM' if confidence > 0.6 else 'LOW'
            }
            
        except Exception as e:
            print(f"Error predicting price direction: {e}")
            return {}
    
    def _analyze_institutional_flow(self, bids: List[Tuple], asks: List[Tuple]) -> Dict:
        """Analyze institutional vs retail order flow patterns"""
        try:
            # Large order detection (institutional)
            large_bid_orders = [bid for bid in bids if bid[0] > 1000]  # >1000 shares
            large_ask_orders = [ask for ask in asks if ask[2] > 1000]
            
            # Medium order detection (retail)
            medium_bid_orders = [bid for bid in bids if 100 <= bid[0] <= 1000]
            medium_ask_orders = [ask for ask in asks if 100 <= ask[2] <= 1000]
            
            # Calculate institutional flow
            institutional_bid_volume = sum(bid[0] for bid in large_bid_orders)
            institutional_ask_volume = sum(ask[2] for ask in large_ask_orders)
            total_institutional = institutional_bid_volume + institutional_ask_volume
            
            institutional_imbalance = 0
            if total_institutional > 0:
                institutional_imbalance = ((institutional_bid_volume - institutional_ask_volume) / total_institutional) * 100
            
            # Calculate retail flow
            retail_bid_volume = sum(bid[0] for bid in medium_bid_orders)
            retail_ask_volume = sum(ask[2] for ask in medium_ask_orders)
            total_retail = retail_bid_volume + retail_ask_volume
            
            retail_imbalance = 0
            if total_retail > 0:
                retail_imbalance = ((retail_bid_volume - retail_ask_volume) / total_retail) * 100
            
            # Detect flow divergence
            divergence = abs(institutional_imbalance - retail_imbalance)
            
            return {
                'institutional_imbalance': institutional_imbalance,
                'retail_imbalance': retail_imbalance,
                'flow_divergence': divergence,
                'institutional_volume': total_institutional,
                'retail_volume': total_retail,
                'large_orders': {
                    'bid_count': len(large_bid_orders),
                    'ask_count': len(large_ask_orders),
                    'total_volume': total_institutional
                },
                'dominant_flow': 'INSTITUTIONAL' if total_institutional > total_retail else 'RETAIL',
                'flow_alignment': 'ALIGNED' if divergence < 10 else 'DIVERGENT'
            }
            
        except Exception as e:
            print(f"Error analyzing institutional flow: {e}")
            return {}
    
    def _generate_flow_signals(self, basic_imbalance: Dict, flow_momentum: Dict, 
                              institutional_analysis: Dict, current_price: float) -> List[TradingSignal]:
        """Generate trading signals based on order flow analysis"""
        try:
            signals = []
            
            # Get key metrics
            imbalance = basic_imbalance.get('quantity_imbalance', 0)
            momentum_value = flow_momentum.get('momentum', 0)
            inst_imbalance = institutional_analysis.get('institutional_imbalance', 0)
            
            # Strong imbalance signal
            if abs(imbalance) > 25:
                signal = TradingSignal(
                    signal_id=f"FLOW_IMBALANCE_{datetime.now().strftime('%H%M%S')}",
                    timestamp=datetime.now(),
                    signal_type='STRONG_IMBALANCE',
                    direction='BUY' if imbalance > 0 else 'SELL',
                    confidence=min(95, abs(imbalance) * 2),
                    strength=abs(imbalance),
                    timeframe='1-5 minutes',
                    entry_price=current_price,
                    target_price=current_price * (1.005 if imbalance > 0 else 0.995),
                    stop_loss_price=current_price * (0.997 if imbalance > 0 else 1.003),
                    description=f"Strong {'buying' if imbalance > 0 else 'selling'} pressure detected",
                    factors={'imbalance': imbalance, 'momentum': momentum_value}
                )
                signals.append(signal)
            
            # Momentum signal
            if abs(momentum_value) > 3:
                signal = TradingSignal(
                    signal_id=f"MOMENTUM_{datetime.now().strftime('%H%M%S')}",
                    timestamp=datetime.now(),
                    signal_type='MOMENTUM',
                    direction='BUY' if momentum_value > 0 else 'SELL',
                    confidence=min(85, abs(momentum_value) * 15),
                    strength=abs(momentum_value),
                    timeframe='2-10 minutes',
                    entry_price=current_price,
                    target_price=current_price * (1.006 if momentum_value > 0 else 0.994),
                    stop_loss_price=current_price * (0.996 if momentum_value > 0 else 1.004),
                    description=f"Strong momentum {'building' if momentum_value > 0 else 'declining'}",
                    factors={'momentum': momentum_value, 'imbalance': imbalance}
                )
                signals.append(signal)
            
            # Institutional flow signal
            if abs(inst_imbalance) > 20 and institutional_analysis.get('institutional_volume', 0) > 5000:
                signal = TradingSignal(
                    signal_id=f"INSTITUTIONAL_{datetime.now().strftime('%H%M%S')}",
                    timestamp=datetime.now(),
                    signal_type='INSTITUTIONAL_FLOW',
                    direction='BUY' if inst_imbalance > 0 else 'SELL',
                    confidence=min(90, abs(inst_imbalance) * 2),
                    strength=abs(inst_imbalance),
                    timeframe='5-30 minutes',
                    entry_price=current_price,
                    target_price=current_price * (1.008 if inst_imbalance > 0 else 0.992),
                    stop_loss_price=current_price * (0.996 if inst_imbalance > 0 else 1.004),
                    description=f"Institutional {'buying' if inst_imbalance > 0 else 'selling'} detected",
                    factors={'institutional_imbalance': inst_imbalance}
                )
                signals.append(signal)
            
            return signals
            
        except Exception as e:
            print(f"Error generating flow signals: {e}")
            return []
    
    def _classify_imbalance(self, imbalance: float) -> str:
        """Classify imbalance strength"""
        if imbalance >= self.thresholds['strong_bullish']:
            return 'STRONG_BULLISH'
        elif imbalance >= self.thresholds['bullish']:
            return 'BULLISH'
        elif imbalance >= self.thresholds['neutral_high']:
            return 'WEAK_BULLISH'
        elif imbalance >= self.thresholds['neutral_low']:
            return 'NEUTRAL'
        elif imbalance >= self.thresholds['bearish']:
            return 'WEAK_BEARISH'
        elif imbalance >= self.thresholds['strong_bearish']:
            return 'BEARISH'
        else:
            return 'STRONG_BEARISH'
    
    def get_current_flow_summary(self) -> Dict:
        """Get current order flow summary"""
        try:
            if not self.imbalance_history:
                return {}
            
            latest = self.imbalance_history[-1]
            
            return {
                'timestamp': latest.timestamp,
                'basic_imbalance': latest.basic_imbalance.get('quantity_imbalance', 0),
                'classification': latest.basic_imbalance.get('classification', 'UNKNOWN'),
                'prediction': latest.prediction,
                'active_signals': len(latest.signals),
                'institutional_flow': latest.institutional_analysis.get('institutional_imbalance', 0),
                'momentum': latest.flow_momentum.get('momentum', 0),
                'trend': latest.flow_momentum.get('trend', 'UNKNOWN')
            }
            
        except Exception as e:
            print(f"Error getting flow summary: {e}")
            return {}
