"""
WebSocket Connection and Order Flow Engine Test
==============================================

This script tests the Smart API WebSocket connection and validates
the order flow engine with real market data.
"""

import asyncio
import json
import time
import websocket
import threading
from datetime import datetime
from typing import Dict, List, Optional
import logging

from order_flow_engine import Order<PERSON>lowEngine, AdvancedOrderFlowAnalyzer, Tick, OrderBook, OrderBookLevel
from order_flow_monitor import OrderFlowMonitor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SmartAPIWebSocketTester:
    """
    Test Smart API WebSocket connection and order flow integration
    """
    
    def __init__(self, api_key: str, access_token: str, symbols: List[str]):
        self.api_key = api_key
        self.access_token = access_token
        self.symbols = symbols
        
        # Connection state
        self.ws = None
        self.is_connected = False
        self.connection_start_time = None
        self.last_heartbeat = None
        
        # Data tracking
        self.tick_count = 0
        self.order_book_count = 0
        self.error_count = 0
        self.connection_attempts = 0
        
        # Order flow components
        self.order_flow_monitor = OrderFlowMonitor(symbols)
        self.analyzers = {symbol: AdvancedOrderFlowAnalyzer(symbol) for symbol in symbols}
        
        # Test results
        self.test_results = {
            'connection_successful': False,
            'data_received': False,
            'order_flow_working': False,
            'signals_generated': 0,
            'latency_ms': [],
            'errors': []
        }
    
    def test_connection(self) -> Dict:
        """Test WebSocket connection to Smart API"""
        logger.info("🔌 Testing Smart API WebSocket Connection...")
        
        try:
            # Smart API WebSocket URL (Angel One)
            ws_url = "wss://smartapisocket.angelone.in/smart-stream"
            
            self.connection_start_time = time.time()
            self.connection_attempts += 1
            
            # Create WebSocket connection
            self.ws = websocket.WebSocketApp(
                ws_url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )
            
            # Start connection in separate thread
            connection_thread = threading.Thread(target=self.ws.run_forever)
            connection_thread.daemon = True
            connection_thread.start()
            
            # Wait for connection or timeout
            timeout = 30  # 30 seconds
            start_wait = time.time()
            
            while not self.is_connected and (time.time() - start_wait) < timeout:
                time.sleep(0.1)
            
            if self.is_connected:
                logger.info("✅ WebSocket connection successful!")
                self.test_results['connection_successful'] = True
                return self._run_data_tests()
            else:
                logger.error("❌ WebSocket connection failed (timeout)")
                self.test_results['errors'].append("Connection timeout")
                return self.test_results
                
        except Exception as e:
            logger.error(f"❌ Connection test failed: {e}")
            self.test_results['errors'].append(f"Connection error: {e}")
            return self.test_results
    
    def _on_open(self, ws):
        """WebSocket opened"""
        connection_time = time.time() - self.connection_start_time
        logger.info(f"🔗 WebSocket opened in {connection_time:.2f}s")
        
        self.is_connected = True
        self.last_heartbeat = time.time()
        
        # Send authentication
        try:
            auth_message = {
                "a": "auth",
                "user": self.api_key,
                "token": self.access_token
            }
            ws.send(json.dumps(auth_message))
            logger.info("🔐 Authentication sent")
            
            # Subscribe to test symbols
            self._subscribe_to_symbols()
            
        except Exception as e:
            logger.error(f"❌ Authentication failed: {e}")
            self.test_results['errors'].append(f"Auth error: {e}")
    
    def _on_message(self, ws, message):
        """Process WebSocket message"""
        try:
            message_start_time = time.time()
            self.last_heartbeat = time.time()
            
            # Try to parse as JSON first
            try:
                data = json.loads(message)
                self._process_json_message(data, message_start_time)
            except json.JSONDecodeError:
                # Binary data
                self._process_binary_message(message, message_start_time)
                
        except Exception as e:
            self.error_count += 1
            logger.error(f"❌ Message processing error: {e}")
            self.test_results['errors'].append(f"Message error: {e}")
    
    def _on_error(self, ws, error):
        """WebSocket error"""
        self.error_count += 1
        logger.error(f"❌ WebSocket error: {error}")
        self.test_results['errors'].append(f"WebSocket error: {error}")
    
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket closed"""
        logger.warning(f"🔌 WebSocket closed: {close_status_code} - {close_msg}")
        self.is_connected = False
    
    def _subscribe_to_symbols(self):
        """Subscribe to symbol data feeds"""
        try:
            for symbol in self.symbols:
                # Subscribe to tick data (mode 1)
                tick_sub = {
                    "a": "subscribe",
                    "v": [[1, symbol]]
                }
                self.ws.send(json.dumps(tick_sub))
                
                # Subscribe to depth data (mode 2) 
                depth_sub = {
                    "a": "subscribe",
                    "v": [[2, symbol]]
                }
                self.ws.send(json.dumps(depth_sub))
                
            logger.info(f"📡 Subscribed to {len(self.symbols)} symbols")
            
        except Exception as e:
            logger.error(f"❌ Subscription failed: {e}")
            self.test_results['errors'].append(f"Subscription error: {e}")
    
    def _process_json_message(self, data: Dict, start_time: float):
        """Process JSON message"""
        message_type = data.get('t', '')
        
        if message_type == 'ck':
            logger.info("✅ Connection acknowledged")
        elif message_type == 'tk':
            self._process_tick_data(data, start_time)
        elif message_type == 'dp':
            self._process_depth_data(data, start_time)
        else:
            logger.debug(f"📨 Unknown message type: {message_type}")
    
    def _process_binary_message(self, data: bytes, start_time: float):
        """Process binary message (tick data)"""
        try:
            # This is a simplified binary parser
            # Real Smart API binary format would be more complex
            if len(data) >= 20:
                self.tick_count += 1
                self.test_results['data_received'] = True
                
                # Calculate latency
                latency_ms = (time.time() - start_time) * 1000
                self.test_results['latency_ms'].append(latency_ms)
                
                logger.info(f"📊 Binary tick #{self.tick_count} (latency: {latency_ms:.1f}ms)")
                
        except Exception as e:
            logger.error(f"❌ Binary processing error: {e}")
    
    def _process_tick_data(self, data: Dict, start_time: float):
        """Process tick data and feed to order flow engine"""
        try:
            symbol = data.get('tk', 'UNKNOWN')
            price = float(data.get('lp', 0))
            volume = int(data.get('v', 0))
            
            if price > 0 and volume > 0:
                self.tick_count += 1
                self.test_results['data_received'] = True
                
                # Calculate latency
                latency_ms = (time.time() - start_time) * 1000
                self.test_results['latency_ms'].append(latency_ms)
                
                # Create tick object
                tick = Tick(
                    timestamp=datetime.now(),
                    price=price,
                    volume=volume,
                    buyer_initiated=True  # Simplified for testing
                )
                
                # Feed to order flow engine
                if symbol in self.analyzers:
                    signal = self.analyzers[symbol].flow_engine.add_tick(tick)
                    
                    if signal:
                        self.test_results['signals_generated'] += 1
                        self.test_results['order_flow_working'] = True
                        logger.info(f"🚨 SIGNAL: {symbol} - {signal.signal_type} "
                                  f"(Strength: {signal.strength:.2f})")
                
                logger.info(f"📈 {symbol}: ₹{price} (Vol: {volume:,}) "
                          f"[Tick #{self.tick_count}, Latency: {latency_ms:.1f}ms]")
                
        except Exception as e:
            logger.error(f"❌ Tick processing error: {e}")
    
    def _process_depth_data(self, data: Dict, start_time: float):
        """Process order book depth data"""
        try:
            symbol = data.get('tk', 'UNKNOWN')
            
            # Extract bid/ask levels
            bids = []
            asks = []
            
            for i in range(5):  # Top 5 levels
                bid_price = data.get(f'bp{i+1}', 0)
                bid_qty = data.get(f'bq{i+1}', 0)
                if bid_price > 0:
                    bids.append(OrderBookLevel(
                        price=float(bid_price),
                        quantity=int(bid_qty),
                        orders=1
                    ))
                
                ask_price = data.get(f'sp{i+1}', 0)
                ask_qty = data.get(f'sq{i+1}', 0)
                if ask_price > 0:
                    asks.append(OrderBookLevel(
                        price=float(ask_price),
                        quantity=int(ask_qty),
                        orders=1
                    ))
            
            if bids and asks:
                self.order_book_count += 1
                
                # Create order book
                order_book = OrderBook(
                    timestamp=datetime.now(),
                    bids=bids,
                    asks=asks
                )
                
                # Feed to order flow engine
                if symbol in self.analyzers:
                    self.analyzers[symbol].flow_engine.add_order_book(order_book)
                
                latency_ms = (time.time() - start_time) * 1000
                logger.info(f"📚 {symbol} Order Book #{self.order_book_count} "
                          f"(Bid: ₹{bids[0].price}, Ask: ₹{asks[0].price}) "
                          f"[Latency: {latency_ms:.1f}ms]")
                
        except Exception as e:
            logger.error(f"❌ Depth processing error: {e}")
    
    def _run_data_tests(self) -> Dict:
        """Run data reception and order flow tests"""
        logger.info("📊 Testing data reception and order flow...")
        
        # Wait for data
        test_duration = 60  # 60 seconds
        start_time = time.time()
        
        while (time.time() - start_time) < test_duration:
            time.sleep(1)
            
            # Print progress every 10 seconds
            elapsed = int(time.time() - start_time)
            if elapsed % 10 == 0:
                logger.info(f"⏱️  Test progress: {elapsed}s - "
                          f"Ticks: {self.tick_count}, "
                          f"Order Books: {self.order_book_count}, "
                          f"Signals: {self.test_results['signals_generated']}")
        
        # Disconnect
        if self.ws:
            self.ws.close()
        
        return self._generate_test_report()
    
    def _generate_test_report(self) -> Dict:
        """Generate comprehensive test report"""
        # Calculate statistics
        avg_latency = sum(self.test_results['latency_ms']) / len(self.test_results['latency_ms']) if self.test_results['latency_ms'] else 0
        max_latency = max(self.test_results['latency_ms']) if self.test_results['latency_ms'] else 0
        min_latency = min(self.test_results['latency_ms']) if self.test_results['latency_ms'] else 0
        
        self.test_results.update({
            'total_ticks': self.tick_count,
            'total_order_books': self.order_book_count,
            'total_errors': self.error_count,
            'avg_latency_ms': avg_latency,
            'max_latency_ms': max_latency,
            'min_latency_ms': min_latency,
            'connection_attempts': self.connection_attempts
        })
        
        return self.test_results
    
    def print_test_report(self):
        """Print formatted test report"""
        results = self.test_results
        
        print("\n" + "="*60)
        print("📊 WEBSOCKET & ORDER FLOW TEST REPORT")
        print("="*60)
        
        # Connection Results
        print(f"🔌 Connection Test:")
        print(f"  ✅ Connected: {results['connection_successful']}")
        print(f"  🔄 Attempts: {results['connection_attempts']}")
        
        # Data Reception
        print(f"\n📡 Data Reception:")
        print(f"  ✅ Data Received: {results['data_received']}")
        print(f"  📈 Total Ticks: {results['total_ticks']}")
        print(f"  📚 Order Books: {results['total_order_books']}")
        
        # Latency Analysis
        if results['latency_ms']:
            print(f"\n⚡ Latency Analysis:")
            print(f"  📊 Average: {results['avg_latency_ms']:.1f}ms")
            print(f"  ⬆️  Maximum: {results['max_latency_ms']:.1f}ms")
            print(f"  ⬇️  Minimum: {results['min_latency_ms']:.1f}ms")
        
        # Order Flow Results
        print(f"\n🔍 Order Flow Engine:")
        print(f"  ✅ Working: {results['order_flow_working']}")
        print(f"  🚨 Signals Generated: {results['signals_generated']}")
        
        # Error Summary
        print(f"\n❌ Errors:")
        print(f"  📊 Total Errors: {results['total_errors']}")
        if results['errors']:
            for error in results['errors'][-5:]:  # Show last 5 errors
                print(f"    • {error}")
        
        # Overall Assessment
        print(f"\n🎯 Overall Assessment:")
        if results['connection_successful'] and results['data_received']:
            if results['avg_latency_ms'] < 100:
                print("  🟢 EXCELLENT - Low latency, good data flow")
            elif results['avg_latency_ms'] < 500:
                print("  🟡 GOOD - Acceptable latency")
            else:
                print("  🟠 FAIR - High latency, may affect performance")
        else:
            print("  🔴 POOR - Connection or data issues")
        
        print("="*60)


async def main():
    """Main test function"""
    print("🚀 Smart API WebSocket & Order Flow Test")
    print("="*50)
    
    # Configuration
    API_KEY = input("Enter your Smart API Key: ").strip()
    ACCESS_TOKEN = input("Enter your Access Token: ").strip()
    
    if not API_KEY or not ACCESS_TOKEN:
        print("❌ API credentials required!")
        return
    
    # Test symbols (use actual NSE symbols)
    SYMBOLS = ["RELIANCE", "TCS", "INFY"]
    
    print(f"\n📡 Testing with symbols: {', '.join(SYMBOLS)}")
    print("⏱️  Test duration: 60 seconds")
    print("🔄 Starting test...\n")
    
    # Create tester
    tester = SmartAPIWebSocketTester(API_KEY, ACCESS_TOKEN, SYMBOLS)
    
    try:
        # Run test
        results = tester.test_connection()
        
        # Print results
        tester.print_test_report()
        
        # Recommendations
        print("\n💡 Recommendations:")
        if results['connection_successful']:
            print("  ✅ Connection working - proceed with live trading")
            if results['avg_latency_ms'] > 200:
                print("  ⚠️  Consider optimizing network connection")
        else:
            print("  ❌ Fix connection issues before proceeding")
            print("  🔧 Check API credentials and network connectivity")
        
        if results['order_flow_working']:
            print("  ✅ Order flow engine working correctly")
        else:
            print("  ⚠️  Order flow engine needs more data or time")
        
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
