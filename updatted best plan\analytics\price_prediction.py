"""
30-Minute Price Prediction Engine
"""

import statistics
from collections import deque
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from core.data_structures import OrderBookSnapshot, PricePrediction
from config.settings import MODEL_WEIGHTS


class PricePredictionEngine:
    """30-minute price prediction using order book patterns and flow analysis"""
    
    def __init__(self):
        # Historical data for model training
        self.price_history = deque(maxlen=1800)  # 30 minutes at 1 second intervals
        self.order_book_features = deque(maxlen=1800)
        self.flow_features = deque(maxlen=1800)
        self.prediction_history = deque(maxlen=100)
        
        # Model parameters
        self.model_weights = MODEL_WEIGHTS
        
        # Prediction confidence tracking
        self.prediction_accuracy = deque(maxlen=50)
        self.model_confidence = 0.5
        
        # Pattern recognition
        self.known_patterns = {}
        self.pattern_outcomes = {}
        
    def predict_price_30min(self, current_price: float, snapshot: OrderBookSnapshot, 
                           flow_analysis: Dict, analytics: Dict) -> PricePrediction:
        """Generate 30-minute price prediction"""
        try:
            timestamp = datetime.now()
            
            # Extract features for prediction
            features = self._extract_prediction_features(
                current_price, snapshot, flow_analysis, analytics, timestamp
            )
            
            # Store features for model learning
            self._store_features(features, current_price, timestamp)
            
            # Generate prediction using multiple models
            models = {}
            
            # Model 1: Order Book Imbalance Model
            models['imbalance_model'] = self._imbalance_based_prediction(features, current_price)
            
            # Model 2: Flow Momentum Model
            models['momentum_model'] = self._momentum_based_prediction(features, current_price)
            
            # Model 3: Volume Profile Model
            models['volume_model'] = self._volume_based_prediction(features, current_price)
            
            # Ensemble prediction combining all models
            ensemble_prediction = self._create_ensemble_prediction(models, current_price)
            
            # Calculate confidence metrics
            confidence_metrics = self._calculate_prediction_confidence(models, ensemble_prediction)
            
            # Risk assessment
            risk_assessment = self._assess_prediction_risk(ensemble_prediction, features)
            
            # Create prediction result
            prediction = PricePrediction(
                timestamp=timestamp,
                current_price=current_price,
                target_time=timestamp + timedelta(minutes=30),
                models=models,
                ensemble_prediction=ensemble_prediction,
                confidence_metrics=confidence_metrics,
                risk_assessment=risk_assessment
            )
            
            # Store prediction for accuracy tracking
            self.prediction_history.append(prediction)
            
            return prediction
            
        except Exception as e:
            print(f"Error in 30-minute price prediction: {e}")
            return PricePrediction(
                timestamp=datetime.now(),
                current_price=current_price,
                target_time=datetime.now() + timedelta(minutes=30),
                models={},
                ensemble_prediction={},
                confidence_metrics={},
                risk_assessment={}
            )
    
    def _extract_prediction_features(self, current_price: float, snapshot: OrderBookSnapshot, 
                                   flow_analysis: Dict, analytics: Dict, timestamp: datetime) -> Dict:
        """Extract features for price prediction models"""
        try:
            features = {
                'timestamp': timestamp,
                'current_price': current_price,
                'order_book': {},
                'flow': {},
                'analytics': {},
                'technical': {}
            }
            
            # Order book features
            if snapshot:
                features['order_book'] = {
                    'imbalance': snapshot.imbalance,
                    'spread': snapshot.spread,
                    'total_bid_qty': snapshot.total_bid_quantity,
                    'total_ask_qty': snapshot.total_ask_quantity,
                    'bid_levels': len(snapshot.bids),
                    'ask_levels': len(snapshot.asks)
                }
            
            # Flow analysis features
            if flow_analysis:
                basic_imbalance = flow_analysis.get('basic_imbalance', {})
                flow_momentum = flow_analysis.get('flow_momentum', {})
                institutional = flow_analysis.get('institutional_analysis', {})
                
                features['flow'] = {
                    'quantity_imbalance': basic_imbalance.get('quantity_imbalance', 0),
                    'momentum': flow_momentum.get('momentum', 0),
                    'institutional_imbalance': institutional.get('institutional_imbalance', 0),
                    'flow_trend': flow_momentum.get('trend', 'NEUTRAL')
                }
            
            # Analytics features
            if analytics:
                features['analytics'] = {
                    'liquidity_score': analytics.get('liquidity_score', 50),
                    'price_efficiency': analytics.get('price_efficiency', 50)
                }
            
            # Technical indicators
            features['technical'] = self._calculate_technical_indicators(current_price)
            
            return features
            
        except Exception as e:
            print(f"Error extracting prediction features: {e}")
            return {}
    
    def _calculate_technical_indicators(self, current_price: float) -> Dict:
        """Calculate technical indicators from price history"""
        try:
            if len(self.price_history) < 10:
                return {}
            
            prices = list(self.price_history)
            
            # Simple moving averages
            sma_5 = statistics.mean(prices[-5:]) if len(prices) >= 5 else current_price
            sma_10 = statistics.mean(prices[-10:]) if len(prices) >= 10 else current_price
            
            # Price momentum
            momentum_5 = (current_price - sma_5) / sma_5 * 100 if sma_5 > 0 else 0
            
            # Volatility
            volatility = statistics.stdev(prices[-20:]) if len(prices) >= 20 else 0
            
            return {
                'sma_5': sma_5,
                'sma_10': sma_10,
                'momentum_5': momentum_5,
                'volatility': volatility
            }
            
        except Exception as e:
            print(f"Error calculating technical indicators: {e}")
            return {}
    
    def _imbalance_based_prediction(self, features: Dict, current_price: float) -> Dict:
        """Predict price based on order book imbalance"""
        try:
            order_book = features.get('order_book', {})
            flow = features.get('flow', {})
            
            imbalance = order_book.get('imbalance', 0)
            flow_imbalance = flow.get('quantity_imbalance', 0)
            
            # Combined imbalance
            combined_imbalance = (imbalance * 0.6) + (flow_imbalance * 0.4)
            
            # Predict price change based on imbalance
            if abs(combined_imbalance) > 20:
                price_change_pct = (combined_imbalance / 100) * 1.5
                confidence = 80
            elif abs(combined_imbalance) > 10:
                price_change_pct = (combined_imbalance / 100) * 0.8
                confidence = 65
            else:
                price_change_pct = (combined_imbalance / 100) * 0.3
                confidence = 45
            
            predicted_price = current_price * (1 + price_change_pct / 100)
            
            return {
                'predicted_price': predicted_price,
                'price_change_pct': price_change_pct,
                'confidence': confidence,
                'model_weight': self.model_weights['order_book_imbalance']
            }
            
        except Exception as e:
            print(f"Error in imbalance-based prediction: {e}")
            return {}
    
    def _momentum_based_prediction(self, features: Dict, current_price: float) -> Dict:
        """Predict price based on flow momentum"""
        try:
            flow = features.get('flow', {})
            technical = features.get('technical', {})
            
            flow_momentum = flow.get('momentum', 0)
            price_momentum = technical.get('momentum_5', 0)
            
            # Combined momentum score
            momentum_score = (flow_momentum * 0.7) + (price_momentum * 0.3)
            
            # Predict based on momentum
            if abs(momentum_score) > 3:
                price_change_pct = momentum_score * 0.4
                confidence = 75
            elif abs(momentum_score) > 1:
                price_change_pct = momentum_score * 0.25
                confidence = 60
            else:
                price_change_pct = momentum_score * 0.1
                confidence = 40
            
            predicted_price = current_price * (1 + price_change_pct / 100)
            
            return {
                'predicted_price': predicted_price,
                'price_change_pct': price_change_pct,
                'confidence': confidence,
                'model_weight': self.model_weights['flow_momentum']
            }
            
        except Exception as e:
            print(f"Error in momentum-based prediction: {e}")
            return {}
    
    def _volume_based_prediction(self, features: Dict, current_price: float) -> Dict:
        """Predict price based on volume profile"""
        try:
            order_book = features.get('order_book', {})
            flow = features.get('flow', {})
            
            total_volume = order_book.get('total_bid_qty', 0) + order_book.get('total_ask_qty', 0)
            institutional_imbalance = flow.get('institutional_imbalance', 0)
            
            # Volume-based prediction
            volume_factor = min(2.0, total_volume / 10000)
            institutional_factor = abs(institutional_imbalance) / 100
            
            volume_score = (volume_factor * 0.6) + (institutional_factor * 0.4)
            direction = 1 if institutional_imbalance > 0 else -1 if institutional_imbalance < 0 else 0
            
            price_change_pct = direction * volume_score * 0.6
            confidence = min(80, volume_score * 40)
            
            predicted_price = current_price * (1 + price_change_pct / 100)
            
            return {
                'predicted_price': predicted_price,
                'price_change_pct': price_change_pct,
                'confidence': confidence,
                'model_weight': self.model_weights['volume_profile']
            }
            
        except Exception as e:
            print(f"Error in volume-based prediction: {e}")
            return {}
    
    def _create_ensemble_prediction(self, models: Dict, current_price: float) -> Dict:
        """Create ensemble prediction from all models"""
        try:
            if not models:
                return {}
            
            weighted_predictions = []
            total_weight = 0
            total_confidence = 0
            
            for model_name, model_result in models.items():
                if model_result and 'predicted_price' in model_result:
                    weight = model_result.get('model_weight', 0.2)
                    confidence = model_result.get('confidence', 50)
                    predicted_price = model_result.get('predicted_price', current_price)
                    
                    effective_weight = weight * (confidence / 100)
                    weighted_predictions.append(predicted_price * effective_weight)
                    total_weight += effective_weight
                    total_confidence += confidence
            
            if total_weight == 0:
                return {}
            
            # Calculate ensemble prediction
            ensemble_price = sum(weighted_predictions) / total_weight
            ensemble_change_pct = ((ensemble_price - current_price) / current_price) * 100
            ensemble_confidence = total_confidence / len(models)
            
            return {
                'predicted_price': ensemble_price,
                'price_change_pct': ensemble_change_pct,
                'confidence': ensemble_confidence,
                'models_used': len([m for m in models.values() if m])
            }
            
        except Exception as e:
            print(f"Error creating ensemble prediction: {e}")
            return {}
    
    def _calculate_prediction_confidence(self, models: Dict, ensemble: Dict) -> Dict:
        """Calculate comprehensive confidence metrics"""
        try:
            # Historical accuracy
            historical_accuracy = statistics.mean(self.prediction_accuracy) if self.prediction_accuracy else 50
            
            # Model agreement
            predictions = [m.get('predicted_price', 0) for m in models.values() if m]
            if len(predictions) > 1:
                prediction_std = statistics.stdev(predictions)
                agreement_score = max(0, 100 - prediction_std)
            else:
                agreement_score = 50
            
            # Overall confidence
            ensemble_confidence = ensemble.get('confidence', 50)
            overall_confidence = (ensemble_confidence * 0.5) + (historical_accuracy * 0.3) + (agreement_score * 0.2)
            
            return {
                'overall_confidence': overall_confidence,
                'historical_accuracy': historical_accuracy,
                'model_agreement': agreement_score,
                'confidence_level': 'HIGH' if overall_confidence > 75 else 'MEDIUM' if overall_confidence > 50 else 'LOW'
            }
            
        except Exception as e:
            print(f"Error calculating prediction confidence: {e}")
            return {}
    
    def _assess_prediction_risk(self, ensemble: Dict, features: Dict) -> Dict:
        """Assess risk factors for the prediction"""
        try:
            risk_score = 0
            risk_factors = []
            
            # Low confidence risk
            confidence = ensemble.get('confidence', 50)
            if confidence < 60:
                risk_factors.append("Low prediction confidence")
                risk_score += 20
            
            # Large predicted move risk
            predicted_change = abs(ensemble.get('price_change_pct', 0))
            if predicted_change > 2:
                risk_factors.append("Large predicted price move")
                risk_score += 15
            
            # High volatility risk
            technical = features.get('technical', {})
            volatility = technical.get('volatility', 0)
            if volatility > 1.0:
                risk_factors.append("High market volatility")
                risk_score += 10
            
            risk_level = 'HIGH' if risk_score > 30 else 'MEDIUM' if risk_score > 15 else 'LOW'
            
            return {
                'risk_score': risk_score,
                'risk_level': risk_level,
                'risk_factors': risk_factors
            }
            
        except Exception as e:
            print(f"Error assessing prediction risk: {e}")
            return {}
    
    def _store_features(self, features: Dict, current_price: float, timestamp: datetime):
        """Store features for model learning"""
        try:
            self.price_history.append(current_price)
            self.order_book_features.append(features.get('order_book', {}))
            self.flow_features.append(features.get('flow', {}))
        except Exception as e:
            print(f"Error storing features: {e}")
    
    def get_prediction_summary(self) -> Dict:
        """Get summary of prediction engine performance"""
        try:
            if not self.prediction_history:
                return {}
            
            latest = self.prediction_history[-1]
            
            return {
                'latest_prediction': latest.ensemble_prediction,
                'confidence_metrics': latest.confidence_metrics,
                'risk_assessment': latest.risk_assessment,
                'predictions_made': len(self.prediction_history),
                'data_points': len(self.price_history)
            }
            
        except Exception as e:
            print(f"Error getting prediction summary: {e}")
            return {}
