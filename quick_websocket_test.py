"""
Quick WebSocket Connection Test
==============================

Test basic WebSocket connectivity to Smart API without credentials
"""

import websocket
import threading
import time
import json

def test_websocket_connection():
    """Test basic WebSocket connection"""
    print("🔌 Testing WebSocket Connection to Smart API...")
    
    connection_successful = False
    error_message = None
    
    def on_open(ws):
        nonlocal connection_successful
        print("✅ WebSocket connection opened successfully!")
        connection_successful = True
        # Close immediately after successful connection
        ws.close()
    
    def on_error(ws, error):
        nonlocal error_message
        error_message = str(error)
        print(f"❌ WebSocket error: {error}")
    
    def on_close(ws, close_status_code, close_msg):
        print(f"🔌 WebSocket closed: {close_status_code}")
    
    try:
        # Smart API WebSocket URL
        ws_url = "wss://smartapisocket.angelone.in/smart-stream"
        
        print(f"📡 Connecting to: {ws_url}")
        
        ws = websocket.WebSocketApp(
            ws_url,
            on_open=on_open,
            on_error=on_error,
            on_close=on_close
        )
        
        # Start connection in thread
        connection_thread = threading.Thread(target=ws.run_forever)
        connection_thread.daemon = True
        connection_thread.start()
        
        # Wait for connection or timeout
        timeout = 10  # 10 seconds
        start_time = time.time()
        
        while not connection_successful and (time.time() - start_time) < timeout:
            if error_message:
                break
            time.sleep(0.1)
        
        if connection_successful:
            print("🎯 Result: WebSocket connectivity is working!")
            print("✅ Smart API endpoint is reachable")
            print("📋 Next step: Get API credentials and test with authentication")
        elif error_message:
            print(f"❌ Connection failed: {error_message}")
            print("🔧 Check internet connection and firewall settings")
        else:
            print("⏰ Connection timeout")
            print("🔧 Check network connectivity")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_websocket_connection()
