"""
Component Builder for CGCL Trading Dashboard
Build UI components one by one for better control and organization
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional

# Professional color scheme
COLORS = {
    'bg_primary': '#0a0a0a',           # Deep black background
    'bg_secondary': '#1a1a1a',        # Secondary panels
    'bg_tertiary': '#2a2a2a',         # Card backgrounds
    'bg_accent': '#3a3a3a',           # Accent panels
    'text_primary': '#ffffff',         # Primary text
    'text_secondary': '#b0b0b0',       # Secondary text
    'text_muted': '#808080',           # Muted text
    'accent_blue': '#00d4ff',          # Professional blue
    'accent_green': '#00ff88',         # Success green
    'accent_red': '#ff4757',           # Alert red
    'accent_orange': '#ffa726',        # Warning orange
    'accent_purple': '#9c27b0',        # Analytics purple
    'border_light': '#404040',         # Light borders
    'border_dark': '#2a2a2a',          # Dark borders
}

class ComponentBuilder:
    """Build UI components one by one"""
    
    def __init__(self):
        self.root = None
        self.components = {}
        
    def initialize_window(self):
        """Initialize the main window"""
        self.root = tk.Tk()
        self.root.title("CGCL Trading Dashboard - Component Builder")
        self.root.geometry("1400x900")
        self.root.configure(bg=COLORS['bg_primary'])
        self.root.state('zoomed')  # Maximize window
        
        # Configure professional styling
        style = ttk.Style()
        style.theme_use('clam')
        
        print("✅ Main window initialized")
        return self.root
    
    def create_header_component(self, parent):
        """Create header component with title and key metrics"""
        header_frame = tk.Frame(parent, bg=COLORS['bg_secondary'], height=120)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # Add border
        border_frame = tk.Frame(header_frame, bg=COLORS['border_light'], height=2)
        border_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Title section
        title_section = tk.Frame(header_frame, bg=COLORS['bg_secondary'])
        title_section.pack(side=tk.LEFT, fill=tk.Y, padx=30, pady=20)
        
        title_label = tk.Label(title_section,
                              text="CGCL TRADING ANALYTICS",
                              bg=COLORS['bg_secondary'],
                              fg=COLORS['accent_blue'],
                              font=('Segoe UI', 24, 'bold'))
        title_label.pack(anchor=tk.W)
        
        subtitle_label = tk.Label(title_section,
                                 text="Component Builder • Real-time Market Intelligence",
                                 bg=COLORS['bg_secondary'],
                                 fg=COLORS['text_secondary'],
                                 font=('Segoe UI', 12))
        subtitle_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Key metrics section
        metrics_section = tk.Frame(header_frame, bg=COLORS['bg_secondary'])
        metrics_section.pack(side=tk.RIGHT, fill=tk.Y, padx=30, pady=20)
        
        # Create metric cards
        metrics_grid = tk.Frame(metrics_section, bg=COLORS['bg_secondary'])
        metrics_grid.pack(fill=tk.BOTH, expand=True)
        
        # Current Price
        self.create_metric_card(metrics_grid, "CURRENT PRICE", "₹184.75", 
                               COLORS['accent_green'], 0, 0)
        
        # Change
        self.create_metric_card(metrics_grid, "24H CHANGE", "*****%", 
                               COLORS['accent_green'], 0, 1)
        
        # Volume
        self.create_metric_card(metrics_grid, "VOLUME", "1.2M", 
                               COLORS['accent_blue'], 1, 0)
        
        # Market Cap
        self.create_metric_card(metrics_grid, "MARKET CAP", "₹2.1B", 
                               COLORS['accent_purple'], 1, 1)
        
        # Live status indicator
        status_section = tk.Frame(header_frame, bg=COLORS['bg_secondary'])
        status_section.pack(side=tk.RIGHT, fill=tk.Y, padx=20, pady=20)
        
        self.create_live_status(status_section)
        
        self.components['header'] = header_frame
        print("✅ Header component created")
        return header_frame
    
    def create_metric_card(self, parent, title, value, color, row, col):
        """Create individual metric card"""
        card_frame = tk.Frame(parent, bg=COLORS['bg_tertiary'], 
                             relief=tk.RAISED, bd=1)
        card_frame.grid(row=row, column=col, padx=10, pady=5, sticky='nsew', ipadx=15, ipady=10)
        
        title_label = tk.Label(card_frame, text=title,
                              bg=COLORS['bg_tertiary'],
                              fg=COLORS['text_secondary'],
                              font=('Segoe UI', 9, 'bold'))
        title_label.pack()
        
        value_label = tk.Label(card_frame, text=value,
                              bg=COLORS['bg_tertiary'],
                              fg=color,
                              font=('Segoe UI', 16, 'bold'))
        value_label.pack(pady=(5, 0))
        
        return card_frame
    
    def create_live_status(self, parent):
        """Create live connection status indicator"""
        status_frame = tk.Frame(parent, bg=COLORS['bg_tertiary'],
                               relief=tk.RAISED, bd=1)
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Live indicator
        live_label = tk.Label(status_frame, text="🔴 LIVE",
                             bg=COLORS['bg_tertiary'],
                             fg=COLORS['accent_red'],
                             font=('Segoe UI', 12, 'bold'))
        live_label.pack(pady=(10, 5))
        
        # Connection status
        conn_label = tk.Label(status_frame, text="WebSocket Connected",
                             bg=COLORS['bg_tertiary'],
                             fg=COLORS['text_secondary'],
                             font=('Segoe UI', 9))
        conn_label.pack()
        
        # Last update
        update_label = tk.Label(status_frame, text="Updated: --:--:--",
                               bg=COLORS['bg_tertiary'],
                               fg=COLORS['text_muted'],
                               font=('Segoe UI', 8))
        update_label.pack(pady=(5, 10))
    
    def create_main_content_area(self, parent):
        """Create scrollable main content area for components"""
        # Create canvas and scrollbar for scrollable content
        canvas = tk.Canvas(parent, bg=COLORS['bg_primary'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)

        # Scrollable frame
        scrollable_frame = tk.Frame(canvas, bg=COLORS['bg_primary'])

        # Configure scrolling
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        scrollbar.pack(side="right", fill="y", padx=(0, 5), pady=5)

        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        self.components['main_content'] = scrollable_frame
        self.canvas = canvas  # Store canvas reference for width calculations
        print("✅ Scrollable main content area created")
        return scrollable_frame
    
    def create_full_width_chart(self, parent, title, chart_type, row_height=400):
        """Create a full-width chart component with doubled height"""
        chart_frame = tk.Frame(parent, bg=COLORS['bg_secondary'], height=row_height)
        chart_frame.pack(fill=tk.X, pady=2)  # Minimal vertical spacing
        chart_frame.pack_propagate(False)

        # Title bar
        title_frame = tk.Frame(chart_frame, bg=COLORS['bg_secondary'], height=30)
        title_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text=title,
                              bg=COLORS['bg_secondary'],
                              fg=COLORS['accent_blue'],
                              font=('Segoe UI', 12, 'bold'))
        title_label.pack(side=tk.LEFT, pady=5)

        # Chart area
        chart_area = tk.Frame(chart_frame, bg=COLORS['bg_tertiary'])
        chart_area.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create chart based on type
        if chart_type == "cvd":
            self.create_cvd_chart(chart_area)
        elif chart_type == "volume_profile":
            self.create_volume_profile_chart(chart_area)
        elif chart_type == "footprint":
            self.create_footprint_chart(chart_area)
        elif chart_type == "order_flow":
            self.create_order_flow_chart(chart_area)
        elif chart_type == "order_book":
            self.create_order_book_display(chart_area)

        return chart_frame

    def create_cvd_chart(self, parent):
        """Create Cumulative Volume Delta chart with doubled height"""
        # Calculate width accounting for scrollbar (subtract ~20px for scrollbar)
        fig = Figure(figsize=(18, 5), facecolor=COLORS['bg_tertiary'])
        fig.patch.set_facecolor(COLORS['bg_tertiary'])

        ax = fig.add_subplot(111)
        ax.set_facecolor(COLORS['bg_tertiary'])

        # Generate sample CVD data
        times = np.arange(0, 60, 1)
        price_data = 184.75 + np.cumsum(np.random.randn(60) * 0.02)
        cvd_data = np.cumsum(np.random.randn(60) * 100)

        # Create divergence
        cvd_data[40:] = cvd_data[40] - np.cumsum(np.abs(np.random.randn(20)) * 50)

        # Plot
        ax_twin = ax.twinx()
        ax.plot(times, price_data, color=COLORS['accent_blue'], linewidth=2, label='Price')
        ax_twin.plot(times, cvd_data, color=COLORS['accent_orange'], linewidth=2, label='CVD')

        # Highlight divergence
        ax.axvspan(40, 60, alpha=0.2, color=COLORS['accent_red'])

        # Styling
        ax.set_ylabel('Price (₹)', color=COLORS['text_secondary'], fontsize=10)
        ax_twin.set_ylabel('CVD', color=COLORS['text_secondary'], fontsize=10)
        ax.tick_params(colors=COLORS['text_secondary'], labelsize=9)
        ax_twin.tick_params(colors=COLORS['text_secondary'], labelsize=9)
        ax.grid(True, alpha=0.3, color=COLORS['border_light'])

        # Style spines
        for spine in ax.spines.values():
            spine.set_color(COLORS['border_light'])
        for spine in ax_twin.spines.values():
            spine.set_color(COLORS['border_light'])

        fig.tight_layout()

        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_volume_profile_chart(self, parent):
        """Create Volume Profile chart with doubled height"""
        # Calculate width accounting for scrollbar
        fig = Figure(figsize=(18, 5), facecolor=COLORS['bg_tertiary'])
        fig.patch.set_facecolor(COLORS['bg_tertiary'])

        ax = fig.add_subplot(111)
        ax.set_facecolor(COLORS['bg_tertiary'])

        # Generate volume profile data
        prices = np.linspace(184.00, 185.50, 30)
        volumes = np.random.gamma(2, 500, 30)

        # Create horizontal bar chart
        bars = ax.barh(prices, volumes, color=COLORS['accent_blue'], alpha=0.7, height=0.04)

        # Highlight High Volume Node
        hvn_index = np.argmax(volumes)
        bars[hvn_index].set_color(COLORS['accent_green'])

        # Mark low volume areas
        low_vol_threshold = np.percentile(volumes, 25)
        for i, vol in enumerate(volumes):
            if vol < low_vol_threshold:
                bars[i].set_color(COLORS['accent_red'])
                bars[i].set_alpha(0.5)

        ax.set_xlabel('Volume', color=COLORS['text_secondary'], fontsize=10)
        ax.set_ylabel('Price (₹)', color=COLORS['text_secondary'], fontsize=10)
        ax.tick_params(colors=COLORS['text_secondary'], labelsize=9)
        ax.grid(True, alpha=0.3, color=COLORS['border_light'])

        # Style spines
        for spine in ax.spines.values():
            spine.set_color(COLORS['border_light'])

        fig.tight_layout()

        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_footprint_chart(self, parent):
        """Create Footprint Analysis chart with doubled height"""
        # Calculate width accounting for scrollbar
        fig = Figure(figsize=(18, 5), facecolor=COLORS['bg_tertiary'])
        fig.patch.set_facecolor(COLORS['bg_tertiary'])

        ax = fig.add_subplot(111)
        ax.set_facecolor(COLORS['bg_tertiary'])

        # Generate footprint data
        prices = np.linspace(184.50, 185.00, 10)
        bid_volumes = np.random.randint(100, 1000, 10)
        ask_volumes = np.random.randint(100, 1000, 10)

        # Plot bid and ask volumes
        ax.barh(prices, -bid_volumes, color=COLORS['accent_green'], alpha=0.7, height=0.04, label='Bid Volume')
        ax.barh(prices, ask_volumes, color=COLORS['accent_red'], alpha=0.7, height=0.04, label='Ask Volume')

        # Mark imbalances (300% rule)
        for i in range(len(bid_volumes)):
            if bid_volumes[i] > 0 and ask_volumes[i] > 0:
                ratio = max(bid_volumes[i], ask_volumes[i]) / min(bid_volumes[i], ask_volumes[i])
                if ratio >= 3.0:
                    if bid_volumes[i] > ask_volumes[i]:
                        ax.barh(prices[i], -bid_volumes[i], color=COLORS['accent_green'], alpha=1.0, height=0.04)
                        ax.text(-bid_volumes[i]/2, prices[i], 'IMBAL', ha='center', va='center',
                               fontsize=8, fontweight='bold', color='white')
                    else:
                        ax.barh(prices[i], ask_volumes[i], color=COLORS['accent_red'], alpha=1.0, height=0.04)
                        ax.text(ask_volumes[i]/2, prices[i], 'IMBAL', ha='center', va='center',
                               fontsize=8, fontweight='bold', color='white')

        ax.axvline(x=0, color=COLORS['text_secondary'], linestyle='-', alpha=0.5)
        ax.set_xlabel('Volume (Bid ← | → Ask)', color=COLORS['text_secondary'], fontsize=10)
        ax.set_ylabel('Price (₹)', color=COLORS['text_secondary'], fontsize=10)
        ax.tick_params(colors=COLORS['text_secondary'], labelsize=9)
        ax.legend(fontsize=9, facecolor=COLORS['bg_tertiary'])
        ax.grid(True, alpha=0.3, color=COLORS['border_light'])

        # Style spines
        for spine in ax.spines.values():
            spine.set_color(COLORS['border_light'])

        fig.tight_layout()

        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_order_flow_chart(self, parent):
        """Create Order Flow Exhaustion chart with doubled height"""
        # Calculate width accounting for scrollbar
        fig = Figure(figsize=(18, 5), facecolor=COLORS['bg_tertiary'])
        fig.patch.set_facecolor(COLORS['bg_tertiary'])

        ax = fig.add_subplot(111)
        ax.set_facecolor(COLORS['bg_tertiary'])

        # Generate exhaustion pattern data
        times = np.arange(0, 20, 1)
        buying_exhaustion = np.array([1, 1.2, 1.5, 1.8, 2.0, 1.9, 1.7, 1.5, 1.2, 1.0,
                                     0.8, 0.6, 0.4, 0.2, 0.1, -0.1, -0.3, -0.5, -0.7, -0.9])
        selling_exhaustion = np.array([-1, -1.2, -1.5, -1.8, -2.0, -1.9, -1.7, -1.5, -1.2, -1.0,
                                      -0.8, -0.6, -0.4, -0.2, -0.1, 0.1, 0.3, 0.5, 0.7, 0.9])

        # Plot exhaustion patterns
        ax.plot(times, buying_exhaustion, color=COLORS['accent_green'], linewidth=3,
               label='Buying → Exhaustion', alpha=0.8)
        ax.plot(times, selling_exhaustion, color=COLORS['accent_red'], linewidth=3,
               label='Selling → Exhaustion', alpha=0.8)

        # Mark exhaustion points
        ax.scatter([10], [buying_exhaustion[10]], color=COLORS['accent_orange'], s=100, zorder=5)
        ax.scatter([10], [selling_exhaustion[10]], color=COLORS['accent_orange'], s=100, zorder=5)

        # Add exhaustion zone
        ax.axvspan(8, 12, alpha=0.2, color=COLORS['accent_orange'], label='Exhaustion Zone')

        ax.axhline(y=0, color=COLORS['text_secondary'], linestyle='-', alpha=0.5)
        ax.set_xlabel('Time', color=COLORS['text_secondary'], fontsize=10)
        ax.set_ylabel('Flow Strength', color=COLORS['text_secondary'], fontsize=10)
        ax.tick_params(colors=COLORS['text_secondary'], labelsize=9)
        ax.legend(fontsize=9, facecolor=COLORS['bg_tertiary'])
        ax.grid(True, alpha=0.3, color=COLORS['border_light'])

        # Style spines
        for spine in ax.spines.values():
            spine.set_color(COLORS['border_light'])

        fig.tight_layout()

        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_order_book_display(self, parent):
        """Create Order Book display"""
        # Create scrollable frame for order book
        canvas = tk.Canvas(parent, bg=COLORS['bg_tertiary'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=COLORS['bg_tertiary'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Column headers
        header_frame = tk.Frame(scrollable_frame, bg=COLORS['bg_accent'], height=30)
        header_frame.pack(fill=tk.X, pady=(0, 2))
        header_frame.pack_propagate(False)

        headers = [("Qty", 120), ("Orders", 80), ("Bid", 100), ("Ask", 100), ("Orders", 80), ("Qty", 120)]
        for header, width in headers:
            color = COLORS['accent_green'] if header == "Bid" else COLORS['accent_red'] if header == "Ask" else COLORS['text_secondary']
            label = tk.Label(header_frame, text=header, bg=COLORS['bg_accent'], fg=color,
                           font=('Segoe UI', 10, 'bold'), width=width//8, anchor='center')
            label.pack(side=tk.LEFT, padx=1, fill=tk.Y)

        # Sample order book data
        sample_bids = [(1250, 15, 184.70), (890, 12, 184.65), (1100, 18, 184.60),
                       (750, 8, 184.55), (950, 14, 184.50), (1200, 20, 184.45),
                       (680, 9, 184.40), (1050, 16, 184.35), (920, 11, 184.30), (800, 13, 184.25)]

        sample_asks = [(184.75, 10, 980), (184.80, 16, 1200), (184.85, 12, 850),
                       (184.90, 20, 1400), (184.95, 8, 600), (185.00, 15, 1100),
                       (185.05, 11, 750), (185.10, 18, 1300), (185.15, 9, 650), (185.20, 14, 950)]

        # Create order book rows
        for i in range(10):
            row_frame = tk.Frame(scrollable_frame, bg=COLORS['bg_secondary'], height=25)
            row_frame.pack(fill=tk.X, pady=1)
            row_frame.pack_propagate(False)

            # Bid side
            if i < len(sample_bids):
                qty, orders, price = sample_bids[i]

                tk.Label(row_frame, text=f"{qty:,}", bg=COLORS['bg_secondary'], fg=COLORS['accent_green'],
                        font=('Consolas', 9), width=15, anchor='e').pack(side=tk.LEFT, padx=1)
                tk.Label(row_frame, text=str(orders), bg=COLORS['bg_secondary'], fg=COLORS['text_secondary'],
                        font=('Consolas', 9), width=10, anchor='center').pack(side=tk.LEFT, padx=1)
                tk.Label(row_frame, text=f"₹{price:.2f}", bg=COLORS['bg_secondary'], fg=COLORS['accent_green'],
                        font=('Consolas', 10, 'bold'), width=12, anchor='e').pack(side=tk.LEFT, padx=1)
            else:
                for width in [15, 10, 12]:
                    tk.Label(row_frame, text="", bg=COLORS['bg_secondary'], width=width).pack(side=tk.LEFT, padx=1)

            # Ask side
            if i < len(sample_asks):
                price, orders, qty = sample_asks[i]

                tk.Label(row_frame, text=f"₹{price:.2f}", bg=COLORS['bg_secondary'], fg=COLORS['accent_red'],
                        font=('Consolas', 10, 'bold'), width=12, anchor='w').pack(side=tk.LEFT, padx=1)
                tk.Label(row_frame, text=str(orders), bg=COLORS['bg_secondary'], fg=COLORS['text_secondary'],
                        font=('Consolas', 9), width=10, anchor='center').pack(side=tk.LEFT, padx=1)
                tk.Label(row_frame, text=f"{qty:,}", bg=COLORS['bg_secondary'], fg=COLORS['accent_red'],
                        font=('Consolas', 9), width=15, anchor='w').pack(side=tk.LEFT, padx=1)
            else:
                for width in [12, 10, 15]:
                    tk.Label(row_frame, text="", bg=COLORS['bg_secondary'], width=width).pack(side=tk.LEFT, padx=1)

    def run_component_demo(self):
        """Run demo with all charts filling the black space"""
        if not self.root:
            self.initialize_window()

        # Main container - minimal padding
        main_container = tk.Frame(self.root, bg=COLORS['bg_primary'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create header component
        self.create_header_component(main_container)

        # Create main content area
        content_area = self.create_main_content_area(main_container)

        # Add all charts in rows - each taking full width with doubled height
        print("📊 Adding Order Book Display...")
        self.create_full_width_chart(content_area, "📊 Professional Order Book", "order_book", 500)

        print("📊 Adding CVD Analysis...")
        self.create_full_width_chart(content_area, "📈 Cumulative Volume Delta (CVD) Analysis", "cvd", 400)

        print("📊 Adding Volume Profile...")
        self.create_full_width_chart(content_area, "📊 Volume Profile (Fair Value Analysis)", "volume_profile", 400)

        print("📊 Adding Footprint Analysis...")
        self.create_full_width_chart(content_area, "🦶 Footprint Analysis (Bid/Ask Imbalances)", "footprint", 400)

        print("📊 Adding Order Flow Exhaustion...")
        self.create_full_width_chart(content_area, "🌊 Order Flow Exhaustion Patterns", "order_flow", 400)

        print("✅ All charts added - Full space utilization with scrolling!")
        print("🎯 Each chart: 100% height increase, full width, scrollable UI")
        print("📏 Chart heights: Order Book 500px, Others 400px")
        print("🖱️ Use mouse wheel to scroll through all charts")

        self.root.mainloop()

# Demo function
def run_demo():
    """Run the component builder demo"""
    builder = ComponentBuilder()
    builder.run_component_demo()

if __name__ == "__main__":
    run_demo()
