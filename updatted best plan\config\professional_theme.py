"""
Professional Theme Configuration for CGCL Trading Dashboard
Executive-level styling and color schemes
"""

# Professional Executive Color Palette
EXECUTIVE_COLORS = {
    # Primary Background Colors
    'bg_primary': '#0a0a0a',           # Deep black - main background
    'bg_secondary': '#1a1a1a',        # Dark gray - panels
    'bg_tertiary': '#2a2a2a',         # Medium gray - cards
    'bg_quaternary': '#3a3a3a',       # Light gray - accents
    'bg_hover': '#4a4a4a',            # Hover states
    
    # Text Colors
    'text_primary': '#ffffff',         # Primary white text
    'text_secondary': '#e0e0e0',       # Secondary light gray
    'text_tertiary': '#b0b0b0',        # Tertiary medium gray
    'text_muted': '#808080',           # Muted dark gray
    'text_disabled': '#505050',        # Disabled text
    
    # Accent Colors - Professional Blue Palette
    'accent_primary': '#00d4ff',       # Bright cyan blue
    'accent_secondary': '#0099cc',     # Medium blue
    'accent_tertiary': '#006699',      # Dark blue
    
    # Status Colors
    'success': '#00ff88',              # Bright green
    'success_dark': '#00cc66',         # Dark green
    'warning': '#ffa726',              # Orange
    'warning_dark': '#ff8f00',         # Dark orange
    'danger': '#ff4757',               # Red
    'danger_dark': '#e84142',          # Dark red
    'info': '#9c27b0',                 # Purple
    'info_dark': '#7b1fa2',            # Dark purple
    
    # Trading Colors
    'bid_green': '#00ff88',            # Bid side green
    'bid_green_bg': '#1a4d3a',         # Bid background
    'ask_red': '#ff4757',              # Ask side red
    'ask_red_bg': '#4d1a1a',           # Ask background
    'spread_color': '#ffa726',         # Spread indicator
    'spread_bg': '#4d3d1a',            # Spread background
    
    # Border and Separator Colors
    'border_light': '#404040',         # Light borders
    'border_medium': '#303030',        # Medium borders
    'border_dark': '#202020',          # Dark borders
    'separator': '#2a2a2a',            # Separators
    
    # Chart Colors
    'chart_bg': '#1a1a1a',            # Chart background
    'chart_grid': '#404040',           # Chart grid lines
    'chart_axis': '#606060',           # Chart axis
    'chart_text': '#b0b0b0',          # Chart text
    
    # Gradient Colors
    'gradient_start': '#2a2a2a',       # Gradient start
    'gradient_end': '#1a1a1a',         # Gradient end
}

# Professional Typography
EXECUTIVE_FONTS = {
    'primary_family': 'Segoe UI',      # Primary font family
    'secondary_family': 'Arial',       # Secondary font family
    'monospace_family': 'Consolas',    # Monospace for numbers
    
    # Font Sizes
    'title_large': 24,                 # Large titles
    'title_medium': 18,                # Medium titles
    'title_small': 16,                 # Small titles
    'subtitle': 14,                    # Subtitles
    'body_large': 12,                  # Large body text
    'body_medium': 11,                 # Medium body text
    'body_small': 10,                  # Small body text
    'caption': 9,                      # Caption text
    'micro': 8,                        # Micro text
    
    # Font Weights
    'weight_light': 'normal',          # Light weight
    'weight_normal': 'normal',         # Normal weight
    'weight_bold': 'bold',             # Bold weight
}

# Professional Layout Configuration
EXECUTIVE_LAYOUT = {
    # Spacing
    'padding_large': 20,               # Large padding
    'padding_medium': 15,              # Medium padding
    'padding_small': 10,               # Small padding
    'padding_micro': 5,                # Micro padding
    
    # Margins
    'margin_large': 20,                # Large margins
    'margin_medium': 15,               # Medium margins
    'margin_small': 10,                # Small margins
    'margin_micro': 5,                 # Micro margins
    
    # Border Radius
    'radius_large': 12,                # Large radius
    'radius_medium': 8,                # Medium radius
    'radius_small': 4,                 # Small radius
    
    # Border Widths
    'border_thick': 3,                 # Thick borders
    'border_medium': 2,                # Medium borders
    'border_thin': 1,                  # Thin borders
    
    # Shadow
    'shadow_offset': 2,                # Shadow offset
    'shadow_blur': 4,                  # Shadow blur
    
    # Animation
    'animation_fast': 150,             # Fast animations (ms)
    'animation_medium': 300,           # Medium animations (ms)
    'animation_slow': 500,             # Slow animations (ms)
}

# Professional Component Styles
EXECUTIVE_COMPONENTS = {
    # Cards
    'card': {
        'bg': EXECUTIVE_COLORS['bg_tertiary'],
        'border': EXECUTIVE_COLORS['border_light'],
        'border_width': EXECUTIVE_LAYOUT['border_medium'],
        'padding': EXECUTIVE_LAYOUT['padding_medium'],
        'radius': EXECUTIVE_LAYOUT['radius_medium'],
    },
    
    # Buttons
    'button_primary': {
        'bg': EXECUTIVE_COLORS['accent_primary'],
        'fg': EXECUTIVE_COLORS['text_primary'],
        'bg_hover': EXECUTIVE_COLORS['accent_secondary'],
        'border': EXECUTIVE_COLORS['accent_primary'],
        'padding_x': 20,
        'padding_y': 8,
        'font_size': EXECUTIVE_FONTS['body_medium'],
        'font_weight': EXECUTIVE_FONTS['weight_bold'],
    },
    
    'button_success': {
        'bg': EXECUTIVE_COLORS['success'],
        'fg': EXECUTIVE_COLORS['text_primary'],
        'bg_hover': EXECUTIVE_COLORS['success_dark'],
        'border': EXECUTIVE_COLORS['success'],
        'padding_x': 20,
        'padding_y': 8,
        'font_size': EXECUTIVE_FONTS['body_medium'],
        'font_weight': EXECUTIVE_FONTS['weight_bold'],
    },
    
    'button_danger': {
        'bg': EXECUTIVE_COLORS['danger'],
        'fg': EXECUTIVE_COLORS['text_primary'],
        'bg_hover': EXECUTIVE_COLORS['danger_dark'],
        'border': EXECUTIVE_COLORS['danger'],
        'padding_x': 20,
        'padding_y': 8,
        'font_size': EXECUTIVE_FONTS['body_medium'],
        'font_weight': EXECUTIVE_FONTS['weight_bold'],
    },
    
    'button_secondary': {
        'bg': EXECUTIVE_COLORS['bg_quaternary'],
        'fg': EXECUTIVE_COLORS['text_primary'],
        'bg_hover': EXECUTIVE_COLORS['bg_hover'],
        'border': EXECUTIVE_COLORS['border_light'],
        'padding_x': 20,
        'padding_y': 8,
        'font_size': EXECUTIVE_FONTS['body_medium'],
        'font_weight': EXECUTIVE_FONTS['weight_normal'],
    },
    
    # Labels
    'label_title': {
        'fg': EXECUTIVE_COLORS['text_primary'],
        'font_family': EXECUTIVE_FONTS['primary_family'],
        'font_size': EXECUTIVE_FONTS['title_medium'],
        'font_weight': EXECUTIVE_FONTS['weight_bold'],
    },
    
    'label_subtitle': {
        'fg': EXECUTIVE_COLORS['text_secondary'],
        'font_family': EXECUTIVE_FONTS['primary_family'],
        'font_size': EXECUTIVE_FONTS['subtitle'],
        'font_weight': EXECUTIVE_FONTS['weight_normal'],
    },
    
    'label_body': {
        'fg': EXECUTIVE_COLORS['text_tertiary'],
        'font_family': EXECUTIVE_FONTS['primary_family'],
        'font_size': EXECUTIVE_FONTS['body_medium'],
        'font_weight': EXECUTIVE_FONTS['weight_normal'],
    },
    
    'label_caption': {
        'fg': EXECUTIVE_COLORS['text_muted'],
        'font_family': EXECUTIVE_FONTS['primary_family'],
        'font_size': EXECUTIVE_FONTS['caption'],
        'font_weight': EXECUTIVE_FONTS['weight_normal'],
    },
    
    # Metrics
    'metric_value': {
        'fg': EXECUTIVE_COLORS['accent_primary'],
        'font_family': EXECUTIVE_FONTS['primary_family'],
        'font_size': EXECUTIVE_FONTS['title_large'],
        'font_weight': EXECUTIVE_FONTS['weight_bold'],
    },
    
    'metric_label': {
        'fg': EXECUTIVE_COLORS['text_secondary'],
        'font_family': EXECUTIVE_FONTS['primary_family'],
        'font_size': EXECUTIVE_FONTS['caption'],
        'font_weight': EXECUTIVE_FONTS['weight_bold'],
    },
    
    # Order Book
    'orderbook_header': {
        'bg': EXECUTIVE_COLORS['bg_quaternary'],
        'fg': EXECUTIVE_COLORS['text_primary'],
        'font_family': EXECUTIVE_FONTS['primary_family'],
        'font_size': EXECUTIVE_FONTS['body_small'],
        'font_weight': EXECUTIVE_FONTS['weight_bold'],
    },
    
    'orderbook_bid': {
        'fg': EXECUTIVE_COLORS['bid_green'],
        'font_family': EXECUTIVE_FONTS['monospace_family'],
        'font_size': EXECUTIVE_FONTS['body_small'],
        'font_weight': EXECUTIVE_FONTS['weight_normal'],
    },
    
    'orderbook_ask': {
        'fg': EXECUTIVE_COLORS['ask_red'],
        'font_family': EXECUTIVE_FONTS['monospace_family'],
        'font_size': EXECUTIVE_FONTS['body_small'],
        'font_weight': EXECUTIVE_FONTS['weight_normal'],
    },
    
    'orderbook_neutral': {
        'fg': EXECUTIVE_COLORS['text_tertiary'],
        'font_family': EXECUTIVE_FONTS['monospace_family'],
        'font_size': EXECUTIVE_FONTS['body_small'],
        'font_weight': EXECUTIVE_FONTS['weight_normal'],
    },
}

# Professional Chart Configuration
EXECUTIVE_CHARTS = {
    'figure': {
        'facecolor': EXECUTIVE_COLORS['chart_bg'],
        'edgecolor': EXECUTIVE_COLORS['border_dark'],
        'dpi': 100,
    },
    
    'axes': {
        'facecolor': EXECUTIVE_COLORS['chart_bg'],
        'edgecolor': EXECUTIVE_COLORS['border_light'],
        'linewidth': 1,
        'grid': True,
        'grid_alpha': 0.3,
        'grid_color': EXECUTIVE_COLORS['chart_grid'],
        'grid_linewidth': 0.5,
    },
    
    'text': {
        'color': EXECUTIVE_COLORS['chart_text'],
        'fontfamily': EXECUTIVE_FONTS['primary_family'],
        'fontsize': EXECUTIVE_FONTS['body_small'],
    },
    
    'lines': {
        'price': {
            'color': EXECUTIVE_COLORS['accent_primary'],
            'linewidth': 2,
            'alpha': 1.0,
        },
        'ma_short': {
            'color': EXECUTIVE_COLORS['warning'],
            'linewidth': 1.5,
            'alpha': 0.8,
        },
        'ma_long': {
            'color': EXECUTIVE_COLORS['info'],
            'linewidth': 1.5,
            'alpha': 0.8,
        },
    },
    
    'bars': {
        'volume': {
            'color': EXECUTIVE_COLORS['success'],
            'alpha': 0.6,
            'width': 0.8,
        },
        'bid': {
            'color': EXECUTIVE_COLORS['bid_green'],
            'alpha': 0.7,
        },
        'ask': {
            'color': EXECUTIVE_COLORS['ask_red'],
            'alpha': 0.7,
        },
    },
}

# Professional Window Configuration
EXECUTIVE_WINDOW = {
    'title': 'CGCL Professional Trading Dashboard - Executive Interface',
    'geometry': '1600x1000',
    'min_width': 1200,
    'min_height': 800,
    'bg': EXECUTIVE_COLORS['bg_primary'],
    'state': 'zoomed',  # Start maximized
}

# Export all configurations
PROFESSIONAL_THEME = {
    'colors': EXECUTIVE_COLORS,
    'fonts': EXECUTIVE_FONTS,
    'layout': EXECUTIVE_LAYOUT,
    'components': EXECUTIVE_COMPONENTS,
    'charts': EXECUTIVE_CHARTS,
    'window': EXECUTIVE_WINDOW,
}
