"""
Core Data Structures for Order Book and Market Data
"""

from dataclasses import dataclass
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from enum import Enum
import statistics


@dataclass
class OrderBookLevel:
    """Represents a single level in the order book"""
    price: float
    quantity: int
    orders: int
    timestamp: datetime
    
    def __post_init__(self):
        """Validate data after initialization"""
        if self.price <= 0:
            raise ValueError("Price must be positive")
        if self.quantity < 0:
            raise ValueError("Quantity cannot be negative")
        if self.orders < 0:
            raise ValueError("Orders cannot be negative")


@dataclass
class MarketData:
    """Market data snapshot"""
    symbol: str
    timestamp: datetime
    ltp: float  # Last Traded Price
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: int
    prev_close: float
    
    @property
    def price_change(self) -> float:
        """Calculate price change from previous close"""
        return self.ltp - self.prev_close
    
    @property
    def price_change_pct(self) -> float:
        """Calculate percentage price change"""
        if self.prev_close == 0:
            return 0.0
        return (self.price_change / self.prev_close) * 100


@dataclass
class OrderBookSnapshot:
    """Complete order book snapshot"""
    symbol: str
    timestamp: datetime
    bids: List[OrderBookLevel]
    asks: List[OrderBookLevel]
    market_data: Optional[MarketData] = None
    
    @property
    def best_bid(self) -> Optional[OrderBookLevel]:
        """Get best bid (highest price)"""
        return self.bids[0] if self.bids else None
    
    @property
    def best_ask(self) -> Optional[OrderBookLevel]:
        """Get best ask (lowest price)"""
        return self.asks[0] if self.asks else None
    
    @property
    def spread(self) -> float:
        """Calculate bid-ask spread"""
        if self.best_bid and self.best_ask:
            return self.best_ask.price - self.best_bid.price
        return 0.0
    
    @property
    def mid_price(self) -> float:
        """Calculate mid price"""
        if self.best_bid and self.best_ask:
            return (self.best_bid.price + self.best_ask.price) / 2
        elif self.market_data:
            return self.market_data.ltp
        return 0.0
    
    @property
    def total_bid_quantity(self) -> int:
        """Total quantity on bid side"""
        return sum(level.quantity for level in self.bids)
    
    @property
    def total_ask_quantity(self) -> int:
        """Total quantity on ask side"""
        return sum(level.quantity for level in self.asks)
    
    @property
    def imbalance(self) -> float:
        """Calculate order book imbalance (-100 to +100)"""
        total_qty = self.total_bid_quantity + self.total_ask_quantity
        if total_qty == 0:
            return 0.0
        return ((self.total_bid_quantity - self.total_ask_quantity) / total_qty) * 100


class OrderBookEventType(Enum):
    """Types of order book events"""
    PRICE_CHANGED = "price_changed"
    SPREAD_CHANGED = "spread_changed"
    IMBALANCE_CHANGED = "imbalance_changed"
    VOLUME_SURGE = "volume_surge"
    NEW_LEVEL_ADDED = "new_level_added"
    LEVEL_REMOVED = "level_removed"
    LEVEL_UPDATED = "level_updated"
    CONNECTION_STATUS = "connection_status"
    DATA_QUALITY = "data_quality"


@dataclass
class OrderBookEvent:
    """Order book event data"""
    event_type: OrderBookEventType
    symbol: str
    timestamp: datetime
    data: Dict
    priority: int = 1  # 1=low, 2=medium, 3=high
    
    def __post_init__(self):
        """Generate unique event ID"""
        self.event_id = f"{self.timestamp.strftime('%Y%m%d_%H%M%S_%f')}_{self.event_type.value}"


@dataclass
class PerformanceMetrics:
    """System performance metrics"""
    timestamp: datetime
    latency_ms: float
    update_rate_hz: float
    memory_usage_mb: float
    cpu_usage_percent: float
    data_quality_percent: float
    connection_status: str
    message_count: int
    error_count: int
    
    def is_healthy(self) -> bool:
        """Check if system performance is healthy"""
        return (
            self.latency_ms < 100 and
            self.update_rate_hz >= 1 and
            self.memory_usage_mb < 512 and
            self.cpu_usage_percent < 80 and
            self.data_quality_percent >= 60
        )


@dataclass
class TradingSignal:
    """Trading signal data structure"""
    signal_id: str
    timestamp: datetime
    signal_type: str
    direction: str  # BUY, SELL, HOLD
    confidence: float  # 0-100
    strength: float
    timeframe: str
    entry_price: float
    target_price: float
    stop_loss_price: float
    description: str
    factors: Dict
    
    @property
    def risk_reward_ratio(self) -> float:
        """Calculate risk-reward ratio"""
        if self.direction == "BUY":
            risk = abs(self.entry_price - self.stop_loss_price)
            reward = abs(self.target_price - self.entry_price)
        else:  # SELL
            risk = abs(self.stop_loss_price - self.entry_price)
            reward = abs(self.entry_price - self.target_price)
        
        return reward / risk if risk > 0 else 0.0


@dataclass
class FlowAnalysis:
    """Order flow analysis results"""
    timestamp: datetime
    basic_imbalance: Dict
    volume_weighted_imbalance: Dict
    depth_imbalance: Dict
    flow_momentum: Dict
    prediction: Dict
    institutional_analysis: Dict
    signals: List[TradingSignal]


@dataclass
class PricePrediction:
    """Price prediction results"""
    timestamp: datetime
    current_price: float
    target_time: datetime
    models: Dict
    ensemble_prediction: Dict
    confidence_metrics: Dict
    risk_assessment: Dict


@dataclass
class SupportResistanceLevel:
    """Support or resistance level"""
    price: float
    level_type: str  # SUPPORT, RESISTANCE
    strength: float  # 0-100
    touches: int
    distance_pct: float
    volume: int = 0
    orders: int = 0
    
    def __str__(self):
        return f"{self.level_type} at ₹{self.price:.2f} (Strength: {self.strength:.0f}%)"
