"""
Angel One Style Market Depth System
===================================

Complete system that replicates Angel One's market depth interface
with enhanced features and real-time Smart API integration.
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import json
from datetime import datetime, timedelta
import logging

from advanced_market_depth_gui import AdvancedMarketDepthGUI, MarketDepthEngine
from smart_api_realtime import SmartAPIRealTimeConnector

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AngelOneStyleMarketDepth:
    """
    Complete Angel One style market depth system with:
    - Professional market depth display
    - Real-time order flow analysis
    - Enhanced coloring and readability
    - Smart API integration
    - Advanced market microstructure analysis
    """
    
    def __init__(self, symbols=None, credentials_file="smart_api_credentials.json"):
        if symbols is None:
            symbols = ["RELIANCE", "TCS", "INFY", "HDFCBANK"]
        
        self.symbols = symbols
        self.credentials_file = credentials_file
        
        # Market depth engines for each symbol
        self.depth_engines = {symbol: MarketDepthEngine(symbol) for symbol in symbols}
        
        # Smart API connector
        self.api_connector = None
        
        # Advanced GUI
        self.gui = AdvancedMarketDepthGUI(symbols)
        
        # Data tracking
        self.tick_count = 0
        self.depth_updates = 0
        self.start_time = None
        
        # System state
        self.is_running = False
        
        # Performance metrics
        self.performance_metrics = {
            'total_ticks': 0,
            'total_depth_updates': 0,
            'avg_latency_ms': 0,
            'connection_uptime': 0
        }
    
    def initialize_system(self):
        """Initialize the complete system"""
        logger.info("🚀 Initializing Angel One Style Market Depth System...")
        
        try:
            # Check credentials
            try:
                with open(self.credentials_file, 'r') as f:
                    credentials = json.load(f)
                logger.info("✅ Smart API credentials loaded")
            except FileNotFoundError:
                logger.error("❌ Credentials file not found. Run test_smart_api_auth.py first.")
                return False
            
            # Initialize Smart API connector
            self.api_connector = SmartAPIRealTimeConnector(self.credentials_file)
            self.api_connector.set_callbacks(
                on_tick=self.handle_tick_data,
                on_order_book=self.handle_order_book_data
            )
            
            logger.info("✅ System components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            return False
    
    def start_system(self):
        """Start the complete market depth system"""
        if not self.initialize_system():
            return False
        
        logger.info("🚀 Starting Angel One Style Market Depth System...")
        self.is_running = True
        self.start_time = time.time()
        
        try:
            # Start GUI in separate thread
            gui_thread = threading.Thread(target=self.run_gui, daemon=True)
            gui_thread.start()
            
            # Start performance monitoring
            perf_thread = threading.Thread(target=self.monitor_performance, daemon=True)
            perf_thread.start()
            
            # Connect to Smart API
            logger.info("📡 Connecting to Smart API for real-time data...")
            if self.api_connector.connect(self.symbols):
                logger.info("✅ Smart API connected - Starting real-time market depth")
                
                # Start heartbeat
                self.api_connector.start_heartbeat()
                
                # Update GUI status
                self.gui.status_label.config(text="🟢 Live Market Data", fg=self.gui.colors['bid_green'])
                
                # Start main monitoring loop
                self.run_monitoring_loop()
                
            else:
                logger.error("❌ Smart API connection failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ System startup failed: {e}")
            return False
    
    def run_gui(self):
        """Run the GUI"""
        try:
            self.gui.run()
        except Exception as e:
            logger.error(f"GUI error: {e}")
        finally:
            self.stop_system()
    
    def handle_tick_data(self, symbol, tick):
        """Handle incoming tick data from Smart API"""
        try:
            self.tick_count += 1
            self.performance_metrics['total_ticks'] = self.tick_count
            
            # Process through market depth engine
            if symbol in self.depth_engines:
                depth_analysis = self.depth_engines[symbol].add_tick(
                    price=tick.price,
                    volume=tick.volume,
                    timestamp=tick.timestamp,
                    buyer_initiated=tick.buyer_initiated
                )
                
                if depth_analysis:
                    # Update GUI with comprehensive analysis
                    self.update_gui_with_analysis(symbol, depth_analysis)
            
            # Log every 100th tick
            if self.tick_count % 100 == 0:
                logger.info(f"📊 Processed {self.tick_count} ticks, {self.depth_updates} depth updates")
                
        except Exception as e:
            logger.error(f"Tick handling error: {e}")
    
    def handle_order_book_data(self, symbol, order_book):
        """Handle incoming order book data from Smart API"""
        try:
            self.depth_updates += 1
            self.performance_metrics['total_depth_updates'] = self.depth_updates
            
            # Convert order book to required format
            bids = [(level.price, level.quantity, level.orders) for level in order_book.bids]
            asks = [(level.price, level.quantity, level.orders) for level in order_book.asks]
            
            # Process through market depth engine
            if symbol in self.depth_engines:
                enhanced_book = self.depth_engines[symbol].add_order_book(
                    bids=bids,
                    asks=asks,
                    timestamp=order_book.timestamp
                )
                
                if enhanced_book:
                    # Update GUI with enhanced order book
                    self.update_gui_order_book(symbol, enhanced_book)
            
        except Exception as e:
            logger.error(f"Order book handling error: {e}")
    
    def update_gui_with_analysis(self, symbol, depth_analysis):
        """Update GUI with comprehensive market depth analysis"""
        try:
            # Update symbol metrics
            metrics = {
                'current_price': depth_analysis['current_price'],
                'price_change': depth_analysis['current_price'] - depth_analysis['vwap'],
                'price_change_pct': ((depth_analysis['current_price'] - depth_analysis['vwap']) / depth_analysis['vwap']) * 100 if depth_analysis['vwap'] > 0 else 0,
                'volume': depth_analysis['total_volume'],
                'vwap': depth_analysis['vwap'],
                'spread': depth_analysis['spread'],
                'imbalance': depth_analysis['order_flow_imbalance']
            }
            
            self.gui.update_symbol_metrics(symbol, metrics)
            
            # Update analysis section
            analysis_text = {
                'Market Sentiment': depth_analysis['market_sentiment'],
                'Trend': depth_analysis['price_action'].get('trend', 'Sideways'),
                'Volume Profile POC': f"₹{depth_analysis['volume_profile'].get('poc_price', 0):.2f}",
                'Liquidity Imbalance': f"{depth_analysis['liquidity_analysis'].get('liquidity_imbalance', 0):+.1f}%",
                'Support Levels': ', '.join([f"₹{level:.2f}" for level in depth_analysis['support_resistance']['support_levels'][:2]]),
                'Resistance Levels': ', '.join([f"₹{level:.2f}" for level in depth_analysis['support_resistance']['resistance_levels'][:2]]),
                'Price Volatility': f"{depth_analysis['price_action'].get('volatility', 0)*100:.2f}%",
                'Order Flow': 'Bullish' if depth_analysis['order_flow_imbalance'] > 10 else 'Bearish' if depth_analysis['order_flow_imbalance'] < -10 else 'Neutral'
            }
            
            self.gui.update_analysis(symbol, analysis_text)
            
        except Exception as e:
            logger.error(f"GUI analysis update error: {e}")
    
    def update_gui_order_book(self, symbol, enhanced_book):
        """Update GUI with enhanced order book data"""
        try:
            # Prepare depth data for GUI
            depth_data = {
                'bids': enhanced_book['bids'],
                'asks': enhanced_book['asks'],
                'timestamp': datetime.now()
            }
            
            # Update market depth display
            self.gui.update_market_depth(symbol, depth_data)
            
            # Log large orders if detected
            if enhanced_book.get('large_orders'):
                for large_order in enhanced_book['large_orders']:
                    logger.info(f"🔍 Large Order Detected: {symbol} - {large_order['side']} "
                               f"₹{large_order['price']:.2f} x {large_order['quantity']:,} "
                               f"({large_order['size_ratio']:.1f}x avg size)")
            
        except Exception as e:
            logger.error(f"GUI order book update error: {e}")
    
    def monitor_performance(self):
        """Monitor system performance"""
        while self.is_running:
            try:
                time.sleep(30)  # Update every 30 seconds
                
                # Calculate performance metrics
                if self.start_time:
                    uptime = time.time() - self.start_time
                    self.performance_metrics['connection_uptime'] = uptime
                
                # Log performance
                logger.info(f"📊 Performance: {self.tick_count} ticks, "
                           f"{self.depth_updates} depth updates, "
                           f"Uptime: {uptime/60:.1f} min")
                
                # Check API connection
                if self.api_connector:
                    api_status = self.api_connector.get_status()
                    if not api_status['connected']:
                        logger.warning("⚠️ API connection lost")
                        self.gui.status_label.config(text="🟡 Connection Lost", fg=self.gui.colors['spread_yellow'])
                
            except Exception as e:
                logger.error(f"Performance monitoring error: {e}")
    
    def run_monitoring_loop(self):
        """Main monitoring loop"""
        logger.info("📡 Starting main monitoring loop...")
        
        try:
            while self.is_running:
                # Check connection status
                if self.api_connector and not self.api_connector.is_connected:
                    logger.warning("⚠️ API connection lost, attempting reconnection...")
                    self.gui.status_label.config(text="🟡 Reconnecting...", fg=self.gui.colors['spread_yellow'])
                    
                    if self.api_connector.connect(self.symbols):
                        logger.info("✅ Reconnected successfully")
                        self.gui.status_label.config(text="🟢 Live Data", fg=self.gui.colors['bid_green'])
                    else:
                        logger.error("❌ Reconnection failed")
                        self.gui.status_label.config(text="🔴 Connection Failed", fg=self.gui.colors['ask_red'])
                
                time.sleep(10)  # Check every 10 seconds
                
        except KeyboardInterrupt:
            logger.info("⏹️ System stopped by user")
        except Exception as e:
            logger.error(f"Monitoring loop error: {e}")
        finally:
            self.stop_system()
    
    def stop_system(self):
        """Stop the system gracefully"""
        logger.info("🛑 Stopping Angel One Style Market Depth System...")
        self.is_running = False
        
        # Disconnect API
        if self.api_connector:
            self.api_connector.disconnect()
        
        # Export session data
        self.export_session_data()
        
        logger.info("✅ System stopped successfully")
    
    def export_session_data(self):
        """Export session data and analysis"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # Export comprehensive analysis for each symbol
            for symbol in self.symbols:
                engine = self.depth_engines[symbol]
                
                analysis_data = {
                    'symbol': symbol,
                    'session_summary': {
                        'total_ticks': len(engine.tick_data),
                        'total_volume': engine.total_volume,
                        'vwap': engine.vwap,
                        'current_price': engine.current_price,
                        'price_levels_analyzed': len(engine.price_levels),
                        'volume_profile': dict(engine.volume_profile),
                        'support_levels': engine.support_levels,
                        'resistance_levels': engine.resistance_levels
                    },
                    'performance_metrics': self.performance_metrics
                }
                
                filename = f"market_depth_analysis_{symbol}_{timestamp}.json"
                with open(filename, 'w') as f:
                    json.dump(analysis_data, f, indent=2, default=str)
                
                logger.info(f"📁 Exported analysis: {filename}")
            
        except Exception as e:
            logger.error(f"Data export error: {e}")


def main():
    """Main function"""
    print("🚀 ANGEL ONE STYLE MARKET DEPTH SYSTEM")
    print("=" * 60)
    print("Features:")
    print("✅ Professional market depth display (Angel One style)")
    print("✅ Enhanced coloring and readability")
    print("✅ Real-time Smart API integration")
    print("✅ Advanced market microstructure analysis")
    print("✅ Order flow and liquidity analysis")
    print("✅ Support/resistance identification")
    print("✅ Volume profile analysis")
    print("✅ Large order detection")
    print("=" * 60)
    
    # Check market hours
    current_time = datetime.now().time()
    market_start = datetime.strptime("09:15", "%H:%M").time()
    market_end = datetime.strptime("15:30", "%H:%M").time()
    
    if not (market_start <= current_time <= market_end):
        print(f"\n⚠️  Note: Currently outside market hours (9:15 AM - 3:30 PM)")
        print(f"Current time: {current_time.strftime('%H:%M')}")
        print("System will work but may receive limited live data")
    
    # Symbols to monitor
    symbols = ["RELIANCE", "TCS", "INFY", "HDFCBANK"]
    
    try:
        # Create and start system
        system = AngelOneStyleMarketDepth(symbols)
        
        print(f"\n📊 Monitoring symbols: {', '.join(symbols)}")
        print("🔄 Starting Angel One style market depth system...")
        print("📱 Professional GUI will open with enhanced market depth")
        print("⏹️  Close GUI window to stop system")
        
        # Start the system
        system.start_system()
        
    except KeyboardInterrupt:
        print("\n⏹️ System stopped by user")
    except Exception as e:
        print(f"\n❌ System failed: {e}")
        logger.error(f"System failure: {e}")


if __name__ == "__main__":
    main()
